/**
 * Workona 集成测试套件
 * 验证 Workona 隔离机制的正确性和性能
 */

import {
  WorkSpace,
  TabIdMapping,
  WorkspaceSession
} from '../types/workspace';
import { WorkonaTabManager } from '../utils/workonaTabManager';
import { WorkspaceSessionManager } from '../utils/workspaceSessionManager';
import { StorageManager } from '../utils/storage';
import { MigrationManager } from '../utils/dataMigration';

/**
 * 测试工具类
 */
class WorkonaTestUtils {
  /**
   * 创建测试工作区
   */
  static createTestWorkspace(id: string, name: string): WorkSpace {
    return {
      id,
      name,
      icon: '🧪',
      color: '#3b82f6',
      websites: [],
      createdAt: Date.now(),
      updatedAt: Date.now(),
      isActive: false,
      order: 0,
      type: 'saved',
      pos: Date.now(),
      state: 'inactive',
      workonaTabIds: [],
      sessionId: `session_${id}`,
      tabOrder: []
    };
  }

  /**
   * 创建测试标签页映射
   */
  static createTestTabMapping(workspaceId: string, chromeId: number): TabIdMapping {
    const workonaId = WorkonaTabManager.generateWorkonaTabId(workspaceId);
    return {
      workonaId,
      chromeId,
      workspaceId,
      isWorkspaceCore: true,
      tabType: 'core',
      createdAt: Date.now(),
      lastSyncAt: Date.now(),
      metadata: {
        source: 'workspace_website'
      }
    };
  }

  /**
   * 创建测试会话
   */
  static createTestSession(workspaceId: string): WorkspaceSession {
    return {
      workspaceId,
      tabs: {},
      tabOrder: [],
      lastActiveAt: Date.now()
    };
  }
}

/**
 * 性能监控器
 */
class PerformanceMonitor {
  private static measurements: Map<string, number> = new Map();

  /**
   * 开始性能测量
   */
  static startMeasurement(name: string): void {
    this.measurements.set(name, performance.now());
  }

  /**
   * 结束性能测量并返回耗时
   */
  static endMeasurement(name: string): number {
    const startTime = this.measurements.get(name);
    if (!startTime) {
      throw new Error(`No measurement started for: ${name}`);
    }

    const endTime = performance.now();
    const duration = endTime - startTime;
    this.measurements.delete(name);
    
    console.log(`⏱️ ${name}: ${duration.toFixed(2)}ms`);
    return duration;
  }

  /**
   * 验证性能指标
   */
  static validatePerformance(name: string, maxDuration: number): boolean {
    const duration = this.endMeasurement(name);
    const passed = duration <= maxDuration;
    
    if (passed) {
      console.log(`✅ 性能测试通过: ${name} (${duration.toFixed(2)}ms ≤ ${maxDuration}ms)`);
    } else {
      console.error(`❌ 性能测试失败: ${name} (${duration.toFixed(2)}ms > ${maxDuration}ms)`);
    }
    
    return passed;
  }
}

/**
 * Workona 集成测试类
 */
export class WorkonaIntegrationTest {
  /**
   * 运行所有测试
   */
  static async runAllTests(): Promise<boolean> {
    console.log('🧪 开始 Workona 集成测试...');
    
    let allTestsPassed = true;
    
    try {
      // 1. 数据结构测试
      allTestsPassed = await this.testDataStructures() && allTestsPassed;
      
      // 2. 标签页ID管理测试
      allTestsPassed = await this.testTabIdManagement() && allTestsPassed;
      
      // 3. 会话隔离测试
      allTestsPassed = await this.testSessionIsolation() && allTestsPassed;
      
      // 4. 数据迁移测试
      allTestsPassed = await this.testDataMigration() && allTestsPassed;
      
      // 5. 性能测试
      allTestsPassed = await this.testPerformance() && allTestsPassed;
      
      // 6. 兼容性测试
      allTestsPassed = await this.testCompatibility() && allTestsPassed;
      
      if (allTestsPassed) {
        console.log('🎉 所有 Workona 集成测试通过！');
      } else {
        console.error('❌ 部分 Workona 集成测试失败');
      }
      
      return allTestsPassed;
    } catch (error) {
      console.error('❌ 测试过程中发生错误:', error);
      return false;
    }
  }

  /**
   * 测试数据结构
   */
  private static async testDataStructures(): Promise<boolean> {
    console.log('📊 测试数据结构...');
    
    try {
      // 测试工作区创建
      const workspace = WorkonaTestUtils.createTestWorkspace('test_ws_1', 'Test Workspace');
      
      // 验证 Workona 字段
      if (!workspace.type || !workspace.pos || !workspace.state) {
        console.error('❌ 工作区缺少 Workona 字段');
        return false;
      }
      
      // 测试标签页映射
      const mapping = WorkonaTestUtils.createTestTabMapping('test_ws_1', 12345);
      
      // 验证 Workona ID 格式
      if (!mapping.workonaId.startsWith('t-test_ws_1-')) {
        console.error('❌ Workona ID 格式不正确');
        return false;
      }
      
      console.log('✅ 数据结构测试通过');
      return true;
    } catch (error) {
      console.error('❌ 数据结构测试失败:', error);
      return false;
    }
  }

  /**
   * 测试标签页ID管理
   */
  private static async testTabIdManagement(): Promise<boolean> {
    console.log('🔗 测试标签页ID管理...');
    
    try {
      const workspaceId = 'test_ws_2';
      const chromeId = 67890;
      
      // 生成 Workona ID
      const workonaId = WorkonaTabManager.generateWorkonaTabId(workspaceId);
      
      // 验证 ID 格式
      if (!workonaId.startsWith(`t-${workspaceId}-`)) {
        console.error('❌ Workona ID 生成格式不正确');
        return false;
      }
      
      // 测试 ID 映射创建
      const mappingResult = await WorkonaTabManager.createTabIdMapping(
        workonaId,
        chromeId,
        workspaceId
      );
      
      if (!mappingResult.success) {
        console.error('❌ 标签页ID映射创建失败:', mappingResult.error);
        return false;
      }
      
      // 测试双向查询
      const chromeIdResult = await WorkonaTabManager.getChromeIdByWorkonaId(workonaId);
      const workonaIdResult = await WorkonaTabManager.getWorkonaIdByChromeId(chromeId);
      
      if (!chromeIdResult.success || chromeIdResult.data !== chromeId) {
        console.error('❌ Chrome ID 查询失败');
        return false;
      }
      
      if (!workonaIdResult.success || workonaIdResult.data !== workonaId) {
        console.error('❌ Workona ID 查询失败');
        return false;
      }
      
      console.log('✅ 标签页ID管理测试通过');
      return true;
    } catch (error) {
      console.error('❌ 标签页ID管理测试失败:', error);
      return false;
    }
  }

  /**
   * 测试会话隔离
   */
  private static async testSessionIsolation(): Promise<boolean> {
    console.log('🔒 测试会话隔离...');
    
    try {
      const workspaceId1 = 'test_ws_3';
      const workspaceId2 = 'test_ws_4';
      
      // 切换到工作区1
      const switch1Result = await WorkspaceSessionManager.switchSession(workspaceId1);
      if (!switch1Result.success) {
        console.error('❌ 会话切换失败:', switch1Result.error);
        return false;
      }
      
      // 验证当前会话
      const currentSession1 = WorkspaceSessionManager.getCurrentSession();
      if (!currentSession1 || currentSession1.workspaceId !== workspaceId1) {
        console.error('❌ 会话隔离失败 - 工作区1');
        return false;
      }
      
      // 切换到工作区2
      const switch2Result = await WorkspaceSessionManager.switchSession(workspaceId2);
      if (!switch2Result.success) {
        console.error('❌ 会话切换失败:', switch2Result.error);
        return false;
      }
      
      // 验证会话隔离
      const currentSession2 = WorkspaceSessionManager.getCurrentSession();
      if (!currentSession2 || currentSession2.workspaceId !== workspaceId2) {
        console.error('❌ 会话隔离失败 - 工作区2');
        return false;
      }
      
      console.log('✅ 会话隔离测试通过');
      return true;
    } catch (error) {
      console.error('❌ 会话隔离测试失败:', error);
      return false;
    }
  }

  /**
   * 测试数据迁移
   */
  private static async testDataMigration(): Promise<boolean> {
    console.log('🔄 测试数据迁移...');
    
    try {
      // 检测数据版本
      const versionResult = await MigrationManager.detectDataVersion();
      if (!versionResult.success) {
        console.error('❌ 数据版本检测失败:', versionResult.error);
        return false;
      }
      
      console.log(`📊 检测到数据版本: ${versionResult.data}`);
      
      // 测试迁移过程（如果需要）
      const migrationResult = await StorageManager.migrateToWorkonaFormat();
      if (!migrationResult.success) {
        console.error('❌ 数据迁移测试失败:', migrationResult.error);
        return false;
      }
      
      console.log('✅ 数据迁移测试通过');
      return true;
    } catch (error) {
      console.error('❌ 数据迁移测试失败:', error);
      return false;
    }
  }

  /**
   * 测试性能指标
   */
  private static async testPerformance(): Promise<boolean> {
    console.log('⚡ 测试性能指标...');
    
    try {
      let allPerformanceTestsPassed = true;
      
      // 测试工作区切换性能（目标 ≤2秒）
      PerformanceMonitor.startMeasurement('workspace_switch');
      await WorkspaceSessionManager.switchSession('test_ws_performance');
      allPerformanceTestsPassed = PerformanceMonitor.validatePerformance('workspace_switch', 2000) && allPerformanceTestsPassed;
      
      // 测试标签页匹配性能（目标 ≤100ms）
      PerformanceMonitor.startMeasurement('tab_matching');
      // 模拟标签页匹配测试
      await new Promise(resolve => setTimeout(resolve, 50)); // 模拟匹配时间
      allPerformanceTestsPassed = PerformanceMonitor.validatePerformance('tab_matching', 100) && allPerformanceTestsPassed;
      
      // 测试数据存储性能（目标 ≤500ms）
      PerformanceMonitor.startMeasurement('data_storage');
      await StorageManager.saveTabIdMappings([]);
      allPerformanceTestsPassed = PerformanceMonitor.validatePerformance('data_storage', 500) && allPerformanceTestsPassed;
      
      if (allPerformanceTestsPassed) {
        console.log('✅ 性能测试通过');
      } else {
        console.error('❌ 部分性能测试失败');
      }
      
      return allPerformanceTestsPassed;
    } catch (error) {
      console.error('❌ 性能测试失败:', error);
      return false;
    }
  }

  /**
   * 测试向后兼容性
   */
  private static async testCompatibility(): Promise<boolean> {
    console.log('🔄 测试向后兼容性...');
    
    try {
      // 测试现有功能是否正常工作
      const workspacesResult = await StorageManager.getWorkspaces();
      if (!workspacesResult.success) {
        console.error('❌ 获取工作区失败:', workspacesResult.error);
        return false;
      }
      
      // 测试设置功能
      const settingsResult = await StorageManager.getSettings();
      if (!settingsResult.success) {
        console.error('❌ 获取设置失败:', settingsResult.error);
        return false;
      }
      
      console.log('✅ 向后兼容性测试通过');
      return true;
    } catch (error) {
      console.error('❌ 向后兼容性测试失败:', error);
      return false;
    }
  }
}
