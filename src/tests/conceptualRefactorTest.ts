/**
 * 概念性重构功能测试
 * 验证工作区管理的核心概念和用户标签页隐藏逻辑
 */

import { WorkonaTabManager } from '../utils/workonaTabManager';
import { WorkspaceUserTabsVisibilityManager } from '../utils/tabs';
import { MigrationManager } from '../utils/dataMigration';

/**
 * 测试概念性重构的核心功能
 */
export class ConceptualRefactorTest {
  
  /**
   * 测试标签页元数据分类
   */
  static async testTabMetadataClassification(): Promise<boolean> {
    try {
      console.log('🧪 测试标签页元数据分类...');
      
      // 模拟创建工作区核心标签页
      const coreTabMapping = await WorkonaTabManager.createTabIdMapping(
        't-workspace1-core123',
        1001,
        'workspace1',
        'website1',
        {
          isWorkspaceCore: true,
          source: 'workspace_website'
        }
      );
      
      if (!coreTabMapping.success) {
        console.error('❌ 创建核心标签页映射失败');
        return false;
      }
      
      // 模拟创建会话临时标签页
      const sessionTabMapping = await WorkonaTabManager.createTabIdMapping(
        't-workspace1-session456',
        1002,
        'workspace1',
        undefined,
        {
          isWorkspaceCore: false,
          source: 'user_opened'
        }
      );
      
      if (!sessionTabMapping.success) {
        console.error('❌ 创建会话标签页映射失败');
        return false;
      }
      
      // 验证元数据
      const coreMetadata = await WorkonaTabManager.getTabMetadata('t-workspace1-core123');
      const sessionMetadata = await WorkonaTabManager.getTabMetadata('t-workspace1-session456');
      
      if (!coreMetadata.success || !sessionMetadata.success) {
        console.error('❌ 获取标签页元数据失败');
        return false;
      }
      
      const coreData = coreMetadata.data!;
      const sessionData = sessionMetadata.data!;
      
      // 验证分类正确性
      if (coreData.isWorkspaceCore !== true || coreData.tabType !== 'core') {
        console.error('❌ 核心标签页分类错误');
        return false;
      }
      
      if (sessionData.isWorkspaceCore !== false || sessionData.tabType !== 'session') {
        console.error('❌ 会话标签页分类错误');
        return false;
      }
      
      console.log('✅ 标签页元数据分类测试通过');
      return true;
    } catch (error) {
      console.error('❌ 标签页元数据分类测试失败:', error);
      return false;
    }
  }
  
  /**
   * 测试用户标签页隐藏逻辑
   */
  static async testUserTabHidingLogic(): Promise<boolean> {
    try {
      console.log('🧪 测试用户标签页隐藏逻辑...');
      
      // 模拟标签页信息
      const coreTab = {
        id: 1001,
        url: 'https://example.com',
        title: '核心标签页',
        favicon: '',
        isPinned: false,
        isActive: true,
        windowId: 1,
        index: 0
      };
      
      const sessionTab = {
        id: 1002,
        url: 'https://temp.com',
        title: '临时标签页',
        favicon: '',
        isPinned: false,
        isActive: false,
        windowId: 1,
        index: 1
      };
      
      const systemTab = {
        id: 1003,
        url: 'chrome://newtab/',
        title: '新标签页',
        favicon: '',
        isPinned: false,
        isActive: false,
        windowId: 1,
        index: 2
      };
      
      // 测试隐藏逻辑
      const coreTabShouldHide = await (GlobalUserTabsVisibilityManager as any).isRealUserTab(coreTab);
      const sessionTabShouldHide = await (GlobalUserTabsVisibilityManager as any).isRealUserTab(sessionTab);
      const systemTabShouldHide = await (GlobalUserTabsVisibilityManager as any).isRealUserTab(systemTab);
      
      // 验证结果
      if (coreTabShouldHide !== false) {
        console.error('❌ 核心标签页不应该被隐藏，但测试结果为可隐藏');
        return false;
      }
      
      if (sessionTabShouldHide !== true) {
        console.error('❌ 会话标签页应该可以被隐藏，但测试结果为不可隐藏');
        return false;
      }
      
      if (systemTabShouldHide !== false) {
        console.error('❌ 系统标签页不应该被隐藏，但测试结果为可隐藏');
        return false;
      }
      
      console.log('✅ 用户标签页隐藏逻辑测试通过');
      return true;
    } catch (error) {
      console.error('❌ 用户标签页隐藏逻辑测试失败:', error);
      return false;
    }
  }
  
  /**
   * 测试数据迁移功能
   */
  static async testDataMigration(): Promise<boolean> {
    try {
      console.log('🧪 测试数据迁移功能...');
      
      // 测试元数据迁移
      const migrationResult = await MigrationManager.migrateTabMappingsMetadata();
      
      if (!migrationResult.success) {
        console.error('❌ 数据迁移失败:', migrationResult.error);
        return false;
      }
      
      console.log(`✅ 数据迁移测试通过，迁移了 ${migrationResult.data} 个映射`);
      return true;
    } catch (error) {
      console.error('❌ 数据迁移测试失败:', error);
      return false;
    }
  }
  
  /**
   * 运行所有测试
   */
  static async runAllTests(): Promise<void> {
    console.log('🚀 开始概念性重构功能测试...');
    
    const tests = [
      { name: '标签页元数据分类', test: this.testTabMetadataClassification },
      { name: '用户标签页隐藏逻辑', test: this.testUserTabHidingLogic },
      { name: '数据迁移功能', test: this.testDataMigration }
    ];
    
    let passedTests = 0;
    
    for (const { name, test } of tests) {
      try {
        const result = await test();
        if (result) {
          passedTests++;
          console.log(`✅ ${name} 测试通过`);
        } else {
          console.log(`❌ ${name} 测试失败`);
        }
      } catch (error) {
        console.log(`❌ ${name} 测试异常:`, error);
      }
    }
    
    console.log(`\n📊 测试结果: ${passedTests}/${tests.length} 个测试通过`);
    
    if (passedTests === tests.length) {
      console.log('🎉 所有概念性重构功能测试通过！');
    } else {
      console.log('⚠️ 部分测试失败，请检查实现');
    }
  }
}

// 如果直接运行此文件，执行所有测试
if (typeof window === 'undefined') {
  ConceptualRefactorTest.runAllTests().catch(console.error);
}
