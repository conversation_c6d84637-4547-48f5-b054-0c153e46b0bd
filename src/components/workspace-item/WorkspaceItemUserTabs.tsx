import React, { useState, useEffect } from 'react';
import { Eye, EyeOff, Loader2 } from 'lucide-react';
import { WorkSpace } from '@/types/workspace';
import { WorkspaceUserTabsVisibilityManager, UserTabsRealTimeMonitor } from '@/utils/tabs';
import { WorkspaceStateSync } from '@/utils/workspaceStateSync';
import { useToast } from '@/components/Toast';
import { ToastErrorHandler } from '@/utils/errorHandler';

// 用户标签页状态接口
interface UserTabsState {
  isHidden: boolean;
  hiddenTabsCount: number;
  loading: boolean;
  error?: string;
}

interface WorkspaceItemUserTabsProps {
  workspace: WorkSpace;
  isActive: boolean;
}

/**
 * 工作区用户标签页管理组件
 * 职责：处理用户标签页的显示/隐藏功能
 * 
 * 🎯 核心职责：
 * 1. 用户标签页状态管理
 * 2. 显示/隐藏切换功能
 * 3. 继续隐藏功能
 * 4. 实时状态同步
 */
export const WorkspaceItemUserTabs: React.FC<WorkspaceItemUserTabsProps> = ({
  workspace,
  isActive
}) => {
  // 用户标签页状态
  const [userTabsState, setUserTabsState] = useState<UserTabsState>({
    isHidden: false,
    hiddenTabsCount: 0,
    loading: false,
  });

  // Toast 错误处理
  const { showError, showSuccess } = useToast();
  const errorHandler = new ToastErrorHandler(showError);

  /**
   * 加载工作区用户标签页状态
   */
  const loadUserTabsState = async () => {
    try {
      setUserTabsState(prev => ({ ...prev, loading: true }));

      // 使用静态导入的工作区用户标签页管理器
      const result = await WorkspaceUserTabsVisibilityManager.getWorkspaceUserTabsState(workspace.id);

      if (result.success) {
        setUserTabsState({
          isHidden: result.data!.isHidden,
          hiddenTabsCount: result.data!.hiddenTabsCount,
          loading: false,
        });
      } else {
        setUserTabsState(prev => ({
          ...prev,
          loading: false,
          error: result.error?.message || '获取用户标签页状态失败',
        }));
      }
    } catch (error) {
      console.error('加载用户标签页状态失败:', error);
      setUserTabsState(prev => ({
        ...prev,
        loading: false,
        error: '加载用户标签页状态失败',
      }));
    }
  };

  /**
   * 切换用户标签页可见性
   */
  const toggleUserTabsVisibility = async () => {
    try {
      setUserTabsState(prev => ({ ...prev, loading: true }));

      // 使用静态导入的 WorkspaceUserTabsVisibilityManager 和 UserTabsRealTimeMonitor
      const result = await WorkspaceUserTabsVisibilityManager.toggleWorkspaceUserTabsVisibility(workspace.id);

      if (result.success) {
        setUserTabsState({
          isHidden: result.data!.isHidden,
          hiddenTabsCount: result.data!.hiddenTabsCount,
          loading: false,
        });

        // 显示成功消息
        const action = result.data!.isHidden ? '隐藏' : '显示';
        showSuccess(`${action}用户标签页成功`);

        // 刷新实时监控
        await UserTabsRealTimeMonitor.refreshMonitoring();
      } else {
        errorHandler.handleError(result.error!, '切换用户标签页可见性失败');
        setUserTabsState(prev => ({ ...prev, loading: false }));
      }
    } catch (error) {
      console.error('切换用户标签页可见性失败:', error);
      errorHandler.handleError(error, '切换用户标签页可见性失败');
      setUserTabsState(prev => ({ ...prev, loading: false }));
    }
  };

  /**
   * 继续隐藏用户标签页
   */
  const continueHideUserTabs = async () => {
    try {
      setUserTabsState(prev => ({ ...prev, loading: true }));

      // 使用静态导入的 WorkspaceUserTabsVisibilityManager 和 UserTabsRealTimeMonitor
      const result = await WorkspaceUserTabsVisibilityManager.continueHideWorkspaceUserTabs(workspace.id);

      if (result.success) {
        setUserTabsState({
          isHidden: result.data!.isHidden,
          hiddenTabsCount: result.data!.hiddenTabsCount,
          loading: false,
        });

        showSuccess('继续隐藏用户标签页成功');

        // 刷新实时监控
        await UserTabsRealTimeMonitor.refreshMonitoring();
      } else {
        errorHandler.handleError(result.error!, '继续隐藏用户标签页失败');
        setUserTabsState(prev => ({ ...prev, loading: false }));
      }
    } catch (error) {
      console.error('继续隐藏用户标签页失败:', error);
      errorHandler.handleError(error, '继续隐藏用户标签页失败');
      setUserTabsState(prev => ({ ...prev, loading: false }));
    }
  };

  // 设置状态监听器
  useEffect(() => {
    const setupStateListener = async () => {
      const handleStateUpdate = (workspaceId: string, eventType: 'switch' | 'userTabsVisibility') => {
        if (workspaceId === workspace.id) {
          console.log(`📊 收到工作区 ${workspace.name} 的实时状态更新: ${eventType}`);
          
          if (eventType === 'userTabsVisibility') {
            // 重新加载用户标签页状态
            loadUserTabsState();
          }
        }
      };

      // 使用静态导入的 WorkspaceStateSync
      const removeListener = WorkspaceStateSync.addStateListener(handleStateUpdate);

      return removeListener;
    };

    setupStateListener().then(removeListener => {
      return () => {
        if (removeListener) {
          removeListener();
        }
      };
    });
  }, [workspace.id, workspace.name]);

  // 初始加载状态
  useEffect(() => {
    loadUserTabsState();
  }, [workspace.id]);

  // 渲染用户标签页控制按钮
  const renderUserTabsButton = () => {
    if (!isActive) return null;

    const { isHidden, hiddenTabsCount, loading } = userTabsState;

    if (loading) {
      return (
        <button
          disabled
          className="flex items-center gap-1 px-2 py-1 text-xs bg-gray-100 text-gray-500 rounded hover:bg-gray-200 transition-colors"
        >
          <Loader2 className="w-3 h-3 animate-spin" />
          <span>处理中...</span>
        </button>
      );
    }

    if (isHidden) {
      return (
        <div className="flex items-center gap-1">
          <button
            onClick={toggleUserTabsVisibility}
            className="flex items-center gap-1 px-2 py-1 text-xs bg-blue-100 text-blue-700 rounded hover:bg-blue-200 transition-colors"
            title="显示用户标签页"
          >
            <Eye className="w-3 h-3" />
            <span>显示用户标签页</span>
          </button>
          <button
            onClick={continueHideUserTabs}
            className="flex items-center gap-1 px-2 py-1 text-xs bg-orange-100 text-orange-700 rounded hover:bg-orange-200 transition-colors"
            title="继续隐藏"
          >
            <EyeOff className="w-3 h-3" />
            <span>继续隐藏</span>
          </button>
          {hiddenTabsCount > 0 && (
            <span className="text-xs text-gray-500">
              ({hiddenTabsCount}个已隐藏)
            </span>
          )}
        </div>
      );
    }

    return (
      <button
        onClick={toggleUserTabsVisibility}
        className="flex items-center gap-1 px-2 py-1 text-xs bg-gray-100 text-gray-700 rounded hover:bg-gray-200 transition-colors"
        title="隐藏用户标签页"
      >
        <EyeOff className="w-3 h-3" />
        <span>隐藏用户标签页</span>
      </button>
    );
  };

  return (
    <div className="flex items-center gap-2">
      {renderUserTabsButton()}
    </div>
  );
};

export default WorkspaceItemUserTabs;
