import React from 'react';
import { CheckSquare } from 'lucide-react';
import { WorkSpace } from '@/types/workspace';
import { useTabOperations } from './WorkspaceItemTabManager';
import { useToast } from '@/components/Toast';
import { ToastErrorHandler } from '@/utils/errorHandler';

interface WorkspaceItemBatchOperationsProps {
  workspace: WorkSpace;
  isActive: boolean;
  batchMode: boolean;
  setBatchMode: (mode: boolean) => void;
  selectedWebsites?: Set<string>;
  onBatchPin?: (websiteIds: string[], isPinned: boolean) => void;
  onBatchDelete?: (websiteIds: string[]) => void;
}

/**
 * 工作区批量操作组件
 * 职责：处理网站的批量操作功能
 * 
 * 🎯 核心职责：
 * 1. 批量选择模式管理
 * 2. 批量固定/取消固定
 * 3. 批量删除操作
 * 4. 选择状态管理
 */
export const WorkspaceItemBatchOperations: React.FC<WorkspaceItemBatchOperationsProps> = ({
  workspace,
  isActive,
  batchMode,
  setBatchMode,
  selectedWebsites = new Set(),
  onBatchPin,
  onBatchDelete
}) => {

  // Toast 错误处理
  const { showError, showSuccess } = useToast();
  const errorHandler = new ToastErrorHandler(showError);

  // 标签页操作钩子
  const { pinWebsiteTabs, unpinWebsiteTabs } = useTabOperations(workspace);

  /**
   * 批量固定网站
   */
  const handleBatchPin = async (websiteIds: string[]) => {
    try {
      console.log(`📌 批量固定网站: ${websiteIds.length} 个`);

      // 如果有外部处理函数，优先使用
      if (onBatchPin) {
        onBatchPin(websiteIds, true);
        return;
      }

      // 内部处理逻辑
      for (const websiteId of websiteIds) {
        // 查找对应的标签页并固定
        const website = workspace.websites.find(w => w.id === websiteId);
        if (website) {
          await pinWebsiteTabs(website);
        }
      }

      showSuccess(`成功固定 ${websiteIds.length} 个网站的标签页`);
    } catch (error) {
      console.error('批量固定失败:', error);
      errorHandler.handle(error, '批量固定失败');
    }
  };

  /**
   * 批量取消固定网站
   */
  const handleBatchUnpin = async (websiteIds: string[]) => {
    try {
      console.log(`📌 批量取消固定网站: ${websiteIds.length} 个`);

      // 如果有外部处理函数，优先使用
      if (onBatchPin) {
        onBatchPin(websiteIds, false);
        return;
      }

      // 内部处理逻辑
      for (const websiteId of websiteIds) {
        // 查找对应的标签页并取消固定
        const website = workspace.websites.find(w => w.id === websiteId);
        if (website) {
          await unpinWebsiteTabs(website);
        }
      }

      showSuccess(`成功取消固定 ${websiteIds.length} 个网站的标签页`);
    } catch (error) {
      console.error('批量取消固定失败:', error);
      errorHandler.handle(error, '批量取消固定失败');
    }
  };

  /**
   * 批量删除网站
   */
  const handleBatchDelete = async (websiteIds: string[]) => {
    try {
      console.log(`🗑️ 批量删除网站: ${websiteIds.length} 个`);

      if (onBatchDelete) {
        onBatchDelete(websiteIds);
        showSuccess(`成功删除 ${websiteIds.length} 个网站`);
      }
    } catch (error) {
      console.error('批量删除失败:', error);
      errorHandler.handle(error, '批量删除失败');
    }
  };

  // 网站选择逻辑由WebsiteList组件处理

  // 退出批量模式由父组件处理

  /**
   * 进入批量模式
   */
  const enterBatchMode = () => {
    setBatchMode(true);
  };

  // 如果不是活跃工作区或没有网站，不显示批量操作
  if (!isActive || workspace.websites.length === 0) {
    return null;
  }

  // 批量模式工具栏 - 只显示操作按钮，选择逻辑由WebsiteList处理
  if (batchMode) {
    const selectedCount = selectedWebsites.size;

    return (
      <div className="flex items-center gap-2 flex-wrap">
        {selectedCount > 0 && (
          <>
            <button
              onClick={() => handleBatchPin(Array.from(selectedWebsites))}
              className="px-2 py-1 text-xs bg-green-100 text-green-700 rounded hover:bg-green-200 transition-colors"
            >
              批量固定
            </button>
            <button
              onClick={() => handleBatchUnpin(Array.from(selectedWebsites))}
              className="px-2 py-1 text-xs bg-yellow-100 text-yellow-700 rounded hover:bg-yellow-200 transition-colors"
            >
              批量取消固定
            </button>
            <button
              onClick={() => handleBatchDelete(Array.from(selectedWebsites))}
              className="px-2 py-1 text-xs bg-red-100 text-red-700 rounded hover:bg-red-200 transition-colors"
            >
              批量删除
            </button>
          </>
        )}
      </div>
    );
  }

  // 进入批量模式按钮
  return (
    <button
      onClick={enterBatchMode}
      className="flex items-center gap-1 px-2 py-1 text-xs bg-gray-100 text-gray-700 rounded hover:bg-gray-200 transition-colors"
      title="批量操作"
    >
      <CheckSquare className="w-3 h-3" />
      <span>批量操作</span>
    </button>
  );
};

export default WorkspaceItemBatchOperations;
