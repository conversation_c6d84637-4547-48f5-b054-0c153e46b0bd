import React, { useState } from 'react';
import { Play, Loader2 } from 'lucide-react';
import { WorkSpace, Website } from '@/types/workspace';
import { WorkonaTabManager } from '@/utils/workonaTabManager';
import { useToast } from '@/components/Toast';
import { ToastErrorHandler } from '@/utils/errorHandler';

interface OpenAllTabsState {
  loading: boolean;
  progress: { current: number; total: number };
}

interface WorkspaceItemTabManagerProps {
  workspace: WorkSpace;
  isActive: boolean;
}

/**
 * 工作区标签页管理组件
 * 职责：处理标签页相关的操作
 * 
 * 🎯 核心职责：
 * 1. 打开全部标签页功能
 * 2. 标签页固定/取消固定
 * 3. 标签页状态检查
 * 4. 批量标签页操作
 */
export const WorkspaceItemTabManager: React.FC<WorkspaceItemTabManagerProps> = ({
  workspace,
  isActive
}) => {
  // 打开全部标签页的状态
  const [openAllTabsState, setOpenAllTabsState] = useState<OpenAllTabsState>({
    loading: false,
    progress: { current: 0, total: 0 }
  });

  // Toast 错误处理
  const { showError, showSuccess } = useToast();
  const errorHandler = new ToastErrorHandler(showError);

  /**
   * 固定网站对应的标签页
   */
  const pinWebsiteTabs = async (website: Website) => {
    try {
      // 通过Workona ID映射查找对应的标签页
      const workonaTabIds = await WorkonaTabManager.getWorkspaceWorkonaTabIds(workspace.id);

      if (!workonaTabIds.success) {
        console.warn('获取工作区Workona标签页ID失败:', workonaTabIds.error);
        return;
      }

      for (const workonaId of workonaTabIds.data || []) {
        try {
          // 检查是否是对应网站的标签页
          const metadataResult = await WorkonaTabManager.getTabMetadata(workonaId);
          if (metadataResult.success && metadataResult.data?.websiteId === website.id) {
            // 获取Chrome标签页ID
            const chromeIdResult = await WorkonaTabManager.getChromeIdByWorkonaId(workonaId);
            if (chromeIdResult.success && chromeIdResult.data) {
              const tab = await chrome.tabs.get(chromeIdResult.data);
              if (tab && !tab.pinned) {
                await chrome.tabs.update(tab.id!, { pinned: true });
                console.log(`📌 固定标签页: ${tab.title}`);
              }
            }
          }
        } catch (error) {
          console.warn(`处理Workona标签页 ${workonaId} 时出错:`, error);
        }
      }
    } catch (error) {
      console.error('固定网站标签页失败:', error);
    }
  };

  /**
   * 取消固定网站对应的标签页
   */
  const unpinWebsiteTabs = async (website: Website) => {
    try {
      // 通过Workona ID映射查找对应的标签页
      const workonaTabIds = await WorkonaTabManager.getWorkspaceWorkonaTabIds(workspace.id);

      if (!workonaTabIds.success) {
        console.warn('获取工作区Workona标签页ID失败:', workonaTabIds.error);
        return;
      }

      for (const workonaId of workonaTabIds.data || []) {
        try {
          // 检查是否是对应网站的标签页
          const metadataResult = await WorkonaTabManager.getTabMetadata(workonaId);
          if (metadataResult.success && metadataResult.data?.websiteId === website.id) {
            // 获取Chrome标签页ID
            const chromeIdResult = await WorkonaTabManager.getChromeIdByWorkonaId(workonaId);
            if (chromeIdResult.success && chromeIdResult.data) {
              const tab = await chrome.tabs.get(chromeIdResult.data);
              if (tab && tab.pinned) {
                await chrome.tabs.update(tab.id!, { pinned: false });
                console.log(`📌 取消固定标签页: ${tab.title}`);
              }
            }
          }
        } catch (error) {
          console.warn(`处理Workona标签页 ${workonaId} 时出错:`, error);
        }
      }
    } catch (error) {
      console.error('取消固定网站标签页失败:', error);
    }
  };

  /**
   * 打开全部标签页
   */
  const handleOpenAllTabs = async () => {
    try {
      setOpenAllTabsState({ loading: true, progress: { current: 0, total: 0 } });

      if (workspace.websites.length === 0) {
        showError('工作区中没有网站');
        setOpenAllTabsState({ loading: false, progress: { current: 0, total: 0 } });
        return;
      }

      console.log(`🚀 开始打开工作区 ${workspace.name} 的所有标签页...`);

      // 检查哪些网站还没有打开的标签页
      const unopenedWebsites: Website[] = [];

      for (const website of workspace.websites) {
        try {
          // 通过Workona ID映射检查是否已有对应的标签页
          const workonaTabIds = await WorkonaTabManager.getWorkspaceWorkonaTabIds(workspace.id);
          if (workonaTabIds.success && workonaTabIds.data) {
            let hasOpenTab = false;

            for (const workonaId of workonaTabIds.data) {
              const metadataResult = await WorkonaTabManager.getTabMetadata(workonaId);
              if (metadataResult.success && metadataResult.data?.websiteId === website.id) {
                // 检查对应的Chrome标签页是否存在
                const chromeIdResult = await WorkonaTabManager.getChromeIdByWorkonaId(workonaId);
                if (chromeIdResult.success && chromeIdResult.data) {
                  try {
                    await chrome.tabs.get(chromeIdResult.data);
                    hasOpenTab = true;
                    break;
                  } catch {
                    // 标签页不存在，需要清理映射
                    await WorkonaTabManager.removeTabMapping(workonaId);
                  }
                }
              }
            }

            if (!hasOpenTab) {
              unopenedWebsites.push(website);
            }
          } else {
            unopenedWebsites.push(website);
          }
        } catch (error) {
          console.warn(`检查网站 ${website.title} 的标签页状态时出错:`, error);
          unopenedWebsites.push(website);
        }
      }

      if (unopenedWebsites.length === 0) {
        showSuccess('所有网站的标签页都已打开');
        setOpenAllTabsState({ loading: false, progress: { current: 0, total: 0 } });
        return;
      }

      console.log(`需要打开 ${unopenedWebsites.length} 个网站的标签页`);

      // 批量打开标签页，避免一次性打开太多
      const BATCH_SIZE = 5;
      let openedCount = 0;

      setOpenAllTabsState({
        loading: true,
        progress: { current: 0, total: unopenedWebsites.length }
      });

      for (let i = 0; i < unopenedWebsites.length; i += BATCH_SIZE) {
        const batch = unopenedWebsites.slice(i, i + BATCH_SIZE);

        // 并行处理当前批次
        const batchPromises = batch.map(async (website) => {
          try {
            // 创建新标签页
            const newTab = await chrome.tabs.create({
              url: website.url,
              active: false // 在后台打开，不切换到新标签页
            });

            // 创建Workona ID映射
            if (newTab.id) {
              const workonaId = WorkonaTabManager.generateWorkonaTabId(workspace.id);
              const mappingResult = await WorkonaTabManager.createTabIdMapping(
                workonaId,
                newTab.id,
                workspace.id,
                website.id,
                {
                  isWorkspaceCore: true,
                  source: 'user_opened'
                }
              );

              if (mappingResult.success) {
                console.log(`✅ 为网站 ${website.title} 创建了标签页和映射: ${workonaId}`);
              } else {
                console.warn(`为网站 ${website.title} 创建映射失败:`, mappingResult.error);
              }
            }

            return true;
          } catch (error) {
            console.error(`打开网站 ${website.title} 的标签页失败:`, error);
            return false;
          }
        });

        // 等待当前批次完成
        const results = await Promise.all(batchPromises);
        const successCount = results.filter(Boolean).length;
        openedCount += successCount;

        // 更新进度
        setOpenAllTabsState({
          loading: true,
          progress: { current: openedCount, total: unopenedWebsites.length }
        });

        // 批次间添加小延迟，避免过于频繁的操作
        if (i + BATCH_SIZE < unopenedWebsites.length) {
          await new Promise(resolve => setTimeout(resolve, 500));
        }
      }

      setOpenAllTabsState({ loading: false, progress: { current: 0, total: 0 } });
      showSuccess(`成功打开了 ${openedCount} 个网站的标签页`);

    } catch (error) {
      console.error('打开全部标签页失败:', error);
      errorHandler.handleError(error, '打开全部标签页失败');
      setOpenAllTabsState({ loading: false, progress: { current: 0, total: 0 } });
    }
  };

  // 渲染打开全部标签页按钮
  const renderOpenAllTabsButton = () => {
    if (!isActive || workspace.websites.length === 0) return null;

    const { loading, progress } = openAllTabsState;

    if (loading) {
      return (
        <button
          disabled
          className="flex items-center gap-1 px-2 py-1 text-xs bg-gray-100 text-gray-500 rounded"
        >
          <Loader2 className="w-3 h-3 animate-spin" />
          <span>
            {progress.total > 0 
              ? `打开中... (${progress.current}/${progress.total})`
              : '准备中...'
            }
          </span>
        </button>
      );
    }

    return (
      <button
        onClick={handleOpenAllTabs}
        className="flex items-center gap-1 px-2 py-1 text-xs bg-green-100 text-green-700 rounded hover:bg-green-200 transition-colors"
        title="打开全部标签页"
      >
        <Play className="w-3 h-3" />
        <span>打开全部</span>
      </button>
    );
  };

  return (
    <div className="flex items-center gap-2">
      {renderOpenAllTabsButton()}
    </div>
  );
};

// 导出标签页操作方法供其他组件使用
export const useTabOperations = (workspace: WorkSpace) => {
  const { showError } = useToast();
  const errorHandler = new ToastErrorHandler(showError);

  const pinWebsiteTabs = async (website: Website) => {
    try {
      const workonaTabIds = await WorkonaTabManager.getWorkspaceWorkonaTabIds(workspace.id);
      if (!workonaTabIds.success) return;

      for (const workonaId of workonaTabIds.data || []) {
        const metadataResult = await WorkonaTabManager.getTabMetadata(workonaId);
        if (metadataResult.success && metadataResult.data?.websiteId === website.id) {
          const chromeIdResult = await WorkonaTabManager.getChromeIdByWorkonaId(workonaId);
          if (chromeIdResult.success && chromeIdResult.data) {
            const tab = await chrome.tabs.get(chromeIdResult.data);
            if (tab && !tab.pinned) {
              await chrome.tabs.update(tab.id!, { pinned: true });
            }
          }
        }
      }
    } catch (error) {
      errorHandler.handleError(error, '固定标签页失败');
    }
  };

  const unpinWebsiteTabs = async (website: Website) => {
    try {
      const workonaTabIds = await WorkonaTabManager.getWorkspaceWorkonaTabIds(workspace.id);
      if (!workonaTabIds.success) return;

      for (const workonaId of workonaTabIds.data || []) {
        const metadataResult = await WorkonaTabManager.getTabMetadata(workonaId);
        if (metadataResult.success && metadataResult.data?.websiteId === website.id) {
          const chromeIdResult = await WorkonaTabManager.getChromeIdByWorkonaId(workonaId);
          if (chromeIdResult.success && chromeIdResult.data) {
            const tab = await chrome.tabs.get(chromeIdResult.data);
            if (tab && tab.pinned) {
              await chrome.tabs.update(tab.id!, { pinned: false });
            }
          }
        }
      }
    } catch (error) {
      errorHandler.handleError(error, '取消固定标签页失败');
    }
  };

  return { pinWebsiteTabs, unpinWebsiteTabs };
};

export default WorkspaceItemTabManager;
