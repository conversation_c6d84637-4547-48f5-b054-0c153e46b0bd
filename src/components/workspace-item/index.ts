/**
 * 工作区项目组件模块统一导出
 * 
 * 🎯 模块职责分离：
 * - WorkspaceItemUserTabs: 用户标签页管理（显示/隐藏功能）
 * - WorkspaceItemTabManager: 标签页管理功能（打开全部、固定等）
 * - WorkspaceItemBatchOperations: 批量操作功能（批量选择、操作）
 * - WorkspaceItemActions: 用户操作处理（编辑、删除、添加等）
 * 
 * 📋 重构说明：
 * 原来的892行WorkspaceItem.tsx文件已按照单一职责原则拆分为4个专门的组件
 * 每个组件只负责一个核心功能，提高了代码的可维护性和可测试性
 */

// 用户标签页管理
export { default as WorkspaceItemUserTabs } from './WorkspaceItemUserTabs';

// 标签页管理
export { default as WorkspaceItemTabManager, useTabOperations } from './WorkspaceItemTabManager';

// 批量操作
export { default as WorkspaceItemBatchOperations } from './WorkspaceItemBatchOperations';

// 用户操作
export { default as WorkspaceItemActions, useWebsiteActions } from './WorkspaceItemActions';
