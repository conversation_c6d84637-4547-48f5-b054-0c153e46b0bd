import React, { useState } from 'react';
import {
  ChevronDown,
  ChevronRight,
  Monitor,
  Loader2
} from 'lucide-react';
import { WorkSpace } from '@/types/workspace';
import WebsiteList from './WebsiteList';
import {
  WorkspaceItemUserTabs,
  WorkspaceItemTabManager,
  WorkspaceItemBatchOperations,
  WorkspaceItemActions,
  useWebsiteActions
} from './workspace-item';

interface WorkspaceItemProps {
  workspace: WorkSpace;
  isActive: boolean;
  isExpanded: boolean;
  isSetupInProgress?: boolean; // 是否正在后台设置中
  onWorkspaceClick: () => void;
  onToggleExpand: () => void;
  onUpdateWorkspace: (updates: { name?: string; icon?: string; color?: string }) => void;
  onDeleteWorkspace: () => void;
  onAddCurrentTab: () => void;
  onAddWebsiteUrl: (url: string) => void;
  onRemoveWebsite: (websiteId: string) => void;
  onUpdateWebsite: (websiteId: string, updates: { url?: string; title?: string; isPinned?: boolean }) => void;
  onReorderWebsites: (websiteIds: string[]) => void;
  onTogglePin?: (websiteId: string, isPinned: boolean) => void;
  onBatchPin?: (websiteIds: string[], isPinned: boolean) => void;
  onBatchDelete?: (websiteIds: string[]) => void;
}

/**
 * 工作区项目组件 - 重构后的简化版本
 * 
 * 🎯 重构说明：
 * 原来的892行WorkspaceItem.tsx文件已按照单一职责原则拆分为4个专门的组件：
 * - WorkspaceItemUserTabs: 用户标签页管理（显示/隐藏功能）
 * - WorkspaceItemTabManager: 标签页管理功能（打开全部、固定等）
 * - WorkspaceItemBatchOperations: 批量操作功能（批量选择、操作）
 * - WorkspaceItemActions: 用户操作处理（编辑、删除、添加等）
 * 
 * 📋 向后兼容：
 * 本组件保留了原有的所有功能和接口，确保现有代码无需修改即可使用新的组件结构
 */
const WorkspaceItem: React.FC<WorkspaceItemProps> = ({
  workspace,
  isActive,
  isExpanded,
  isSetupInProgress = false,
  onWorkspaceClick,
  onToggleExpand,
  onUpdateWorkspace,
  onDeleteWorkspace,
  onAddCurrentTab,
  onAddWebsiteUrl,
  onRemoveWebsite,
  onUpdateWebsite,
  onReorderWebsites,

  onTogglePin,
  onBatchPin,
  onBatchDelete
}) => {
  const [batchMode, setBatchMode] = useState(false);
  const [selectedWebsites, setSelectedWebsites] = useState<Set<string>>(new Set());

  // 使用网站操作钩子
  const {
    handleEditWebsite,
    handleRemoveWebsite,
  } = useWebsiteActions(onUpdateWebsite, onRemoveWebsite);

  // 处理批量选择状态变化
  const handleSelectionChange = (selectedIds: Set<string>) => {
    setSelectedWebsites(selectedIds);
  };

  // 包装批量操作函数以匹配WebsiteList的接口
  const handleBatchPinWrapper = (websiteIds: string[]) => {
    onBatchPin?.(websiteIds, true);
  };

  const handleBatchUnpinWrapper = (websiteIds: string[]) => {
    onBatchPin?.(websiteIds, false);
  };

  return (
    <div className={`workspace-item ${isActive ? 'active' : ''} border border-gray-200 rounded-lg overflow-hidden`}>
      {/* 工作区头部 */}
      <div
        className={`p-4 cursor-pointer transition-colors ${
          isActive
            ? '!bg-blue-50 !border-l-4 !border-l-blue-500'
            : '!bg-white hover:!bg-gray-50'
        }`}
        onClick={onWorkspaceClick}
      >
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-3">
            {/* 展开/收起按钮 */}
            <button
              onClick={(e) => {
                e.stopPropagation();
                onToggleExpand();
              }}
              className="p-1 hover:bg-gray-200 rounded transition-colors"
            >
              {isExpanded ? (
                <ChevronDown className="w-4 h-4 text-gray-500" />
              ) : (
                <ChevronRight className="w-4 h-4 text-gray-500" />
              )}
            </button>

            {/* 工作区图标和名称 */}
            <div className="flex items-center gap-2">
              <span className="text-lg">{workspace.icon}</span>
              <div>
                <h3 className="font-medium text-gray-900">{workspace.name}</h3>
                <p className="text-sm text-gray-500">
                  {workspace.websites.length} 个网站
                </p>
              </div>
            </div>

            {/* 活跃状态指示器 */}
            {isActive && (
              <div className="flex items-center gap-1 px-2 py-1 bg-blue-100 text-blue-700 rounded-full text-xs">
                <Monitor className="w-3 h-3" />
                <span>活跃</span>
              </div>
            )}

            {/* 设置进行中指示器 */}
            {isSetupInProgress && (
              <div className="flex items-center gap-1 px-2 py-1 bg-orange-100 text-orange-700 rounded-full text-xs">
                <Loader2 className="w-3 h-3 animate-spin" />
                <span>设置中</span>
              </div>
            )}
          </div>

          {/* 右侧操作区域 */}
          <div className="flex items-center gap-2" onClick={(e) => e.stopPropagation()}>
            {/* 用户标签页管理 */}
            <WorkspaceItemUserTabs
              workspace={workspace}
              isActive={isActive}
            />

            {/* 标签页管理 */}
            <WorkspaceItemTabManager
              workspace={workspace}
              isActive={isActive}
            />

            {/* 批量操作 */}
            <WorkspaceItemBatchOperations
              workspace={workspace}
              isActive={isActive}
              batchMode={batchMode}
              setBatchMode={setBatchMode}
              selectedWebsites={selectedWebsites}
              onBatchPin={onBatchPin}
              onBatchDelete={onBatchDelete}
              onUpdateWebsite={onUpdateWebsite}
            />

            {/* 用户操作 */}
            <WorkspaceItemActions
              workspace={workspace}
              isActive={isActive}
              onUpdateWorkspace={onUpdateWorkspace}
              onDeleteWorkspace={onDeleteWorkspace}
              onAddCurrentTab={onAddCurrentTab}
              onAddWebsiteUrl={onAddWebsiteUrl}
              onUpdateWebsite={onUpdateWebsite}

            />
          </div>
        </div>
      </div>

      {/* 展开的内容区域 */}
      {isExpanded && (
        <div className="border-t border-gray-200">
          {/* 批量操作模式在右侧操作区域已处理，这里不需要重复渲染 */}

          {/* 网站列表 */}
          <div className="p-4">
            <WebsiteList
              websites={workspace.websites}
              activeWorkspace={workspace}
              onEditWebsite={handleEditWebsite}
              onRemoveWebsite={handleRemoveWebsite}
              onReorderWebsites={onReorderWebsites}
              onBatchDelete={onBatchDelete}
              onBatchPin={handleBatchPinWrapper}
              onBatchUnpin={handleBatchUnpinWrapper}
              onTogglePin={onTogglePin}
              batchMode={batchMode}
              onExitBatchMode={() => setBatchMode(false)}
              onSelectionChange={handleSelectionChange}
            />
          </div>
        </div>
      )}
    </div>
  );
};

export default WorkspaceItem;
