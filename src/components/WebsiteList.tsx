import React, { useState, useEffect } from 'react';
import { X, Edit, Check, Trash2, Pin, PinOff, Globe, XCircle } from 'lucide-react';
import { Website, WorkSpace } from '@/types/workspace';
import { useToast } from '@/components/Toast';
import { StorageManager } from '@/utils/storage';
import { WorkonaTabManager } from '@/utils/workonaTabManager';
import { WorkspaceSwitcher } from '@/utils/workspaceSwitcher';

interface WebsiteListProps {
  websites: Website[];
  activeWorkspace?: WorkSpace;
  onRemoveWebsite: (websiteId: string) => void;
  onEditWebsite: (website: Website) => void;
  onReorderWebsites?: (websiteIds: string[]) => void;
  onBatchDelete?: (websiteIds: string[]) => void;
  onBatchPin?: (websiteIds: string[]) => void;
  onBatchUnpin?: (websiteIds: string[]) => void;
  onTogglePin?: (websiteId: string, isPinned: boolean) => void;
  batchMode?: boolean;
  onExitBatchMode?: () => void;
  onSelectionChange?: (selectedIds: Set<string>) => void;
}

/**
 * 网站列表组件
 */
const WebsiteList: React.FC<WebsiteListProps> = ({
  websites,
  activeWorkspace,
  onRemoveWebsite,
  onEditWebsite,
  onReorderWebsites: _onReorderWebsites,
  onBatchDelete,
  onBatchPin,
  onBatchUnpin,
  onTogglePin,
  batchMode = false,
  onExitBatchMode,
  onSelectionChange,
}) => {
  const [selectedWebsites, setSelectedWebsites] = useState<Set<string>>(new Set());
  const [isSelectionMode, setIsSelectionMode] = useState(batchMode);
  const [showMoreMenu, setShowMoreMenu] = useState(false);
  const [pinnedWebsites, setPinnedWebsites] = useState<Set<string>>(new Set());
  const [openTabStates, setOpenTabStates] = useState<Record<string, { isOpen: boolean; chromeId?: number; workonaId?: string }>>({});

  // Toast 功能
  const { showInfo, showToast } = useToast();

  // 包装setSelectedWebsites以通知父组件
  const updateSelectedWebsites = (newSelection: Set<string>) => {
    setSelectedWebsites(newSelection);
    onSelectionChange?.(newSelection);
  };

  // 同步批量模式状态
  useEffect(() => {
    setIsSelectionMode(batchMode);
    if (!batchMode) {
      updateSelectedWebsites(new Set());
    }
  }, [batchMode]);

  // 实时检查标签页状态
  useEffect(() => {
    let intervalId: NodeJS.Timeout;

    const checkTabStates = async () => {
      try {
        // 使用静态导入的 WorkonaTabManager 和 WorkspaceSwitcher

        // 获取当前活跃工作区
        const activeWorkspaceResult = await WorkspaceSwitcher.getCurrentWorkspace();
        if (!activeWorkspaceResult.success || !activeWorkspaceResult.data) {
          return;
        }

        const workspaceId = activeWorkspaceResult.data.id;
        const websiteIds = websites.map(w => w.id);

        // 批量检查所有网站的标签页状态
        const statesResult = await WorkonaTabManager.getWorkspaceWebsiteTabStates(workspaceId, websiteIds);
        if (statesResult.success) {
          setOpenTabStates(statesResult.data!);
        }
      } catch (error) {
        console.warn('检查标签页状态失败:', error);
      }
    };

    // 立即检查一次
    checkTabStates();

    // 每秒检查一次状态
    intervalId = setInterval(checkTabStates, 1000);

    return () => {
      if (intervalId) {
        clearInterval(intervalId);
      }
    };
  }, [websites]);

  // 检查所有网站的固定状态
  useEffect(() => {
    const checkPinnedStatus = async () => {
      const pinnedSet = new Set<string>();

      for (const website of websites) {
        const isPinned = await isWebsitePinned(website);
        if (isPinned) {
          pinnedSet.add(website.id);
        }
      }

      setPinnedWebsites(pinnedSet);
    };

    if (websites.length > 0) {
      checkPinnedStatus();
    }
  }, [websites]);

  // 点击外部关闭更多菜单
  useEffect(() => {
    const handleClickOutside = (_event: MouseEvent) => {
      if (showMoreMenu) {
        setShowMoreMenu(false);
      }
    };

    document.addEventListener('click', handleClickOutside);
    return () => document.removeEventListener('click', handleClickOutside);
  }, [showMoreMenu]);

  /**
   * 基于Workona ID血缘关系的标签页检测（仅检测，不自动跳转）
   */
  const findExistingTab = async (website: Website): Promise<{ found: boolean; tabId?: number; tab?: chrome.tabs.Tab }> => {
    try {
      console.log('🔍 基于Workona ID血缘关系检查标签页:', website.url);

      // 使用静态导入的 StorageManager

      // 获取所有标签页映射
      const allMappings = await StorageManager.getTabIdMappings();
      if (!allMappings.success || !allMappings.data) {
        return { found: false };
      }

      // 查找匹配的映射
      const matchingMapping = allMappings.data.find(mapping =>
        mapping.workspaceId === activeWorkspace?.id &&
        mapping.websiteId === website.id
      );

      if (matchingMapping) {
        try {
          // 验证标签页是否仍然存在
          const tab = await chrome.tabs.get(matchingMapping.chromeId);
          if (tab) {
            console.log('✅ 找到基于Workona ID的匹配标签页:', tab.title);
            return { found: true, tabId: tab.id, tab };
          }
        } catch {
          // 标签页不存在，清理映射
          // 使用静态导入的 WorkonaTabManager
          await WorkonaTabManager.removeTabMapping(matchingMapping.workonaId);
          console.log('🗑️ 清理无效的标签页映射');
        }
      }

      return { found: false };
    } catch (error) {
      console.error('检查重复标签页失败:', error);
      return { found: false };
    }
  };

  /**
   * 激活已存在的标签页
   */
  const activateExistingTab = async (tab: chrome.tabs.Tab): Promise<void> => {
    try {
      await chrome.tabs.update(tab.id!, { active: true });
      await chrome.windows.update(tab.windowId, { focused: true });
      console.log('🎯 激活并定位到现有标签页');
    } catch (error) {
      console.error('激活标签页失败:', error);
    }
  };

  /**
   * 处理网站点击（优化版：支持工作区切换）
   */
  const handleWebsiteClick = async (website: Website) => {
    try {
      console.log('🔍 点击网站:', website.title, website.url);

      // 检查是否需要切换工作区
      if (activeWorkspace) {
        // 使用静态导入的 WorkspaceSwitcher
        const currentWorkspaceResult = await WorkspaceSwitcher.getCurrentWorkspace();

        if (currentWorkspaceResult.success && currentWorkspaceResult.data) {
          const currentWorkspaceId = currentWorkspaceResult.data.id;

          // 如果当前不在目标工作区，先切换工作区
          if (currentWorkspaceId !== activeWorkspace.id) {
            console.log(`🔄 切换到工作区: ${activeWorkspace.name}`);
            const switchResult = await WorkspaceSwitcher.switchToWorkspace(activeWorkspace.id);

            if (!switchResult.success) {
              console.error('工作区切换失败:', switchResult.error);
              showInfo('工作区切换失败', 3000);
              return;
            }
          }
        }
      }

      // 智能重复检测
      const existingResult = await findExistingTab(website);

      if (existingResult.found && existingResult.tab) {
        // 找到现有标签页，激活它
        await activateExistingTab(existingResult.tab);
        showInfo(`已切换到"${website.title}"`, 2000);
      } else {
        // 没有找到现有标签页，创建新的
        console.log('🆕 创建新标签页');
        const newTab = await chrome.tabs.create({
          url: website.url,
          pinned: false, // Workona 风格：不使用固定状态
          active: true
        });

        // 为工作区网站创建核心标签页 Workona ID 映射
        if (newTab.id && activeWorkspace) {
          try {
            // 使用静态导入的 WorkonaTabManager

            const workonaId = WorkonaTabManager.generateWorkonaTabId(activeWorkspace.id);

            const mappingResult = await WorkonaTabManager.createTabIdMapping(
              workonaId,
              newTab.id,
              activeWorkspace.id,
              website.id,
              {
                isWorkspaceCore: true, // 标记为工作区专属标签页
                source: 'workspace_website'
              }
            );

            if (mappingResult.success) {
              console.log(`✅ 为工作区网站创建核心标签页 Workona ID: ${workonaId}`);
            } else {
              console.error('创建Workona ID映射失败:', mappingResult.error);
            }
          } catch (error) {
            console.error('创建工作区核心标签页映射失败:', error);
          }

          // 立即更新标签页状态
          setTimeout(async () => {
            try {
              // 使用静态导入的 WorkonaTabManager 和 WorkspaceSwitcher

              // 获取当前活跃工作区
              const activeWorkspaceResult = await WorkspaceSwitcher.getCurrentWorkspace();
              if (activeWorkspaceResult.success && activeWorkspaceResult.data) {
                const workspaceId = activeWorkspaceResult.data.id;
                const websiteIds = websites.map(w => w.id);

                // 重新检查标签页状态
                const statesResult = await WorkonaTabManager.getWorkspaceWebsiteTabStates(workspaceId, websiteIds);
                if (statesResult.success) {
                  setOpenTabStates(statesResult.data!);
                }
              }
            } catch (error) {
              console.warn('更新标签页状态失败:', error);
            }
          }, 300); // 延迟300ms确保标签页完全创建
        }

        console.log('✅ 新标签页创建完成');
      }
    } catch (error) {
      console.error('Failed to open website:', error);
    }
  };

  // Workona 风格：移除固定状态切换功能

  /**
   * 处理移除网站
   */
  const handleRemoveWebsite = (e: React.MouseEvent, websiteId: string) => {
    e.stopPropagation();
    onRemoveWebsite(websiteId);
  };

  /**
   * 处理编辑网站
   */
  const handleEditWebsite = (e: React.MouseEvent, website: Website) => {
    e.stopPropagation();
    onEditWebsite(website);
  };

  // Workona 风格：移除选择模式切换功能

  /**
   * 处理网站选择
   */
  const handleWebsiteSelect = (websiteId: string) => {
    const newSelected = new Set(selectedWebsites);
    if (newSelected.has(websiteId)) {
      newSelected.delete(websiteId);
    } else {
      newSelected.add(websiteId);
    }
    updateSelectedWebsites(newSelected);
  };

  /**
   * 处理全选/取消全选
   */
  const handleSelectAll = () => {
    if (selectedWebsites.size === websites.length) {
      updateSelectedWebsites(new Set());
    } else {
      updateSelectedWebsites(new Set(websites.map(w => w.id)));
    }
  };

  // Workona 风格：移除批量固定功能

  /**
   * 处理批量删除
   */
  const handleBatchDelete = () => {
    const selectedIds = Array.from(selectedWebsites);

    if (onBatchDelete) {
      onBatchDelete(selectedIds);
    }

    // 退出选择模式
    setIsSelectionMode(false);
    updateSelectedWebsites(new Set());
  };

  /**
   * 处理批量固定
   */
  const handleBatchPin = () => {
    const selectedIds = Array.from(selectedWebsites);

    if (onBatchPin) {
      onBatchPin(selectedIds);
    }

    // 退出选择模式
    setIsSelectionMode(false);
    updateSelectedWebsites(new Set());
  };

  /**
   * 处理批量取消固定
   */
  const handleBatchUnpin = () => {
    const selectedIds = Array.from(selectedWebsites);

    if (onBatchUnpin) {
      onBatchUnpin(selectedIds);
    }

    // 退出选择模式
    setIsSelectionMode(false);
    updateSelectedWebsites(new Set());
  };

  /**
   * 处理单个网站的固定操作
   */
  const handlePinWebsite = async (e: React.MouseEvent, website: Website) => {
    e.stopPropagation();
    try {
      // 通过Workona ID映射查找对应的标签页并固定
      // 使用静态导入的 WorkonaTabManager 和 WorkspaceSwitcher

      // 获取当前活跃工作区
      const activeWorkspaceResult = await WorkspaceSwitcher.getCurrentWorkspace();
      if (!activeWorkspaceResult.success || !activeWorkspaceResult.data) {
        console.warn('无法获取当前工作区');
        return;
      }

      const workspaceId = activeWorkspaceResult.data.id;
      const workonaTabIds = await WorkonaTabManager.getWorkspaceWorkonaTabIds(workspaceId);

      if (!workonaTabIds.success) {
        console.error('❌ 获取工作区Workona标签页ID失败:', workonaTabIds.error);
        return;
      }

      for (const workonaId of workonaTabIds.data!) {
        try {
          // 检查是否是对应网站的标签页
          const metadataResult = await WorkonaTabManager.getTabMetadata(workonaId);
          if (metadataResult.success && metadataResult.data?.websiteId === website.id) {
            // 获取Chrome标签页ID
            const chromeIdResult = await WorkonaTabManager.getChromeIdByWorkonaId(workonaId);
            if (chromeIdResult.success && chromeIdResult.data) {
              const tab = await chrome.tabs.get(chromeIdResult.data);
              if (tab && !tab.pinned) {
                await chrome.tabs.update(tab.id!, { pinned: true });
                console.log(`📌 固定标签页: ${tab.title}`);
                // 更新状态
                setPinnedWebsites(prev => new Set(prev).add(website.id));
              }
            }
          }
        } catch (error) {
          console.warn(`⚠️ 处理Workona标签页 ${workonaId} 时出错:`, error);
        }
      }
    } catch (error) {
      console.error('❌ 固定标签页失败:', error);
    }
  };

  /**
   * 处理单个网站的取消固定操作
   */
  const handleUnpinWebsite = async (e: React.MouseEvent, website: Website) => {
    e.stopPropagation();
    try {
      // 通过Workona ID映射查找对应的标签页并取消固定
      // 使用静态导入的 WorkonaTabManager 和 WorkspaceSwitcher

      // 获取当前活跃工作区
      const activeWorkspaceResult = await WorkspaceSwitcher.getCurrentWorkspace();
      if (!activeWorkspaceResult.success || !activeWorkspaceResult.data) {
        console.warn('无法获取当前工作区');
        return;
      }

      const workspaceId = activeWorkspaceResult.data.id;
      const workonaTabIds = await WorkonaTabManager.getWorkspaceWorkonaTabIds(workspaceId);

      if (!workonaTabIds.success) {
        console.error('❌ 获取工作区Workona标签页ID失败:', workonaTabIds.error);
        return;
      }

      for (const workonaId of workonaTabIds.data!) {
        try {
          // 检查是否是对应网站的标签页
          const metadataResult = await WorkonaTabManager.getTabMetadata(workonaId);
          if (metadataResult.success && metadataResult.data?.websiteId === website.id) {
            // 获取Chrome标签页ID
            const chromeIdResult = await WorkonaTabManager.getChromeIdByWorkonaId(workonaId);
            if (chromeIdResult.success && chromeIdResult.data) {
              const tab = await chrome.tabs.get(chromeIdResult.data);
              if (tab && tab.pinned) {
                await chrome.tabs.update(tab.id!, { pinned: false });
                console.log(`📌 取消固定标签页: ${tab.title}`);
                // 更新状态
                setPinnedWebsites(prev => {
                  const newSet = new Set(prev);
                  newSet.delete(website.id);
                  return newSet;
                });
              }
            }
          }
        } catch (error) {
          console.warn(`⚠️ 处理Workona标签页 ${workonaId} 时出错:`, error);
        }
      }
    } catch (error) {
      console.error('❌ 取消固定标签页失败:', error);
    }
  };

  /**
   * 检查网站对应的标签页是否已固定
   */
  const isWebsitePinned = async (website: Website): Promise<boolean> => {
    try {
      // 使用静态导入的 WorkonaTabManager 和 WorkspaceSwitcher

      // 获取当前活跃工作区
      const activeWorkspaceResult = await WorkspaceSwitcher.getCurrentWorkspace();
      if (!activeWorkspaceResult.success || !activeWorkspaceResult.data) {
        return false;
      }

      const workspaceId = activeWorkspaceResult.data.id;
      const workonaTabIds = await WorkonaTabManager.getWorkspaceWorkonaTabIds(workspaceId);

      if (!workonaTabIds.success) {
        return false;
      }

      for (const workonaId of workonaTabIds.data!) {
        try {
          // 检查是否是对应网站的标签页
          const metadataResult = await WorkonaTabManager.getTabMetadata(workonaId);
          if (metadataResult.success && metadataResult.data?.websiteId === website.id) {
            // 获取Chrome标签页ID
            const chromeIdResult = await WorkonaTabManager.getChromeIdByWorkonaId(workonaId);
            if (chromeIdResult.success && chromeIdResult.data) {
              const tab = await chrome.tabs.get(chromeIdResult.data);
              return tab?.pinned || false;
            }
          }
        } catch (error) {
          // 标签页可能不存在，继续检查其他标签页
          continue;
        }
      }
      return false;
    } catch (error) {
      console.error('❌ 检查固定状态失败:', error);
      return false;
    }
  };

  /**
   * 获取网站图标
   */
  const getWebsiteIcon = (website: Website) => {
    if (website.favicon && website.favicon !== '') {
      return (
        <img
          src={website.favicon}
          alt=""
          className="w-4 h-4 rounded"
          onError={(e) => {
            // 如果图标加载失败，显示默认图标
            (e.target as HTMLImageElement).style.display = 'none';
          }}
        />
      );
    }

    // 默认图标
    return (
      <div className="w-4 h-4 bg-slate-600 rounded flex items-center justify-center">
        <Globe className="w-2.5 h-2.5 text-slate-400" />
      </div>
    );
  };

  /**
   * 关闭网站对应的标签页
   */
  const handleCloseTab = async (e: React.MouseEvent, website: Website) => {
    e.stopPropagation();

    try {
      console.log(`🔄 关闭网站标签页: ${website.title}`);

      // 检查网站是否有打开的标签页
      if (!openTabStates[website.id]?.isOpen) {
        showToast('info', '该网站没有打开的标签页');
        return;
      }

      // 获取工作区的Workona标签页ID列表
      const activeWorkspaceResult = await WorkspaceSwitcher.getCurrentWorkspace();
      if (!activeWorkspaceResult.success || !activeWorkspaceResult.data) {
        showToast('error', '无法获取当前工作区信息');
        return;
      }

      const workonaTabIds = await WorkonaTabManager.getWorkspaceWorkonaTabIds(activeWorkspaceResult.data.id);
      if (!workonaTabIds.success || !workonaTabIds.data) {
        showToast('error', '无法获取工作区标签页信息');
        return;
      }

      // 查找对应网站的标签页并关闭
      let tabClosed = false;
      for (const workonaId of workonaTabIds.data) {
        try {
          // 检查是否是对应网站的标签页
          const metadataResult = await WorkonaTabManager.getTabMetadata(workonaId);
          if (metadataResult.success && metadataResult.data?.websiteId === website.id) {
            // 获取Chrome标签页ID
            const chromeIdResult = await WorkonaTabManager.getChromeIdByWorkonaId(workonaId);
            if (chromeIdResult.success && chromeIdResult.data) {
              // 关闭标签页
              await chrome.tabs.remove(chromeIdResult.data);

              // 清理Workona ID映射
              await WorkonaTabManager.removeTabMapping(workonaId);

              tabClosed = true;
              console.log(`✅ 成功关闭标签页: ${website.title}`);
              showToast('success', `已关闭 ${website.title}`);
              break;
            }
          }
        } catch (error) {
          // 标签页可能已经不存在，继续检查其他标签页
          console.warn(`标签页可能已关闭: ${workonaId}`, error);
          // 清理无效的映射
          await WorkonaTabManager.removeTabMapping(workonaId);
        }
      }

      if (!tabClosed) {
        showToast('info', '未找到要关闭的标签页');
      }

      // 立即更新标签页状态
      setTimeout(async () => {
        try {
          const workspaceId = activeWorkspaceResult.data!.id;
          const websiteIds = websites.map(w => w.id);

          // 重新检查标签页状态
          const statesResult = await WorkonaTabManager.getWorkspaceWebsiteTabStates(workspaceId, websiteIds);
          if (statesResult.success) {
            setOpenTabStates(statesResult.data!);
          }
        } catch (error) {
          console.warn('更新标签页状态失败:', error);
        }
      }, 200); // 延迟200ms确保标签页完全关闭

    } catch (error) {
      console.error('❌ 关闭标签页失败:', error);
      showToast('error', '关闭标签页失败');
    }
  };

  /**
   * 格式化URL显示
   */
  const formatUrl = (url: string) => {
    try {
      const urlObj = new URL(url);
      return urlObj.hostname.replace('www.', '');
    } catch {
      return url;
    }
  };

  // 按order字段排序
  const sortedWebsites = [...websites].sort((a, b) => a.order - b.order);

  return (
    <div className="space-y-1">
      {/* 批量操作工具栏 - 只在批量模式下显示 */}
      {websites.length > 0 && batchMode && isSelectionMode && (
        <div className="mb-2 relative">
          <div className="flex items-center justify-between px-1 py-1.5 bg-slate-700/80 rounded border border-slate-600/50">
            <div className="flex items-center gap-2">
              {isSelectionMode && (
                <>
                  <button
                    onClick={handleSelectAll}
                    className="px-2 py-1 bg-slate-600 text-slate-200 hover:bg-slate-500 hover:text-white rounded text-xs transition-all duration-200"
                  >
                    {selectedWebsites.size === websites.length ? '取消' : '全选'}
                  </button>

                  <div className="flex items-center gap-1 px-1.5 py-1 bg-slate-800/50 rounded">
                    <div className="w-1 h-1 bg-blue-500 rounded-full"></div>
                    <span className="text-xs text-slate-300">
                      {selectedWebsites.size}
                    </span>
                  </div>
                </>
              )}
            </div>

            {/* 右侧按钮组 */}
            <div className="flex items-center gap-1">
              {/* 批量操作按钮 */}
              {selectedWebsites.size > 0 && (
                <>
                  <button
                    onClick={handleBatchPin}
                    className="flex items-center justify-center w-6 h-6 bg-blue-600 text-white hover:bg-blue-700 rounded transition-all duration-200"
                    title="批量固定"
                  >
                    <Pin className="w-3 h-3" />
                  </button>
                  <button
                    onClick={handleBatchUnpin}
                    className="flex items-center justify-center w-6 h-6 bg-slate-600 text-white hover:bg-slate-700 rounded transition-all duration-200"
                    title="批量取消固定"
                  >
                    <PinOff className="w-3 h-3" />
                  </button>
                  <button
                    onClick={handleBatchDelete}
                    className="flex items-center justify-center w-6 h-6 bg-red-600 text-white hover:bg-red-700 rounded transition-all duration-200"
                    title="删除选中"
                  >
                    <Trash2 className="w-3 h-3" />
                  </button>
                </>
              )}

              {/* 退出批量模式按钮 */}
              <button
                onClick={onExitBatchMode}
                className="flex items-center justify-center w-6 h-6 bg-slate-600 text-slate-200 hover:bg-slate-500 hover:text-white rounded transition-all duration-200"
                title="退出批量模式"
              >
                <X className="w-3 h-3" />
              </button>
            </div>
          </div>
        </div>
      )}

      {sortedWebsites.map((website) => {
        const isSelected = selectedWebsites.has(website.id);

        return (
        <div
          key={website.id}
          className={`website-item ${isSelected ? 'bg-blue-600/20 border-blue-500' : ''}`}
          onClick={() => isSelectionMode ? handleWebsiteSelect(website.id) : handleWebsiteClick(website)}
        >
          {/* 选择框 */}
          {isSelectionMode && (
            <div className="flex-shrink-0">
              <div className={`w-4 h-4 rounded border-2 flex items-center justify-center ${
                isSelected
                  ? 'bg-blue-600 border-blue-600'
                  : 'border-slate-400'
              }`}>
                {isSelected && <Check className="w-3 h-3 text-white" />}
              </div>
            </div>
          )}

          {/* 网站图标 */}
          <div className="flex-shrink-0">
            {getWebsiteIcon(website)}
          </div>

          {/* 网站信息 */}
          <div className="flex-1 min-w-0">
            <div className="flex items-center gap-2">
              <span className="text-sm text-white truncate">
                {website.title}
              </span>
              {/* 标签页状态指示器 - 与工作区活跃状态指示器完全一致的样式 */}
              {openTabStates[website.id]?.isOpen && (
                <div
                  className="w-1.5 h-1.5 bg-green-500 rounded-full animate-pulse flex-shrink-0"
                  title="标签页已打开"
                />
              )}
            </div>
            <p className="text-xs text-slate-400 truncate">
              {formatUrl(website.url)}
            </p>
          </div>

          {/* 操作按钮 */}
          {!isSelectionMode && (
            <div className="website-actions flex items-center gap-1">
              {/* 固定/取消固定按钮 */}
              {pinnedWebsites.has(website.id) ? (
                <button
                  onClick={(e) => handleUnpinWebsite(e, website)}
                  className="p-1 hover:bg-orange-600 rounded transition-colors duration-150"
                  title="取消固定标签页"
                >
                  <PinOff className="w-3 h-3 text-orange-400 hover:text-white" />
                </button>
              ) : (
                <button
                  onClick={(e) => handlePinWebsite(e, website)}
                  className="p-1 hover:bg-blue-600 rounded transition-colors duration-150"
                  title="固定标签页"
                >
                  <Pin className="w-3 h-3 text-slate-400 hover:text-white" />
                </button>
              )}

              {/* 关闭标签页按钮 - 只在标签页打开时显示 */}
              {openTabStates[website.id]?.isOpen && (
                <button
                  onClick={(e) => handleCloseTab(e, website)}
                  className="p-1 hover:bg-gray-600 rounded transition-colors duration-150"
                  title="关闭标签页"
                >
                  <XCircle className="w-3 h-3 text-slate-400 hover:text-gray-300" />
                </button>
              )}

              <button
                onClick={(e) => handleEditWebsite(e, website)}
                className="p-1 hover:bg-blue-600 rounded transition-colors duration-150"
                title="编辑网站"
              >
                <Edit className="w-3 h-3 text-slate-400 hover:text-white" />
              </button>
              <button
                onClick={(e) => handleRemoveWebsite(e, website.id)}
                className="p-1 hover:bg-red-600 rounded transition-colors duration-150"
                title="移除网站"
              >
                <Trash2 className="w-3 h-3 text-slate-400 hover:text-white" />
              </button>
            </div>
          )}
        </div>
        );
      })}
    </div>
  );
};

export default WebsiteList;
