import {
  WorkSpace,
  OperationResult
} from '@/types/workspace';
import { ERROR_CODES } from '../constants';
import { WorkonaTabManager } from '../workonaTabManager';
import { TabClassificationUtils } from './TabClassificationUtils';

/**
 * 工作区标签页移动管理器
 * 职责：处理工作区切换时的标签页移动逻辑
 * 
 * 🎯 核心职责：
 * 1. 标签页在窗口间的移动操作
 * 2. 工作区专属窗口的创建和管理
 * 3. 标签页移动的批量处理和优化
 * 4. 移动过程中的错误处理和回滚
 */
export class WorkspaceTabMover {
  /**
   * 将非目标工作区的标签页移动到后台窗口
   */
  static async moveNonTargetWorkspaceTabsToWindow(targetWorkspaceId: string): Promise<OperationResult<void>> {
    try {
      console.log(`🔄 移动非目标工作区标签页到后台窗口 (目标工作区: ${targetWorkspaceId})`);

      // 获取当前窗口的所有标签页
      const currentTabs = await chrome.tabs.query({ currentWindow: true });
      const tabsToMove: number[] = [];

      // 筛选需要移动的标签页
      for (const tab of currentTabs) {
        if (!tab.id || !tab.url) continue;

        // 跳过系统标签页
        if (TabClassificationUtils.isSystemTab(tab.url)) {
          continue;
        }

        // 跳过当前活跃标签页
        if (tab.active) {
          continue;
        }

        // 检查是否属于目标工作区
        const workonaIdResult = await WorkonaTabManager.getWorkonaIdByChromeId(tab.id);
        if (workonaIdResult.success && workonaIdResult.data) {
          const tabWorkspaceId = workonaIdResult.data.split('-')[1];
          if (tabWorkspaceId !== targetWorkspaceId) {
            tabsToMove.push(tab.id);
          }
        } else {
          // 无Workona ID的标签页也移动到后台
          tabsToMove.push(tab.id);
        }
      }

      if (tabsToMove.length === 0) {
        console.log(`ℹ️ 没有需要移动的标签页`);
        return { success: true };
      }

      // 创建或获取后台窗口
      const backgroundWindow = await this.getOrCreateBackgroundWindow();
      if (!backgroundWindow) {
        return {
          success: false,
          error: {
            code: ERROR_CODES.WINDOW_ERROR,
            message: 'Failed to create background window',
          },
        };
      }

      // 批量移动标签页
      await chrome.tabs.move(tabsToMove, {
        windowId: backgroundWindow.id!,
        index: -1
      });

      console.log(`✅ 成功移动 ${tabsToMove.length} 个标签页到后台窗口`);
      return { success: true };
    } catch (error) {
      return {
        success: false,
        error: {
          code: ERROR_CODES.TAB_ERROR,
          message: 'Failed to move non-target workspace tabs',
          details: error,
        },
      };
    }
  }

  /**
   * 将当前标签页移动到工作区专属窗口
   */
  static async moveCurrentTabsToWorkspaceWindow(workspace: WorkSpace): Promise<OperationResult<void>> {
    try {
      console.log(`🔄 移动当前标签页到工作区 "${workspace.name}" 的专属窗口`);

      // 获取当前窗口的标签页
      const currentTabs = await chrome.tabs.query({ currentWindow: true });
      const workspaceTabIds: number[] = [];

      // 筛选属于目标工作区的标签页
      for (const tab of currentTabs) {
        if (!tab.id || !tab.url) continue;

        // 跳过系统标签页
        if (TabClassificationUtils.isSystemTab(tab.url)) {
          continue;
        }

        // 检查是否属于目标工作区
        const workonaIdResult = await WorkonaTabManager.getWorkonaIdByChromeId(tab.id);
        if (workonaIdResult.success && workonaIdResult.data) {
          const tabWorkspaceId = workonaIdResult.data.split('-')[1];
          if (tabWorkspaceId === workspace.id) {
            workspaceTabIds.push(tab.id);
          }
        }
      }

      if (workspaceTabIds.length === 0) {
        console.log(`ℹ️ 当前窗口没有属于工作区 "${workspace.name}" 的标签页`);
        return { success: true };
      }

      // 获取或创建工作区专属窗口
      const workspaceWindow = await this.getOrCreateWorkspaceWindow(workspace);
      if (!workspaceWindow) {
        return {
          success: false,
          error: {
            code: ERROR_CODES.WINDOW_ERROR,
            message: 'Failed to create workspace window',
          },
        };
      }

      // 移动标签页到工作区窗口
      await chrome.tabs.move(workspaceTabIds, {
        windowId: workspaceWindow.id!,
        index: -1
      });

      console.log(`✅ 成功移动 ${workspaceTabIds.length} 个标签页到工作区窗口`);
      return { success: true };
    } catch (error) {
      return {
        success: false,
        error: {
          code: ERROR_CODES.TAB_ERROR,
          message: 'Failed to move tabs to workspace window',
          details: error,
        },
      };
    }
  }

  /**
   * 将标签页从工作区窗口移动回主窗口
   */
  static async moveTabsFromWorkspaceWindow(workspace: WorkSpace): Promise<OperationResult<void>> {
    try {
      console.log(`🔄 将标签页从工作区 "${workspace.name}" 窗口移动回主窗口`);

      // 获取所有窗口
      const allWindows = await chrome.windows.getAll({ populate: true });
      const currentWindow = await chrome.windows.getCurrent();
      
      // 查找工作区相关的标签页
      const tabsToMove: number[] = [];

      for (const window of allWindows) {
        if (window.id === currentWindow.id || !window.tabs) {
          continue;
        }

        for (const tab of window.tabs) {
          if (!tab.id || !tab.url) continue;

          // 跳过系统标签页
          if (TabClassificationUtils.isSystemTab(tab.url)) {
            continue;
          }

          // 检查是否属于目标工作区
          const workonaIdResult = await WorkonaTabManager.getWorkonaIdByChromeId(tab.id);
          if (workonaIdResult.success && workonaIdResult.data) {
            const tabWorkspaceId = workonaIdResult.data.split('-')[1];
            if (tabWorkspaceId === workspace.id) {
              tabsToMove.push(tab.id);
            }
          }
        }
      }

      if (tabsToMove.length === 0) {
        console.log(`ℹ️ 没有需要移动回主窗口的标签页`);
        return { success: true };
      }

      // 移动标签页到当前窗口
      await chrome.tabs.move(tabsToMove, {
        windowId: currentWindow.id!,
        index: -1
      });

      console.log(`✅ 成功移动 ${tabsToMove.length} 个标签页回主窗口`);
      return { success: true };
    } catch (error) {
      return {
        success: false,
        error: {
          code: ERROR_CODES.TAB_ERROR,
          message: 'Failed to move tabs from workspace window',
          details: error,
        },
      };
    }
  }

  /**
   * 获取或创建后台窗口
   */
  private static async getOrCreateBackgroundWindow(): Promise<chrome.windows.Window | null> {
    try {
      // 尝试查找现有的后台窗口
      const allWindows = await chrome.windows.getAll();
      const backgroundWindow = allWindows.find(window => 
        window.state === 'minimized' && window.type === 'normal'
      );

      if (backgroundWindow) {
        return backgroundWindow;
      }

      // 创建新的后台窗口
      const newWindow = await chrome.windows.create({
        url: 'about:blank',
        focused: false,
        state: 'minimized',
        type: 'normal'
      });

      return newWindow;
    } catch (error) {
      console.error('创建后台窗口失败:', error);
      return null;
    }
  }

  /**
   * 获取或创建工作区专属窗口
   */
  private static async getOrCreateWorkspaceWindow(_workspace: WorkSpace): Promise<chrome.windows.Window | null> {
    try {
      // 这里可以实现工作区专属窗口的逻辑
      // 目前简化为使用当前窗口
      const currentWindow = await chrome.windows.getCurrent();
      return currentWindow;
    } catch (error) {
      console.error('获取工作区窗口失败:', error);
      return null;
    }
  }
}
