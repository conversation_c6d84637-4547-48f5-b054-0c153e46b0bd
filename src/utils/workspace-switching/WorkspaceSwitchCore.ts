import {
  WorkSpace,
  WorkspaceSwitchOptions,
  OperationResult
} from '@/types/workspace';
import { StorageManager } from '../storage';
import { ERROR_CODES } from '../constants';

/**
 * 工作区切换核心逻辑
 * 职责：处理工作区切换的核心流程和状态管理
 * 
 * 🎯 核心职责：
 * 1. 工作区切换的主要流程控制
 * 2. 切换状态管理和保护机制
 * 3. 切换选项处理和合并
 * 4. 当前工作区检测和管理
 */
export class WorkspaceSwitchCore {
  private static isBackgroundSetupInProgress = false; // 后台设置进行中标志
  private static currentSetupWorkspaceId: string | null = null; // 当前正在设置的工作区ID

  /**
   * 检查是否有后台设置正在进行
   */
  static isSetupInProgress(): boolean {
    return this.isBackgroundSetupInProgress;
  }

  /**
   * 获取当前正在设置的工作区ID
   */
  static getCurrentSetupWorkspaceId(): string | null {
    return this.currentSetupWorkspaceId;
  }

  /**
   * 设置后台设置状态
   */
  static setBackgroundSetupStatus(workspaceId: string | null, inProgress: boolean): void {
    this.isBackgroundSetupInProgress = inProgress;
    this.currentSetupWorkspaceId = workspaceId;
    
    if (inProgress && workspaceId) {
      console.log(`🔧 开始后台设置工作区: ${workspaceId}`);
    } else {
      console.log(`✅ 后台设置完成`);
    }
  }

  /**
   * 获取当前活跃工作区
   */
  static async getCurrentWorkspace(): Promise<OperationResult<WorkSpace | null>> {
    try {
      // 策略1：从存储中获取活跃工作区ID
      const activeIdResult = await StorageManager.getActiveWorkspaceId();
      if (activeIdResult.success && activeIdResult.data) {
        const workspaceResult = await StorageManager.getWorkspace(activeIdResult.data);
        if (workspaceResult.success) {
          return { success: true, data: workspaceResult.data };
        }
      }

      // 策略2：如果没有存储的活跃工作区，返回null
      return { success: true, data: null };
    } catch (error) {
      return {
        success: false,
        error: {
          code: ERROR_CODES.STORAGE_ERROR,
          message: 'Failed to get current workspace',
          details: error,
        },
      };
    }
  }

  /**
   * 设置当前活跃工作区
   */
  static async setCurrentWorkspace(workspaceId: string | null): Promise<OperationResult<void>> {
    try {
      const result = await StorageManager.setActiveWorkspaceId(workspaceId);
      if (result.success) {
        console.log(`📌 设置活跃工作区: ${workspaceId || 'none'}`);
      }
      return result;
    } catch (error) {
      return {
        success: false,
        error: {
          code: ERROR_CODES.STORAGE_ERROR,
          message: 'Failed to set current workspace',
          details: error,
        },
      };
    }
  }

  /**
   * 准备工作区切换选项
   */
  static async prepareSwitchOptions(
    options: WorkspaceSwitchOptions = {}
  ): Promise<OperationResult<WorkspaceSwitchOptions>> {
    try {
      // 获取用户设置
      const settingsResult = await StorageManager.getSettings();
      if (!settingsResult.success) {
        return { success: false, error: settingsResult.error };
      }

      const settings = settingsResult.data!;

      // 合并切换选项
      // 优先使用传入的选项，其次使用用户设置，最后使用默认值
      const switchOptions: WorkspaceSwitchOptions = {
        closeOtherTabs: options.closeOtherTabs ?? settings.autoCloseOtherTabs,
        preserveUserOpenedTabs: options.preserveUserOpenedTabs ?? settings.preserveUserOpenedTabs,
        focusFirstTab: options.focusFirstTab ?? false, // 默认不自动聚焦到第一个标签页
        openInNewWindow: options.openInNewWindow ?? false,
        skipAnimation: options.skipAnimation ?? false
      };

      console.log(`⚙️ 切换选项:`, switchOptions);
      return { success: true, data: switchOptions };
    } catch (error) {
      return {
        success: false,
        error: {
          code: ERROR_CODES.STORAGE_ERROR,
          message: 'Failed to prepare switch options',
          details: error,
        },
      };
    }
  }

  /**
   * 验证工作区切换请求
   */
  static async validateSwitchRequest(workspaceId: string): Promise<OperationResult<WorkSpace>> {
    try {
      // 检查并发切换保护
      if (this.isBackgroundSetupInProgress) {
        const currentSetupId = this.currentSetupWorkspaceId;
        console.warn(`⚠️ 后台工作区设置正在进行中 (${currentSetupId})，拒绝切换到 ${workspaceId}`);
        return {
          success: false,
          error: {
            code: ERROR_CODES.TAB_ERROR,
            message: 'Background workspace setup in progress',
            details: `Cannot switch to ${workspaceId} while ${currentSetupId} is being set up`
          }
        };
      }

      // 获取目标工作区信息
      const workspaceResult = await StorageManager.getWorkspace(workspaceId);
      if (!workspaceResult.success) {
        return { success: false, error: workspaceResult.error };
      }

      const workspace = workspaceResult.data!;
      console.log(`📋 目标工作区: ${workspace.name} (${workspace.websites.length} 个网站)`);

      return { success: true, data: workspace };
    } catch (error) {
      return {
        success: false,
        error: {
          code: ERROR_CODES.WORKSPACE_ERROR,
          message: 'Failed to validate switch request',
          details: error,
        },
      };
    }
  }

  /**
   * 检查是否需要切换（避免重复切换到同一工作区）
   */
  static async shouldPerformSwitch(targetWorkspaceId: string): Promise<boolean> {
    try {
      const currentWorkspaceResult = await this.getCurrentWorkspace();
      if (currentWorkspaceResult.success && currentWorkspaceResult.data) {
        const currentWorkspace = currentWorkspaceResult.data;
        if (currentWorkspace.id === targetWorkspaceId) {
          console.log(`ℹ️ 已在目标工作区 "${currentWorkspace.name}"，跳过切换`);
          return false;
        }
      }
      return true;
    } catch (error) {
      console.warn('检查是否需要切换时出错:', error);
      return true; // 出错时默认执行切换
    }
  }

  /**
   * 完成工作区切换后的清理工作
   */
  static async finalizeSwitchOperation(workspaceId: string): Promise<OperationResult<void>> {
    try {
      // 设置活跃工作区
      const setActiveResult = await this.setCurrentWorkspace(workspaceId);
      if (!setActiveResult.success) {
        return setActiveResult;
      }

      // 清理后台设置状态
      this.setBackgroundSetupStatus(null, false);

      console.log(`✅ 工作区切换完成: ${workspaceId}`);
      return { success: true };
    } catch (error) {
      return {
        success: false,
        error: {
          code: ERROR_CODES.WORKSPACE_ERROR,
          message: 'Failed to finalize switch operation',
          details: error,
        },
      };
    }
  }
}
