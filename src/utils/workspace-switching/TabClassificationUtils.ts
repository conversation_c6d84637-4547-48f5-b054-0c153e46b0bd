import { WorkonaTabManager } from '../workonaTabManager';

/**
 * 新的3分类标签页系统工具类
 * 职责：提供标签页分类和识别的工具方法
 * 
 * 🎯 核心职责：
 * 1. 标签页类型识别（系统/工作区/用户）
 * 2. 批量标签页分类处理
 * 3. 标签页状态检查和验证
 * 
 * 📋 分类定义：
 * 1. 系统标签页：Chrome内置页面（chrome://、chrome-extension://、about:等）
 * 2. 工作区专属标签页：具有Workona ID映射的标签页
 * 3. 用户标签页：普通网站标签页，不具有Workona ID映射
 */
export class TabClassificationUtils {
  /**
   * 判断是否为系统标签页
   */
  static isSystemTab(url: string): boolean {
    return url.includes('chrome://') ||
           url.includes('chrome-extension://') ||
           url.includes('about:') ||
           url.includes('edge://') ||
           url.includes('workspace-placeholder.html') ||
           url === 'chrome://newtab/' ||
           url === 'about:blank' ||
           url === '';
  }

  /**
   * 检查是否为工作区专属标签页（具有Workona ID映射）
   */
  static async isWorkspaceSpecificTab(tabId: number): Promise<boolean> {
    try {
      const workonaIdResult = await WorkonaTabManager.getWorkonaIdByChromeId(tabId);
      return workonaIdResult.success && !!workonaIdResult.data;
    } catch {
      return false;
    }
  }

  /**
   * 检查是否为用户标签页（非系统且非工作区专属）
   */
  static async isUserTab(tab: { id: number; url?: string }): Promise<boolean> {
    if (!tab.url || this.isSystemTab(tab.url)) {
      return false;
    }
    
    // 检查是否为工作区专属标签页
    const isWorkspaceSpecific = await this.isWorkspaceSpecificTab(tab.id);
    return !isWorkspaceSpecific;
  }

  /**
   * 批量分类标签页
   */
  static async classifyTabs(tabs: chrome.tabs.Tab[]): Promise<{
    systemTabs: chrome.tabs.Tab[];
    workspaceTabs: chrome.tabs.Tab[];
    userTabs: chrome.tabs.Tab[];
  }> {
    const systemTabs: chrome.tabs.Tab[] = [];
    const workspaceTabs: chrome.tabs.Tab[] = [];
    const userTabs: chrome.tabs.Tab[] = [];

    for (const tab of tabs) {
      if (!tab.url || !tab.id) continue;

      if (this.isSystemTab(tab.url)) {
        systemTabs.push(tab);
      } else {
        const isWorkspaceSpecific = await this.isWorkspaceSpecificTab(tab.id);
        if (isWorkspaceSpecific) {
          workspaceTabs.push(tab);
        } else {
          userTabs.push(tab);
        }
      }
    }

    return { systemTabs, workspaceTabs, userTabs };
  }

  /**
   * 获取标签页的分类类型
   */
  static async getTabCategory(tab: { id: number; url?: string }): Promise<'system' | 'workspace' | 'user'> {
    if (!tab.url || this.isSystemTab(tab.url)) {
      return 'system';
    }

    const isWorkspaceSpecific = await this.isWorkspaceSpecificTab(tab.id);
    return isWorkspaceSpecific ? 'workspace' : 'user';
  }

  /**
   * 检查标签页是否可以被移动
   */
  static canMoveTab(tab: chrome.tabs.Tab): boolean {
    if (!tab.url) return false;
    
    // 系统标签页通常不能被移动
    if (this.isSystemTab(tab.url)) {
      return false;
    }

    // 检查是否为特殊的不可移动标签页
    const unmovableUrls = [
      'chrome://newtab/',
      'about:blank',
      'chrome://extensions/',
      'chrome://settings/'
    ];

    return !unmovableUrls.some(url => tab.url!.startsWith(url));
  }

  /**
   * 过滤出可移动的标签页
   */
  static filterMovableTabs(tabs: chrome.tabs.Tab[]): chrome.tabs.Tab[] {
    return tabs.filter(tab => this.canMoveTab(tab));
  }

  /**
   * 检查标签页是否为工作区占位符页面
   */
  static isWorkspacePlaceholder(url: string): boolean {
    return url.includes('workspace-placeholder.html');
  }

  /**
   * 检查标签页是否为新标签页
   */
  static isNewTab(url: string): boolean {
    return url === 'chrome://newtab/' || url === 'about:blank' || url === '';
  }
}
