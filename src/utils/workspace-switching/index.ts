/**
 * 工作区切换模块统一导出
 * 
 * 🎯 模块职责分离：
 * - TabClassificationUtils: 标签页分类和识别工具
 * - WorkspaceSwitchCore: 工作区切换核心逻辑和状态管理
 * - WorkspaceTabMover: 标签页移动和窗口管理
 * - WorkspaceProtectionManager: 系统保护机制和异常处理
 * - WorkspaceNotificationManager: 通知管理和状态同步
 * 
 * 📋 重构说明：
 * 原来的1,704行workspaceSwitcher.ts文件已按照单一职责原则拆分为5个专门的服务类
 * 每个类只负责一个核心功能，提高了代码的可维护性和可测试性
 */

// 标签页分类工具
export { TabClassificationUtils } from './TabClassificationUtils';

// 工作区切换核心逻辑
export { WorkspaceSwitchCore } from './WorkspaceSwitchCore';

// 标签页移动管理
export { WorkspaceTabMover } from './WorkspaceTabMover';

// 系统保护机制
export { WorkspaceProtectionManager } from './WorkspaceProtectionManager';

// 通知管理
export { WorkspaceNotificationManager } from './WorkspaceNotificationManager';
