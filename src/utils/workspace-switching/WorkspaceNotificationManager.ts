/**
 * 工作区通知管理器
 * 职责：处理工作区切换相关的通知和状态同步
 * 
 * 🎯 核心职责：
 * 1. 工作区切换完成通知
 * 2. 工作区设置状态变更通知
 * 3. 扩展内部消息传递
 * 4. 状态同步事件触发
 */
export class WorkspaceNotificationManager {
  /**
   * 通知工作区切换完成
   */
  static async notifyWorkspaceSwitchComplete(workspaceId: string): Promise<void> {
    try {
      console.log(`📢 通知工作区切换完成: ${workspaceId}`);

      // 发送Chrome扩展消息
      const message = {
        type: 'WORKSPACE_SWITCHED',
        workspaceId,
        timestamp: Date.now()
      };

      // 尝试发送消息到所有监听器
      try {
        await chrome.runtime.sendMessage(message);
      } catch (error) {
        // 忽略没有监听器的错误
        console.log('📢 没有消息监听器，跳过消息发送');
      }

      // 触发自定义事件（如果在浏览器环境中）
      if (typeof window !== 'undefined' && window.dispatchEvent) {
        const customEvent = new CustomEvent('workspaceSwitched', {
          detail: { workspaceId, timestamp: Date.now() }
        });
        window.dispatchEvent(customEvent);
      }

      console.log(`✅ 工作区切换通知发送完成: ${workspaceId}`);
    } catch (error) {
      console.error('发送工作区切换通知失败:', error);
    }
  }

  /**
   * 通知工作区设置状态变更
   */
  static async notifyWorkspaceSetupStatusChange(workspaceId: string, isSetupInProgress: boolean): Promise<void> {
    try {
      console.log(`📢 通知工作区设置状态变更: ${workspaceId}, 进行中: ${isSetupInProgress}`);

      // 发送Chrome扩展消息
      const message = {
        type: 'WORKSPACE_SETUP_STATUS_CHANGED',
        workspaceId,
        isSetupInProgress,
        timestamp: Date.now()
      };

      // 尝试发送消息到所有监听器
      try {
        await chrome.runtime.sendMessage(message);
      } catch (error) {
        // 忽略没有监听器的错误
        console.log('📢 没有消息监听器，跳过消息发送');
      }

      // 触发自定义事件（如果在浏览器环境中）
      if (typeof window !== 'undefined' && window.dispatchEvent) {
        const customEvent = new CustomEvent('workspaceSetupStatusChanged', {
          detail: { workspaceId, isSetupInProgress, timestamp: Date.now() }
        });
        window.dispatchEvent(customEvent);
      }

      console.log(`✅ 工作区设置状态通知发送完成: ${workspaceId}`);
    } catch (error) {
      console.error('发送工作区设置状态通知失败:', error);
    }
  }

  /**
   * 通知工作区标签页状态变更
   */
  static async notifyWorkspaceTabStateChange(workspaceId: string, changeType: string, details?: any): Promise<void> {
    try {
      console.log(`📢 通知工作区标签页状态变更: ${workspaceId}, 类型: ${changeType}`);

      // 发送Chrome扩展消息
      const message = {
        type: 'WORKSPACE_TAB_STATE_CHANGED',
        workspaceId,
        changeType,
        details,
        timestamp: Date.now()
      };

      // 尝试发送消息到所有监听器
      try {
        await chrome.runtime.sendMessage(message);
      } catch (error) {
        // 忽略没有监听器的错误
        console.log('📢 没有消息监听器，跳过消息发送');
      }

      console.log(`✅ 工作区标签页状态变更通知发送完成: ${workspaceId}`);
    } catch (error) {
      console.error('发送工作区标签页状态变更通知失败:', error);
    }
  }

  /**
   * 通知用户标签页可见性变更
   */
  static async notifyUserTabsVisibilityChange(workspaceId: string, isHidden: boolean, affectedCount: number): Promise<void> {
    try {
      console.log(`📢 通知用户标签页可见性变更: ${workspaceId}, 隐藏: ${isHidden}, 影响数量: ${affectedCount}`);

      // 发送Chrome扩展消息
      const message = {
        type: 'USER_TABS_VISIBILITY_CHANGED',
        workspaceId,
        isHidden,
        affectedCount,
        timestamp: Date.now()
      };

      // 尝试发送消息到所有监听器
      try {
        await chrome.runtime.sendMessage(message);
      } catch (error) {
        // 忽略没有监听器的错误
        console.log('📢 没有消息监听器，跳过消息发送');
      }

      console.log(`✅ 用户标签页可见性变更通知发送完成: ${workspaceId}`);
    } catch (error) {
      console.error('发送用户标签页可见性变更通知失败:', error);
    }
  }

  /**
   * 发送通用工作区事件通知
   */
  static async sendWorkspaceEvent(eventType: string, workspaceId: string, data?: any): Promise<void> {
    try {
      console.log(`📢 发送工作区事件: ${eventType}, 工作区: ${workspaceId}`);

      // 发送Chrome扩展消息
      const message = {
        type: 'WORKSPACE_EVENT',
        eventType,
        workspaceId,
        data,
        timestamp: Date.now()
      };

      // 尝试发送消息到所有监听器
      try {
        await chrome.runtime.sendMessage(message);
      } catch (error) {
        // 忽略没有监听器的错误
        console.log('📢 没有消息监听器，跳过消息发送');
      }

      // 触发自定义事件（如果在浏览器环境中）
      if (typeof window !== 'undefined' && window.dispatchEvent) {
        const customEvent = new CustomEvent('workspaceEvent', {
          detail: { eventType, workspaceId, data, timestamp: Date.now() }
        });
        window.dispatchEvent(customEvent);
      }

      console.log(`✅ 工作区事件通知发送完成: ${eventType}`);
    } catch (error) {
      console.error('发送工作区事件通知失败:', error);
    }
  }

  /**
   * 批量发送通知
   */
  static async sendBatchNotifications(notifications: Array<{
    type: string;
    workspaceId: string;
    data?: any;
  }>): Promise<void> {
    try {
      console.log(`📢 批量发送 ${notifications.length} 个通知`);

      // 并行发送所有通知
      const promises = notifications.map(notification => 
        this.sendWorkspaceEvent(notification.type, notification.workspaceId, notification.data)
      );

      await Promise.allSettled(promises);

      console.log(`✅ 批量通知发送完成`);
    } catch (error) {
      console.error('批量发送通知失败:', error);
    }
  }

  /**
   * 清理过期的通知监听器
   */
  static cleanupExpiredListeners(): void {
    try {
      console.log('🧹 清理过期的通知监听器');

      // 这里可以添加清理逻辑
      // 例如：移除不再活跃的事件监听器、清理缓存等

      console.log('✅ 通知监听器清理完成');
    } catch (error) {
      console.error('清理通知监听器失败:', error);
    }
  }
}
