import {
  WorkSpace,
  OperationResult
} from '@/types/workspace';
import {
  STORAGE_KEYS,
  ERROR_CODES
} from '../constants';

/**
 * 工作区存储管理器
 * 职责：处理工作区相关的存储操作
 * 
 * 🎯 核心职责：
 * 1. 工作区数据的读写操作
 * 2. 工作区查询和检索
 * 3. 工作区数据验证
 * 4. 工作区存储优化
 */
export class WorkspaceStorage {
  /**
   * 获取所有工作区
   */
  static async getWorkspaces(): Promise<OperationResult<WorkSpace[]>> {
    try {
      const result = await chrome.storage.local.get(STORAGE_KEYS.WORKSPACES);
      const workspaces = result[STORAGE_KEYS.WORKSPACES] || [];
      
      // 验证工作区数据完整性
      const validatedWorkspaces = WorkspaceStorage.validateWorkspaces(workspaces);
      
      return { success: true, data: validatedWorkspaces };
    } catch (error) {
      return {
        success: false,
        error: {
          code: ERROR_CODES.STORAGE_ERROR,
          message: 'Failed to get workspaces',
          details: error,
        },
      };
    }
  }

  /**
   * 保存工作区列表
   */
  static async saveWorkspaces(workspaces: WorkSpace[]): Promise<OperationResult<void>> {
    try {
      // 验证工作区数据
      const validatedWorkspaces = WorkspaceStorage.validateWorkspaces(workspaces);
      
      // 保存到存储
      await chrome.storage.local.set({
        [STORAGE_KEYS.WORKSPACES]: validatedWorkspaces,
      });

      return { success: true };
    } catch (error) {
      return {
        success: false,
        error: {
          code: ERROR_CODES.STORAGE_ERROR,
          message: 'Failed to save workspaces',
          details: error,
        },
      };
    }
  }

  /**
   * 获取单个工作区
   */
  static async getWorkspace(id: string): Promise<OperationResult<WorkSpace>> {
    const result = await WorkspaceStorage.getWorkspaces();
    if (!result.success) {
      return { success: false, error: result.error };
    }

    const workspaces = result.data!;
    const workspace = workspaces.find(w => w.id === id);

    if (!workspace) {
      return {
        success: false,
        error: {
          code: ERROR_CODES.WORKSPACE_NOT_FOUND,
          message: `Workspace with id ${id} not found`,
        },
      };
    }

    return { success: true, data: workspace };
  }

  /**
   * 更新单个工作区
   */
  static async updateWorkspace(updatedWorkspace: WorkSpace): Promise<OperationResult<void>> {
    try {
      // 获取所有工作区
      const workspacesResult = await WorkspaceStorage.getWorkspaces();
      if (!workspacesResult.success) {
        return { success: false, error: workspacesResult.error };
      }

      const workspaces = workspacesResult.data!;
      const workspaceIndex = workspaces.findIndex(w => w.id === updatedWorkspace.id);

      if (workspaceIndex === -1) {
        return {
          success: false,
          error: {
            code: ERROR_CODES.WORKSPACE_NOT_FOUND,
            message: `Workspace with id ${updatedWorkspace.id} not found`,
          },
        };
      }

      // 更新工作区
      workspaces[workspaceIndex] = updatedWorkspace;

      // 保存更新后的列表
      return await WorkspaceStorage.saveWorkspaces(workspaces);
    } catch (error) {
      return {
        success: false,
        error: {
          code: ERROR_CODES.STORAGE_ERROR,
          message: 'Failed to update workspace',
          details: error,
        },
      };
    }
  }

  /**
   * 删除工作区
   */
  static async deleteWorkspace(id: string): Promise<OperationResult<void>> {
    try {
      // 获取所有工作区
      const workspacesResult = await WorkspaceStorage.getWorkspaces();
      if (!workspacesResult.success) {
        return { success: false, error: workspacesResult.error };
      }

      const workspaces = workspacesResult.data!;
      const workspaceIndex = workspaces.findIndex(w => w.id === id);

      if (workspaceIndex === -1) {
        return {
          success: false,
          error: {
            code: ERROR_CODES.WORKSPACE_NOT_FOUND,
            message: `Workspace with id ${id} not found`,
          },
        };
      }

      // 删除工作区
      workspaces.splice(workspaceIndex, 1);

      // 保存更新后的列表
      return await WorkspaceStorage.saveWorkspaces(workspaces);
    } catch (error) {
      return {
        success: false,
        error: {
          code: ERROR_CODES.STORAGE_ERROR,
          message: 'Failed to delete workspace',
          details: error,
        },
      };
    }
  }

  /**
   * 查找工作区
   */
  static async findWorkspaces(predicate: (workspace: WorkSpace) => boolean): Promise<OperationResult<WorkSpace[]>> {
    try {
      const workspacesResult = await WorkspaceStorage.getWorkspaces();
      if (!workspacesResult.success) {
        return { success: false, error: workspacesResult.error };
      }

      const workspaces = workspacesResult.data!;
      const matchedWorkspaces = workspaces.filter(predicate);

      return { success: true, data: matchedWorkspaces };
    } catch (error) {
      return {
        success: false,
        error: {
          code: ERROR_CODES.STORAGE_ERROR,
          message: 'Failed to find workspaces',
          details: error,
        },
      };
    }
  }

  /**
   * 获取工作区统计信息
   */
  static async getWorkspaceStats(): Promise<OperationResult<{
    totalWorkspaces: number;
    totalWebsites: number;
    activeWorkspaces: number;
    averageWebsitesPerWorkspace: number;
  }>> {
    try {
      const workspacesResult = await WorkspaceStorage.getWorkspaces();
      if (!workspacesResult.success) {
        return { success: false, error: workspacesResult.error };
      }

      const workspaces = workspacesResult.data!;
      const totalWorkspaces = workspaces.length;
      const totalWebsites = workspaces.reduce((sum, ws) => sum + ws.websites.length, 0);
      const activeWorkspaces = workspaces.filter(ws => ws.isActive).length;
      const averageWebsitesPerWorkspace = totalWorkspaces > 0 ? totalWebsites / totalWorkspaces : 0;

      return {
        success: true,
        data: {
          totalWorkspaces,
          totalWebsites,
          activeWorkspaces,
          averageWebsitesPerWorkspace,
        },
      };
    } catch (error) {
      return {
        success: false,
        error: {
          code: ERROR_CODES.STORAGE_ERROR,
          message: 'Failed to get workspace stats',
          details: error,
        },
      };
    }
  }

  /**
   * 验证工作区数据完整性
   */
  private static validateWorkspaces(workspaces: WorkSpace[]): WorkSpace[] {
    return workspaces.map(workspace => {
      // 确保必需字段存在
      const validatedWorkspace: WorkSpace = {
        id: workspace.id || `ws_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
        name: workspace.name || 'Untitled Workspace',
        icon: workspace.icon || '📁',
        color: workspace.color || '#3B82F6',
        websites: workspace.websites || [],
        createdAt: workspace.createdAt || Date.now(),
        updatedAt: workspace.updatedAt || Date.now(),
        isActive: workspace.isActive || false,
        order: workspace.order || 0,
        
        // 可选字段
        description: workspace.description,
        position: workspace.position,
        windowId: workspace.windowId,
        userTabsHidden: workspace.userTabsHidden,
        hiddenUserTabIds: workspace.hiddenUserTabIds,
        hiddenTabIds: workspace.hiddenTabIds,
        pinnedTabIds: workspace.pinnedTabIds,
        type: workspace.type,
        pos: workspace.pos,
        state: workspace.state,
        workonaTabIds: workspace.workonaTabIds,
        sessionId: workspace.sessionId,
        tabOrder: workspace.tabOrder,
      };

      // 验证网站数据
      validatedWorkspace.websites = workspace.websites?.map(website => ({
        id: website.id || `site_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
        url: website.url || '',
        title: website.title || 'Untitled Website',
        favicon: website.favicon || '/icons/default-favicon.png',
        isPinned: website.isPinned || false,
        addedAt: website.addedAt || Date.now(),
        order: website.order || 0,
      })) || [];

      return validatedWorkspace;
    });
  }

  /**
   * 压缩工作区数据（移除不必要的字段）
   */
  static compressWorkspaceData(workspaces: WorkSpace[]): WorkSpace[] {
    return workspaces.map(workspace => {
      const compressed: any = {
        id: workspace.id,
        name: workspace.name,
        icon: workspace.icon,
        color: workspace.color,
        websites: workspace.websites,
        createdAt: workspace.createdAt,
        updatedAt: workspace.updatedAt,
        isActive: workspace.isActive,
        order: workspace.order,
      };

      // 只包含有值的可选字段
      if (workspace.description) compressed.description = workspace.description;
      if (workspace.position !== undefined) compressed.position = workspace.position;
      if (workspace.windowId) compressed.windowId = workspace.windowId;
      if (workspace.userTabsHidden) compressed.userTabsHidden = workspace.userTabsHidden;
      if (workspace.hiddenUserTabIds?.length) compressed.hiddenUserTabIds = workspace.hiddenUserTabIds;
      if (workspace.hiddenTabIds?.length) compressed.hiddenTabIds = workspace.hiddenTabIds;
      if (workspace.pinnedTabIds?.length) compressed.pinnedTabIds = workspace.pinnedTabIds;
      if (workspace.type) compressed.type = workspace.type;
      if (workspace.pos !== undefined) compressed.pos = workspace.pos;
      if (workspace.state) compressed.state = workspace.state;
      if (workspace.workonaTabIds?.length) compressed.workonaTabIds = workspace.workonaTabIds;
      if (workspace.sessionId) compressed.sessionId = workspace.sessionId;
      if (workspace.tabOrder?.length) compressed.tabOrder = workspace.tabOrder;

      return compressed as WorkSpace;
    });
  }
}
