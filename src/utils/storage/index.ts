/**
 * 存储管理模块统一导出
 * 
 * 🎯 模块职责分离：
 * - StorageCore: 核心存储操作（基础CRUD、设置管理）
 * - WorkspaceStorage: 工作区相关存储（工作区数据管理）
 * - WorkonaStorage: Workona格式相关存储（扩展数据管理）
 * - StorageImportExport: 数据导入导出（备份恢复）
 * 
 * 📋 重构说明：
 * 原来的1,062行storage.ts文件已按照单一职责原则拆分为4个专门的服务类
 * 每个类只负责一个核心功能，提高了代码的可维护性和可测试性
 */

// 核心存储操作
export { StorageCore } from './StorageCore';

// 工作区存储
export { WorkspaceStorage } from './WorkspaceStorage';

// Workona格式存储
export { WorkonaStorage } from './WorkonaStorage';

// 导入导出
export { StorageImportExport } from './StorageImportExport';
