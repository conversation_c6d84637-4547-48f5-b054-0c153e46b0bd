import {
  Settings,
  OperationResult
} from '@/types/workspace';
import {
  STORAGE_KEYS,
  DEFAULT_SETTINGS,
  ERROR_CODES
} from '../constants';

/**
 * 存储核心管理器
 * 职责：处理基础的Chrome存储API操作
 * 
 * 🎯 核心职责：
 * 1. 基础存储读写操作
 * 2. 设置管理
 * 3. 活跃工作区状态管理
 * 4. 存储变化监听
 * 5. 存储清理操作
 */
export class StorageCore {
  private static changeListeners: Array<(changes: { [key: string]: chrome.storage.StorageChange }) => void> = [];

  /**
   * 获取设置
   */
  static async getSettings(): Promise<OperationResult<Settings>> {
    try {
      const result = await chrome.storage.local.get(STORAGE_KEYS.SETTINGS);
      const settings = { ...DEFAULT_SETTINGS, ...result[STORAGE_KEYS.SETTINGS] };
      return { success: true, data: settings };
    } catch (error) {
      return {
        success: false,
        error: {
          code: ERROR_CODES.STORAGE_ERROR,
          message: 'Failed to get settings',
          details: error,
        },
      };
    }
  }

  /**
   * 保存设置
   */
  static async saveSettings(settings: Partial<Settings>): Promise<OperationResult<void>> {
    try {
      // 获取现有设置
      const currentResult = await StorageCore.getSettings();
      if (!currentResult.success) {
        return { success: false, error: currentResult.error };
      }

      // 合并设置
      const mergedSettings = { ...currentResult.data!, ...settings };

      // 保存到存储
      await chrome.storage.local.set({
        [STORAGE_KEYS.SETTINGS]: mergedSettings,
      });

      return { success: true };
    } catch (error) {
      return {
        success: false,
        error: {
          code: ERROR_CODES.STORAGE_ERROR,
          message: 'Failed to save settings',
          details: error,
        },
      };
    }
  }

  /**
   * 获取活跃工作区ID
   */
  static async getActiveWorkspaceId(): Promise<OperationResult<string | null>> {
    try {
      const result = await chrome.storage.local.get(STORAGE_KEYS.ACTIVE_WORKSPACE_ID);
      const activeWorkspaceId = result[STORAGE_KEYS.ACTIVE_WORKSPACE_ID] || null;
      return { success: true, data: activeWorkspaceId };
    } catch (error) {
      return {
        success: false,
        error: {
          code: ERROR_CODES.STORAGE_ERROR,
          message: 'Failed to get active workspace ID',
          details: error,
        },
      };
    }
  }

  /**
   * 设置活跃工作区ID
   */
  static async setActiveWorkspaceId(id: string | null): Promise<OperationResult<void>> {
    try {
      await chrome.storage.local.set({
        [STORAGE_KEYS.ACTIVE_WORKSPACE_ID]: id,
      });
      return { success: true };
    } catch (error) {
      return {
        success: false,
        error: {
          code: ERROR_CODES.STORAGE_ERROR,
          message: 'Failed to set active workspace ID',
          details: error,
        },
      };
    }
  }

  /**
   * 更新最近活跃工作区列表
   */
  static async updateLastActiveWorkspaces(workspaceId: string): Promise<OperationResult<void>> {
    try {
      // 获取现有列表
      const result = await chrome.storage.local.get(STORAGE_KEYS.LAST_ACTIVE_WORKSPACE_IDS);
      let lastActiveIds: string[] = result[STORAGE_KEYS.LAST_ACTIVE_WORKSPACE_IDS] || [];

      // 移除重复项并添加到开头
      lastActiveIds = lastActiveIds.filter(id => id !== workspaceId);
      lastActiveIds.unshift(workspaceId);

      // 限制列表长度（保留最近10个）
      lastActiveIds = lastActiveIds.slice(0, 10);

      // 保存更新后的列表
      await chrome.storage.local.set({
        [STORAGE_KEYS.LAST_ACTIVE_WORKSPACE_IDS]: lastActiveIds,
      });

      return { success: true };
    } catch (error) {
      return {
        success: false,
        error: {
          code: ERROR_CODES.STORAGE_ERROR,
          message: 'Failed to update last active workspaces',
          details: error,
        },
      };
    }
  }

  /**
   * 获取最近活跃工作区列表
   */
  static async getLastActiveWorkspaces(): Promise<OperationResult<string[]>> {
    try {
      const result = await chrome.storage.local.get(STORAGE_KEYS.LAST_ACTIVE_WORKSPACE_IDS);
      const lastActiveIds = result[STORAGE_KEYS.LAST_ACTIVE_WORKSPACE_IDS] || [];
      return { success: true, data: lastActiveIds };
    } catch (error) {
      return {
        success: false,
        error: {
          code: ERROR_CODES.STORAGE_ERROR,
          message: 'Failed to get last active workspaces',
          details: error,
        },
      };
    }
  }

  /**
   * 清除所有存储数据
   */
  static async clearAll(): Promise<OperationResult<void>> {
    try {
      await chrome.storage.local.clear();
      return { success: true };
    } catch (error) {
      return {
        success: false,
        error: {
          code: ERROR_CODES.STORAGE_ERROR,
          message: 'Failed to clear storage',
          details: error,
        },
      };
    }
  }

  /**
   * 获取存储使用情况
   */
  static async getStorageUsage(): Promise<OperationResult<{
    bytesInUse: number;
    quota: number;
    percentUsed: number;
  }>> {
    try {
      const bytesInUse = await chrome.storage.local.getBytesInUse();
      const quota = chrome.storage.local.QUOTA_BYTES;
      const percentUsed = (bytesInUse / quota) * 100;

      return {
        success: true,
        data: {
          bytesInUse,
          quota,
          percentUsed,
        },
      };
    } catch (error) {
      return {
        success: false,
        error: {
          code: ERROR_CODES.STORAGE_ERROR,
          message: 'Failed to get storage usage',
          details: error,
        },
      };
    }
  }

  /**
   * 添加存储变化监听器
   */
  static onChanged(callback: (changes: { [key: string]: chrome.storage.StorageChange }) => void): void {
    StorageCore.changeListeners.push(callback);
    
    // 如果是第一个监听器，设置Chrome存储监听
    if (StorageCore.changeListeners.length === 1) {
      chrome.storage.onChanged.addListener((changes, areaName) => {
        if (areaName === 'local') {
          StorageCore.changeListeners.forEach(listener => {
            try {
              listener(changes);
            } catch (error) {
              console.error('存储变化监听器执行失败:', error);
            }
          });
        }
      });
    }
  }

  /**
   * 移除存储变化监听器
   */
  static removeChangeListener(callback: (changes: { [key: string]: chrome.storage.StorageChange }) => void): void {
    const index = StorageCore.changeListeners.indexOf(callback);
    if (index > -1) {
      StorageCore.changeListeners.splice(index, 1);
    }
  }

  /**
   * 检查存储键是否存在
   */
  static async hasKey(key: string): Promise<OperationResult<boolean>> {
    try {
      const result = await chrome.storage.local.get(key);
      return { success: true, data: key in result };
    } catch (error) {
      return {
        success: false,
        error: {
          code: ERROR_CODES.STORAGE_ERROR,
          message: 'Failed to check storage key',
          details: error,
        },
      };
    }
  }

  /**
   * 删除指定的存储键
   */
  static async removeKey(key: string): Promise<OperationResult<void>> {
    try {
      await chrome.storage.local.remove(key);
      return { success: true };
    } catch (error) {
      return {
        success: false,
        error: {
          code: ERROR_CODES.STORAGE_ERROR,
          message: 'Failed to remove storage key',
          details: error,
        },
      };
    }
  }

  /**
   * 批量删除存储键
   */
  static async removeKeys(keys: string[]): Promise<OperationResult<void>> {
    try {
      await chrome.storage.local.remove(keys);
      return { success: true };
    } catch (error) {
      return {
        success: false,
        error: {
          code: ERROR_CODES.STORAGE_ERROR,
          message: 'Failed to remove storage keys',
          details: error,
        },
      };
    }
  }
}
