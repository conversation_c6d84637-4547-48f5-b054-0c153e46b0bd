import {
  TabInfo,
  WorkSpace,
  WorkonaTabMatchResult
} from '@/types/workspace';

/**
 * 标签页管理模块 - 重构后的简化版本
 * 
 * 🎯 重构说明：
 * 原来的2,305行tabs.ts文件已按照单一职责原则拆分为多个专门的服务类：
 * - TabManager: 基础标签页操作 (./tabs/TabManager.ts)
 * - TabClassificationService: 标签页分类和自动归属 (./tabs/TabClassificationService.ts)
 * - UserTabsRealTimeMonitor: 用户标签页实时监控 (./tabs/UserTabsRealTimeMonitor.ts)
 * 
 * 📋 向后兼容：
 * 本文件保留了原有的导出接口，确保现有代码无需修改即可使用新的模块结构
 */

// 重新导出拆分后的类，保持向后兼容
export { TabManager } from './tabs/TabManager';
export { TabClassificationService } from './tabs/TabClassificationService';
export { UserTabsRealTimeMonitor } from './tabs/UserTabsRealTimeMonitor';
export { WorkspaceUserTabsVisibilityManager } from './tabs/WorkspaceUserTabsVisibilityManager';

/**
 * 工作区标签页内容匹配系统
 * 职责：基于内容匹配标签页与工作区的关联关系
 */
export class WorkspaceTabContentMatcher {
  /**
   * 检查标签页是否匹配工作区内容
   */
  static async matchTabToWorkspace(tab: TabInfo, workspace: WorkSpace): Promise<WorkonaTabMatchResult> {
    // 基于URL匹配
    const urlMatch = workspace.websites.some(website => 
      tab.url.startsWith(website.url) || website.url.startsWith(tab.url)
    );

    if (urlMatch) {
      return {
        isMatch: true,
        confidence: 0.9,
        matchType: 'prefix'
      };
    }

    return {
      isMatch: false,
      confidence: 0,
      matchType: 'none'
    };
  }
}

/**
 * 调试控制器
 * 职责：控制调试模式和日志输出级别
 */
export class DebugController {
  static readonly isDebugMode = false; // 设置为 true 启用详细日志
  static readonly isVerboseMode = false; // 设置为 true 启用超详细日志
}

/**
 * 用户标签页工具类
 * 职责：提供用户标签页的识别和分类工具方法
 */
export class UserTabsUtils {
  /**
   * 检查标签页是否为系统标签页
   */
  static isSystemTab(url: string): boolean {
    const systemPrefixes = [
      'chrome://',
      'chrome-extension://',
      'edge://',
      'about:',
      'moz-extension://',
      'safari-extension://'
    ];

    return systemPrefixes.some(prefix => url.startsWith(prefix));
  }

  /**
   * 检查标签页是否为扩展内部页面
   */
  static isExtensionTab(url: string): boolean {
    return url.includes('workspace-placeholder.html') || 
           url.startsWith('chrome-extension://') ||
           url === 'chrome://newtab/';
  }
}

/**
 * 标签页分类工具类
 * 职责：提供标签页分类的工具方法
 */
export class TabClassificationUtils {
  /**
   * 使用新的3分类系统判断系统页面
   */
  static isSystemTab(url: string): boolean {
    return UserTabsUtils.isSystemTab(url) || UserTabsUtils.isExtensionTab(url);
  }

  /**
   * 检查标签页是否为用户标签页
   */
  static isUserTab(url: string): boolean {
    return !this.isSystemTab(url);
  }

  /**
   * 获取标签页分类
   */
  static getTabCategory(url: string): 'system' | 'extension' | 'user' {
    if (UserTabsUtils.isSystemTab(url)) {
      return 'system';
    }
    if (UserTabsUtils.isExtensionTab(url)) {
      return 'extension';
    }
    return 'user';
  }
}
