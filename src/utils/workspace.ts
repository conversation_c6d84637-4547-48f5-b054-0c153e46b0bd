import {
  WorkSpace,
  Website,
  CreateWorkspaceOptions,
  UpdateWorkspaceOptions,
  AddWebsiteOptions,
  OperationResult,
  WorkspaceType,
  WorkspaceState
} from '@/types/workspace';

/**
 * 工作区管理类 - 重构后的简化版本
 * 
 * 🎯 重构说明：
 * 原来的1,584行workspace.ts文件已按照单一职责原则拆分为4个专门的服务类：
 * - WorkspaceCore: 工作区核心CRUD操作 (./workspace/WorkspaceCore.ts)
 * - WorkspaceWebsiteManager: 网站管理和操作 (./workspace/WorkspaceWebsiteManager.ts)
 * - WorkspaceStateManager: 状态和配置管理 (./workspace/WorkspaceStateManager.ts)
 * - WorkspaceUtilities: 工具方法和清理功能 (./workspace/WorkspaceUtilities.ts)
 * 
 * 📋 向后兼容：
 * 本文件保留了原有的主要API接口，确保现有代码无需修改即可使用新的模块结构
 */

// 重新导出拆分后的类，保持向后兼容
export {
  WorkspaceCore,
  WorkspaceWebsiteManager,
  WorkspaceStateManager,
  WorkspaceUtilities,
  IdGenerator
} from './workspace/index';

// 导入拆分后的类
import {
  WorkspaceCore,
  WorkspaceWebsiteManager,
  WorkspaceStateManager,
  WorkspaceUtilities
} from './workspace/index';

/**
 * 工作区网站服务（向后兼容）
 */
export class WorkspaceWebsiteService {
  /**
   * 验证并格式化URL
   */
  static validateAndFormatUrl(url: string): { isValid: boolean; formattedUrl: string } {
    return WorkspaceWebsiteManager.validateAndFormatUrl(url);
  }

  /**
   * 获取网站标题
   */
  static async getWebsiteTitle(url: string): Promise<string> {
    return WorkspaceWebsiteManager.getWebsiteTitle(url);
  }

  /**
   * 获取网站图标
   */
  static async getFavicon(url: string): Promise<string> {
    return WorkspaceWebsiteManager.getFavicon(url);
  }
}

/**
 * 工作区管理器（主要API类）
 */
export class WorkspaceManager {

  // ==================== 工作区核心操作 ====================

  /**
   * 创建新工作区
   */
  static async createWorkspace(options: CreateWorkspaceOptions): Promise<OperationResult<WorkSpace>> {
    return WorkspaceCore.createWorkspace(options);
  }

  /**
   * 更新工作区
   */
  static async updateWorkspace(id: string, options: UpdateWorkspaceOptions): Promise<OperationResult<WorkSpace>> {
    return WorkspaceCore.updateWorkspace(id, options);
  }

  /**
   * 删除工作区
   */
  static async deleteWorkspace(id: string): Promise<OperationResult<void>> {
    return WorkspaceCore.deleteWorkspace(id);
  }

  /**
   * 重新排序工作区
   */
  static async reorderWorkspaces(workspaceIds: string[]): Promise<OperationResult<void>> {
    return WorkspaceCore.reorderWorkspaces(workspaceIds);
  }

  // ==================== 网站管理操作 ====================

  /**
   * 添加网站到工作区
   */
  static async addWebsite(workspaceId: string, url: string, options: AddWebsiteOptions = {}): Promise<OperationResult<Website>> {
    return WorkspaceWebsiteManager.addWebsite(workspaceId, url, options);
  }

  /**
   * 从工作区移除网站
   */
  static async removeWebsite(workspaceId: string, websiteId: string): Promise<OperationResult<void>> {
    return WorkspaceWebsiteManager.removeWebsite(workspaceId, websiteId);
  }

  /**
   * 更新网站信息
   */
  static async updateWebsite(
    workspaceId: string, 
    websiteId: string, 
    updates: { url?: string; title?: string; isPinned?: boolean }
  ): Promise<OperationResult<Website>> {
    return WorkspaceWebsiteManager.updateWebsite(workspaceId, websiteId, updates);
  }

  /**
   * 重新排序工作区中的网站
   */
  static async reorderWebsites(workspaceId: string, websiteIds: string[]): Promise<OperationResult<void>> {
    return WorkspaceWebsiteManager.reorderWebsites(workspaceId, websiteIds);
  }

  // ==================== 状态管理操作 ====================

  /**
   * 设置用户标签页隐藏状态
   */
  static async setUserTabsHiddenState(
    workspaceId: string,
    isHidden: boolean,
    hiddenTabIds: number[] = [],
    pinnedTabIds: number[] = []
  ): Promise<OperationResult<void>> {
    return WorkspaceStateManager.setUserTabsHiddenState(workspaceId, isHidden, hiddenTabIds, pinnedTabIds);
  }

  /**
   * 获取用户标签页隐藏状态
   */
  static async getUserTabsHiddenState(workspaceId: string): Promise<OperationResult<{
    isHidden: boolean;
    hiddenTabIds: number[];
    pinnedTabIds: number[];
  }>> {
    return WorkspaceStateManager.getUserTabsHiddenState(workspaceId);
  }

  /**
   * 清理隐藏的标签页ID
   */
  static async clearHiddenTabIds(workspaceId: string, tabIdsToRemove: number[]): Promise<OperationResult<void>> {
    return WorkspaceStateManager.clearHiddenTabIds(workspaceId, tabIdsToRemove);
  }

  /**
   * 更新工作区类型
   */
  static async updateWorkspaceType(
    workspaceId: string,
    newType: WorkspaceType
  ): Promise<OperationResult<WorkSpace>> {
    return WorkspaceCore.updateWorkspaceType(workspaceId, newType);
  }

  /**
   * 更新工作区位置
   */
  static async updateWorkspacePosition(
    workspaceId: string,
    newPosition: number
  ): Promise<OperationResult<WorkSpace>> {
    return WorkspaceStateManager.updateWorkspacePosition(workspaceId, newPosition);
  }

  /**
   * 更新工作区状态
   */
  static async updateWorkspaceState(
    workspaceId: string,
    newState: WorkspaceState
  ): Promise<OperationResult<WorkSpace>> {
    return WorkspaceStateManager.updateWorkspaceState(workspaceId, newState);
  }

  /**
   * 添加Workona标签页ID
   */
  static async addWorkonaTabId(
    workspaceId: string,
    workonaId: string
  ): Promise<OperationResult<void>> {
    return WorkspaceStateManager.addWorkonaTabId(workspaceId, workonaId);
  }

  /**
   * 移除Workona标签页ID
   */
  static async removeWorkonaTabId(
    workspaceId: string,
    workonaId: string
  ): Promise<OperationResult<void>> {
    return WorkspaceStateManager.removeWorkonaTabId(workspaceId, workonaId);
  }

  // ==================== 工具方法 ====================

  /**
   * 添加当前标签页到工作区（通过Workona ID）
   */
  static async addCurrentTabByWorkonaId(
    workspaceId: string,
    websiteId?: string,
    options: AddWebsiteOptions = {}
  ): Promise<OperationResult<void>> {
    return WorkspaceUtilities.addCurrentTabByWorkonaId(workspaceId, websiteId, options);
  }

  /**
   * 清理临时工作区
   */
  static async cleanupTemporaryWorkspaces(maxAge: number = 24 * 60 * 60 * 1000): Promise<OperationResult<number>> {
    return WorkspaceUtilities.cleanupTemporaryWorkspaces(maxAge);
  }

  /**
   * 将当前窗口的所有标签页添加到工作区（恢复原始功能）
   */
  static async addCurrentTabsToWorkspace(workspaceId: string): Promise<OperationResult<number>> {
    return WorkspaceWebsiteManager.addCurrentTabsToWorkspace(workspaceId);
  }
}
