import {
  TabInfo,
  WorkSpace,
  OperationResult
} from '@/types/workspace';
import { ERROR_CODES } from '../constants';
import { WorkonaTabManager } from '../workonaTabManager';
import { StorageManager } from '../storage';
import { WorkspaceSwitcher, TabClassificationUtils } from '../workspaceSwitcher';

/**
 * 标签页分类服务
 * 职责：处理新标签页的自动分类和Workona ID分配
 * 
 * 🎯 核心职责：
 * 1. 新标签页的自动分类和归属
 * 2. 工作区检测和智能分配
 * 3. Workona ID的创建和映射
 * 4. 标签页状态的实时更新
 */
export class TabClassificationService {
  /**
   * 获取当前应该用于新标签页的工作区
   */
  static async getCurrentWorkspaceForNewTab(): Promise<WorkSpace | null> {
    try {
      // 策略1：优先使用存储中的活跃工作区ID（最准确）
      const activeIdResult = await StorageManager.getActiveWorkspaceId();
      if (activeIdResult.success && activeIdResult.data) {
        const workspacesResult = await StorageManager.getWorkspaces();
        if (workspacesResult.success && workspacesResult.data) {
          const workspace = workspacesResult.data.find(w => w.id === activeIdResult.data);
          if (workspace) {
            console.log(`🎯 [getCurrentWorkspaceForNewTab] 通过存储的活跃工作区ID检测到当前工作区: ${workspace.name}`);
            return workspace;
          }
        }
      }

      // 策略2：检查当前窗口中其他标签页的工作区归属
      const currentTabsResult = await this.getCurrentWindowTabs();
      if (!currentTabsResult.success) {
        console.log(`❌ [getCurrentWorkspaceForNewTab] 获取当前窗口标签页失败:`, currentTabsResult.error);
        return null;
      }

      const currentTabs = currentTabsResult.data!;

      // 查找当前窗口中有 Workona ID 的标签页
      for (const tab of currentTabs) {
        const workonaIdResult = await WorkonaTabManager.getWorkonaIdByChromeId(tab.id);

        if (workonaIdResult.success && workonaIdResult.data) {
          const workspaceId = workonaIdResult.data.split('-')[1];

          // 获取工作区信息
          const workspacesResult = await StorageManager.getWorkspaces();
          if (workspacesResult.success && workspacesResult.data) {
            const workspace = workspacesResult.data.find(w => w.id === workspaceId);
            if (workspace) {
              console.log(`🎯 [getCurrentWorkspaceForNewTab] 通过窗口中其他标签页检测到当前工作区: ${workspace.name}`);
              return workspace;
            }
          }
        }
      }

      // 策略3：通过 WorkspaceSwitcher 检测当前活跃工作区
      try {
        const activeWorkspaceResult = await WorkspaceSwitcher.detectActiveWorkspace();

        if (activeWorkspaceResult.success && activeWorkspaceResult.data) {
          const workspace = activeWorkspaceResult.data;
          console.log(`🎯 [getCurrentWorkspaceForNewTab] 通过 WorkspaceSwitcher 检测到当前工作区: ${workspace.name}`);
          return workspace;
        }
      } catch (error) {
        console.warn(`⚠️ [getCurrentWorkspaceForNewTab] 策略3失败:`, error);
      }

      // 策略4：使用第一个可用的工作区
      try {
        const workspacesResult = await StorageManager.getWorkspaces();
        if (workspacesResult.success && workspacesResult.data && workspacesResult.data.length > 0) {
          const firstWorkspace = workspacesResult.data[0];
          console.log(`🎯 [getCurrentWorkspaceForNewTab] 使用默认工作区: ${firstWorkspace.name}`);
          return firstWorkspace;
        }
      } catch (error) {
        console.warn(`⚠️ [getCurrentWorkspaceForNewTab] 策略4失败:`, error);
      }

      return null;
    } catch (error) {
      console.error('❌ [getCurrentWorkspaceForNewTab] 获取当前工作区失败:', error);
      return null;
    }
  }

  /**
   * 获取当前窗口的标签页
   */
  static async getCurrentWindowTabs(): Promise<OperationResult<TabInfo[]>> {
    try {
      const tabs = await chrome.tabs.query({ currentWindow: true });
      const tabInfos: TabInfo[] = tabs.map(tab => ({
        id: tab.id!,
        url: tab.url || '',
        title: tab.title || '',
        favicon: tab.favIconUrl || '',
        isPinned: tab.pinned,
        isActive: tab.active,
        windowId: tab.windowId,
        index: tab.index
      }));

      return { success: true, data: tabInfos };
    } catch (error) {
      return {
        success: false,
        error: {
          code: ERROR_CODES.TAB_ERROR,
          message: 'Failed to get current window tabs',
          details: error,
        },
      };
    }
  }

  /**
   * 自动为新标签页创建会话临时 Workona ID
   *
   * 🎯 核心功能：
   * 1. 为用户新打开的标签页自动分配 Workona ID
   * 2. 根据当前工作区状态决定标签页归属
   * 3. 支持智能归属机制，处理无工作区状态的情况
   *
   * 📋 处理流程：
   * 1. 过滤系统页面（chrome://、extension://等）
   * 2. 检查标签页是否已有 Workona ID（避免重复处理）
   * 3. 获取当前活跃工作区
   * 4. 根据工作区状态创建相应的 Workona ID 映射
   * 5. 触发用户标签页状态更新
   *
   * @param tabId Chrome 标签页 ID
   * @param url 标签页 URL
   * @returns 操作结果
   */
  static async autoClassifyNewTab(tabId: number, url: string): Promise<OperationResult<void>> {
    try {
      console.log(`🎯 [autoClassifyNewTab] 开始自动分类标签页: ID=${tabId}, URL=${url}`);

      // 第一步：过滤系统页面
      // 系统页面包括：chrome://、chrome-extension://、edge://、about: 等
      if (TabClassificationUtils.isSystemTab(url)) {
        console.log(`🚫 [autoClassifyNewTab] 跳过系统页面: ${url}`);
        return { success: true }; // 系统页面不需要分类
      }

      // 第二步：检查标签页是否已有 Workona ID
      // 避免重复处理已经分类的标签页
      const existingResult = await WorkonaTabManager.getWorkonaIdByChromeId(tabId);

      if (existingResult.success && existingResult.data) {
        console.log(`ℹ️ [autoClassifyNewTab] 标签页已有 Workona ID: ${existingResult.data}，跳过分类`);
        return { success: true }; // 已有映射，无需重复创建
      }

      // 第三步：获取当前活跃工作区
      // 使用多策略检测当前应该归属的工作区
      const activeWorkspace = await this.getCurrentWorkspaceForNewTab();

      // 第四步：处理无工作区状态的情况
      if (!activeWorkspace) {
        console.warn(`⚠️ [autoClassifyNewTab] 无当前工作区，为标签页创建智能归属映射: ${url}`);

        // 🧠 智能工作区归属机制：
        // 当没有检测到活跃工作区时，不直接丢弃标签页，而是创建"等待归属"状态
        // 这样当用户后续切换到工作区时，可以自动将这些标签页归属到相应工作区
        const pendingWorkspaceId = 'pending-assignment';
        const workonaId = WorkonaTabManager.generateWorkonaTabId(pendingWorkspaceId);
        console.log(`🆔 为等待归属的用户标签页生成 Workona ID: ${workonaId}`);

        // 创建等待归属的标签页映射
        const mappingResult = await WorkonaTabManager.createTabIdMapping(
          workonaId,
          tabId,
          pendingWorkspaceId,
          undefined, // 无对应的网站配置ID
          {
            isWorkspaceCore: false, // 标记为等待归属的标签页（非工作区核心标签页）
            source: 'user_opened'   // 标记为用户主动打开的标签页
          }
        );

        if (mappingResult.success) {
          console.log(`✅ [autoClassifyNewTab] 成功为等待归属标签页创建映射: ${url} -> ${workonaId}`);
        } else {
          console.error(`❌ [autoClassifyNewTab] 创建等待归属映射失败:`, mappingResult.error);
        }

        return { success: mappingResult.success, error: mappingResult.error };
      }

      // 第五步：为标签页分配到当前活跃工作区
      console.log(`✅ 找到活跃工作区: ${activeWorkspace.name} (ID: ${activeWorkspace.id})`);

      // 生成工作区专属的 Workona ID
      // 格式：t-{workspaceId}-{uuid}
      const workonaId = WorkonaTabManager.generateWorkonaTabId(activeWorkspace.id);
      console.log(`🆔 生成 Workona ID: ${workonaId}`);

      // 创建标签页到工作区的映射关系
      const mappingResult = await WorkonaTabManager.createTabIdMapping(
        workonaId,
        tabId,
        activeWorkspace.id,
        undefined, // 无对应的网站配置ID（用户临时打开的标签页）
        {
          isWorkspaceCore: false, // 标记为会话临时标签页（非工作区核心标签页）
          source: 'user_opened'   // 标记为用户主动打开的标签页
        }
      );

      // 第六步：处理映射创建结果
      if (mappingResult.success) {
        console.log(`✨ [autoClassifyNewTab] 自动为用户标签页创建会话临时 Workona ID: ${workonaId} (${url})`);

        // 🔍 验证映射是否真的创建成功
        // 这是一个安全检查，确保映射确实被正确存储
        const verifyResult = await WorkonaTabManager.getWorkonaIdByChromeId(tabId);

        if (verifyResult.success && verifyResult.data) {
          console.log(`✅ [autoClassifyNewTab] 映射验证成功: ${verifyResult.data}`);

          // 🔄 触发用户标签页状态更新
          // 通知系统有新的用户标签页被分类，需要更新UI显示
          try {
            console.log(`🔄 触发用户标签页状态更新 (工作区: ${activeWorkspace.id}, 新标签页分类)`);

            // 立即触发实时监测更新
            // 这会更新用户标签页的计数和显示状态
            try {
              const { UserTabsRealTimeMonitor } = await import('./UserTabsRealTimeMonitor');
              await UserTabsRealTimeMonitor.triggerImmediateStateCheck();

              // 强制刷新当前工作区状态
              // 确保工作区的用户标签页列表得到及时更新
              await UserTabsRealTimeMonitor.forceRefreshWorkspaceState(activeWorkspace.id);
            } catch (importError) {
              console.warn('动态导入UserTabsRealTimeMonitor失败:', importError);
            }

            console.log(`✅ 用户标签页状态更新完成 (工作区: ${activeWorkspace.id})`);
          } catch (updateError) {
            console.warn('触发用户标签页状态更新失败:', updateError);
          }
        } else {
          console.error(`❌ [autoClassifyNewTab] 映射验证失败:`, verifyResult);
        }
      } else {
        console.error(`❌ [autoClassifyNewTab] 创建 Workona ID 映射失败:`, mappingResult.error);
      }

      return { success: mappingResult.success, error: mappingResult.error };
    } catch (error) {
      return {
        success: false,
        error: {
          code: ERROR_CODES.TAB_ERROR,
          message: 'Failed to auto-classify new tab',
          details: error,
        },
      };
    }
  }
}
