import { WorkSpace } from '@/types/workspace';
import { StorageManager } from '../storage';

/**
 * 用户标签页实时状态监控器
 * 职责：监控用户标签页的可见性变化并实时更新UI状态
 * 
 * 🎯 核心职责：
 * 1. 实时监控用户标签页状态变化
 * 2. 检测工作区用户标签页的显示/隐藏状态
 * 3. 触发UI状态更新和通知
 * 4. 管理监控生命周期（启动/停止）
 */
export class UserTabsRealTimeMonitor {
  private static isMonitoring = false;
  private static monitoringInterval: NodeJS.Timeout | null = null;
  private static lastStateSnapshot: Map<string, any> = new Map();
  private static readonly MONITOR_INTERVAL = 2000; // 优化为2秒检查间隔，减少日志洪水
  private static pendingUpdate = false; // 防止重复更新

  /**
   * 启动实时监控
   */
  static startMonitoring(): void {
    if (this.isMonitoring) {
      return;
    }

    console.log('🚀 启动用户标签页实时监控');
    this.isMonitoring = true;

    // 立即执行一次检查
    this.checkUserTabsStateChanges();

    // 设置定时检查
    this.monitoringInterval = setInterval(() => {
      this.checkUserTabsStateChanges();
    }, this.MONITOR_INTERVAL);
  }

  /**
   * 停止实时监控
   */
  static stopMonitoring(): void {
    if (!this.isMonitoring) {
      return;
    }

    console.log('⏹️ 停止用户标签页实时监控');
    this.isMonitoring = false;

    if (this.monitoringInterval) {
      clearInterval(this.monitoringInterval);
      this.monitoringInterval = null;
    }

    this.lastStateSnapshot.clear();
  }

  /**
   * 检查用户标签页状态变化（优化版：防重复更新）
   */
  private static async checkUserTabsStateChanges(): Promise<void> {
    // 防止重复更新
    if (this.pendingUpdate) {
      return;
    }

    try {
      this.pendingUpdate = true;

      // 获取当前活跃工作区（使用多种策略）
      const activeWorkspace = await this.getCurrentActiveWorkspace();

      if (!activeWorkspace) {
        return; // 没有活跃工作区，跳过检查
      }

      const workspaceId = activeWorkspace.id;

      // 获取当前工作区的用户标签页状态
      const { WorkspaceUserTabsVisibilityManager } = await import('./WorkspaceUserTabsVisibilityManager');
      const currentState = await WorkspaceUserTabsVisibilityManager.getWorkspaceUserTabsState(workspaceId);

      if (!currentState.success) {
        return;
      }

      const stateData = currentState.data!;
      const stateKey = `workspace_${workspaceId}`;
      const lastState = this.lastStateSnapshot.get(stateKey);

      // 创建当前状态快照
      const currentSnapshot = {
        isHidden: stateData.isHidden,
        hiddenTabsCount: stateData.hiddenTabIds.length,
        totalUserTabs: stateData.totalUserTabs,
        visibleUserTabs: stateData.visibleUserTabs,
        actionType: stateData.actionType,
        timestamp: Date.now()
      };

      // 检查是否有状态变化
      if (lastState && this.hasStateChanged(lastState, currentSnapshot)) {
        console.log(`📊 检测到工作区 "${activeWorkspace.name}" 用户标签页状态变化:`, {
          前: lastState,
          后: currentSnapshot
        });

        // 通知状态变化
        await this.notifyStateChange(workspaceId, currentSnapshot);
      }

      // 更新状态快照
      this.lastStateSnapshot.set(stateKey, currentSnapshot);

    } catch (error) {
      console.error('检查用户标签页状态变化失败:', error);
    } finally {
      this.pendingUpdate = false;
    }
  }

  /**
   * 获取当前活跃工作区（多策略检测）
   */
  private static async getCurrentActiveWorkspace(): Promise<WorkSpace | null> {
    try {
      // 策略1：从存储中获取活跃工作区ID
      const activeIdResult = await StorageManager.getActiveWorkspaceId();
      if (activeIdResult.success && activeIdResult.data) {
        const workspacesResult = await StorageManager.getWorkspaces();
        if (workspacesResult.success && workspacesResult.data) {
          const workspace = workspacesResult.data.find(w => w.id === activeIdResult.data);
          if (workspace) {
            return workspace;
          }
        }
      }

      // 策略2：通过WorkspaceSwitcher检测
      try {
        const { WorkspaceSwitcher } = await import('../workspaceSwitcher');
        const activeWorkspaceResult = await WorkspaceSwitcher.detectActiveWorkspace();
        if (activeWorkspaceResult.success && activeWorkspaceResult.data) {
          return activeWorkspaceResult.data;
        }
      } catch (error) {
        console.warn('通过WorkspaceSwitcher检测活跃工作区失败:', error);
      }

      return null;
    } catch (error) {
      console.error('获取当前活跃工作区失败:', error);
      return null;
    }
  }

  /**
   * 检查状态是否发生变化
   */
  private static hasStateChanged(lastState: any, currentState: any): boolean {
    return (
      lastState.isHidden !== currentState.isHidden ||
      lastState.hiddenTabsCount !== currentState.hiddenTabsCount ||
      lastState.totalUserTabs !== currentState.totalUserTabs ||
      lastState.visibleUserTabs !== currentState.visibleUserTabs ||
      lastState.actionType !== currentState.actionType
    );
  }

  /**
   * 通知状态变化
   */
  private static async notifyStateChange(workspaceId: string, _newState: any): Promise<void> {
    try {
      // 发送Chrome扩展消息通知
      chrome.runtime.sendMessage({
        type: 'USER_TABS_VISIBILITY_CHANGED',
        workspaceId,
        timestamp: Date.now()
      }).catch(() => {
        // 忽略消息发送失败（可能没有监听器）
      });
    } catch (error) {
      console.warn('通知状态变化失败:', error);
    }
  }

  /**
   * 强制刷新指定工作区状态
   */
  static async forceRefreshWorkspaceState(workspaceId: string): Promise<void> {
    try {
      console.log(`🔄 强制刷新工作区状态: ${workspaceId}`);
      
      // 清除该工作区的状态快照，强制下次检查时触发更新
      const stateKey = `workspace_${workspaceId}`;
      this.lastStateSnapshot.delete(stateKey);

      // 立即执行一次状态检查
      if (this.isMonitoring) {
        await this.checkUserTabsStateChanges();
      }

      console.log(`✅ 工作区状态刷新完成: ${workspaceId}`);
    } catch (error) {
      console.error(`❌ 强制刷新工作区状态失败 (${workspaceId}):`, error);
    }
  }

  /**
   * 立即触发状态检查
   */
  static async triggerImmediateStateCheck(): Promise<void> {
    if (!this.isMonitoring) {
      console.log('⚠️ 监控未启动，启动监控并执行状态检查');
      this.startMonitoring();
      return;
    }

    console.log('🔄 立即触发用户标签页状态检查');
    await this.checkUserTabsStateChanges();
  }

  /**
   * 获取监控状态
   */
  static getMonitoringStatus(): { isMonitoring: boolean; workspaceCount: number } {
    return {
      isMonitoring: this.isMonitoring,
      workspaceCount: this.lastStateSnapshot.size
    };
  }
}
