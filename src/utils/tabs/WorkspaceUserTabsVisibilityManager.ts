import { OperationResult } from '@/types/workspace';
import { ERROR_CODES } from '../constants';
import { StorageManager } from '../storage';
import { WorkonaTabManager } from '../workonaTabManager';
import { TabClassificationUtils } from '../tabs';

/**
 * 工作区级别的用户标签页可见性管理器
 * 职责：管理特定工作区中的用户标签页隐藏和显示
 * 
 * 🎯 核心职责：
 * 1. 工作区用户标签页状态查询和管理
 * 2. 批量隐藏/显示用户标签页
 * 3. 标签页可见性状态持久化
 * 4. 系统保护机制（防止隐藏重要标签页）
 * 
 * 📋 功能特性：
 * - 支持"继续隐藏"模式
 * - 智能状态检测和缓存
 * - 系统标签页保护
 * - 批量操作优化
 */
export class WorkspaceUserTabsVisibilityManager {

  /**
   * 获取工作区用户标签页状态
   */
  static async getWorkspaceUserTabsState(workspaceId: string): Promise<OperationResult<{
    isHidden: boolean;
    hiddenTabIds: number[];
    pinnedTabIds: number[];
    totalUserTabs: number;
    visibleUserTabs: number;
    canContinueHiding: boolean;
    actionType: 'hide' | 'continue_hide' | 'show';
  }>> {
    try {
      // 获取工作区信息
      const workspaceResult = await StorageManager.getWorkspace(workspaceId);
      if (!workspaceResult.success) {
        return { success: false, error: workspaceResult.error };
      }

      const workspace = workspaceResult.data!;

      // 获取当前窗口的所有标签页
      const { TabClassificationService } = await import('./TabClassificationService');
      const currentTabsResult = await TabClassificationService.getCurrentWindowTabs();
      if (!currentTabsResult.success) {
        return { success: false, error: currentTabsResult.error };
      }

      const allTabs = currentTabsResult.data!;

      // 过滤出用户标签页（排除系统页面和工作区核心标签页）
      const userTabs = [];
      const hiddenTabIds = [];
      const pinnedTabIds = [];

      for (const tab of allTabs) {
        // 跳过系统标签页
        if (TabClassificationUtils.isSystemTab(tab.url)) {
          continue;
        }

        // 检查是否为工作区管理的标签页
        const workonaIdResult = await WorkonaTabManager.getWorkonaIdByChromeId(tab.id);
        if (workonaIdResult.success && workonaIdResult.data) {
          // 检查是否属于当前工作区
          const tabWorkspaceId = workonaIdResult.data.split('-')[1];
          if (tabWorkspaceId === workspaceId) {
            // 检查是否为核心标签页
            const metadataResult = await WorkonaTabManager.getTabMetadata(workonaIdResult.data);
            if (metadataResult.success && metadataResult.data) {
              const { isWorkspaceCore } = metadataResult.data;
              if (!isWorkspaceCore) {
                // 这是用户标签页
                userTabs.push(tab);
                
                // 检查可见性状态
                if (!tab.isActive && tab.windowId !== (await chrome.windows.getCurrent()).id) {
                  hiddenTabIds.push(tab.id);
                }

                // 检查固定状态
                if (tab.isPinned) {
                  pinnedTabIds.push(tab.id);
                }
              }
            }
          }
        } else {
          // 无Workona ID的标签页也可能是用户标签页
          userTabs.push(tab);
          
          // 检查可见性状态
          if (!tab.isActive && tab.windowId !== (await chrome.windows.getCurrent()).id) {
            hiddenTabIds.push(tab.id);
          }

          // 检查固定状态
          if (tab.isPinned) {
            pinnedTabIds.push(tab.id);
          }
        }
      }

      const totalUserTabs = userTabs.length;
      const visibleUserTabs = totalUserTabs - hiddenTabIds.length;
      const isHidden = hiddenTabIds.length > 0;
      const canContinueHiding = visibleUserTabs > 0;

      // 确定操作类型
      let actionType: 'hide' | 'continue_hide' | 'show';
      if (isHidden && canContinueHiding) {
        actionType = 'continue_hide';
      } else if (isHidden) {
        actionType = 'show';
      } else {
        actionType = 'hide';
      }

      return {
        success: true,
        data: {
          isHidden,
          hiddenTabIds,
          pinnedTabIds,
          totalUserTabs,
          visibleUserTabs,
          canContinueHiding,
          actionType
        }
      };
    } catch (error) {
      return {
        success: false,
        error: {
          code: ERROR_CODES.TAB_ERROR,
          message: 'Failed to get workspace user tabs state',
          details: error,
        },
      };
    }
  }

  /**
   * 切换工作区用户标签页可见性
   */
  static async toggleWorkspaceUserTabsVisibility(workspaceId: string): Promise<OperationResult<{
    action: 'hidden' | 'shown' | 'continue_hidden';
    affectedTabsCount: number;
    totalUserTabs: number;
  }>> {
    try {
      // 获取当前状态
      const stateResult = await this.getWorkspaceUserTabsState(workspaceId);
      if (!stateResult.success) {
        return { success: false, error: stateResult.error };
      }

      const state = stateResult.data!;

      // 根据当前状态决定操作
      if (state.actionType === 'show') {
        // 显示所有隐藏的用户标签页
        return await this.showWorkspaceUserTabs(workspaceId);
      } else if (state.actionType === 'continue_hide') {
        // 继续隐藏剩余的用户标签页
        return await this.continueHideWorkspaceUserTabs(workspaceId);
      } else {
        // 隐藏用户标签页
        return await this.hideWorkspaceUserTabs(workspaceId);
      }
    } catch (error) {
      return {
        success: false,
        error: {
          code: ERROR_CODES.TAB_ERROR,
          message: 'Failed to toggle workspace user tabs visibility',
          details: error,
        },
      };
    }
  }

  /**
   * 隐藏工作区用户标签页
   */
  static async hideWorkspaceUserTabs(workspaceId: string): Promise<OperationResult<{
    action: 'hidden';
    affectedTabsCount: number;
    totalUserTabs: number;
  }>> {
    try {
      const stateResult = await this.getWorkspaceUserTabsState(workspaceId);
      if (!stateResult.success) {
        return { success: false, error: stateResult.error };
      }

      const state = stateResult.data!;

      // 获取需要隐藏的标签页（可见的用户标签页）
      const { TabClassificationService } = await import('./TabClassificationService');
      const currentTabsResult = await TabClassificationService.getCurrentWindowTabs();
      if (!currentTabsResult.success) {
        return { success: false, error: currentTabsResult.error };
      }

      const allTabs = currentTabsResult.data!;
      const tabsToHide = [];

      for (const tab of allTabs) {
        // 跳过系统标签页
        if (TabClassificationUtils.isSystemTab(tab.url)) {
          continue;
        }

        // 跳过当前活跃标签页
        if (tab.isActive) {
          continue;
        }

        // 检查是否为用户标签页
        const workonaIdResult = await WorkonaTabManager.getWorkonaIdByChromeId(tab.id);
        if (workonaIdResult.success && workonaIdResult.data) {
          const tabWorkspaceId = workonaIdResult.data.split('-')[1];
          if (tabWorkspaceId === workspaceId) {
            const metadataResult = await WorkonaTabManager.getTabMetadata(workonaIdResult.data);
            if (metadataResult.success && metadataResult.data) {
              const { isWorkspaceCore } = metadataResult.data;
              if (!isWorkspaceCore) {
                tabsToHide.push(tab.id);
              }
            }
          }
        } else {
          // 无Workona ID的标签页也可能是用户标签页
          tabsToHide.push(tab.id);
        }
      }

      // 执行隐藏操作（移动到后台窗口）
      if (tabsToHide.length > 0) {
        // 创建或获取后台窗口
        const backgroundWindow = await chrome.windows.create({
          url: 'about:blank',
          focused: false,
          state: 'minimized'
        });

        // 移动标签页到后台窗口
        await chrome.tabs.move(tabsToHide, {
          windowId: backgroundWindow.id!,
          index: -1
        });
      }

      return {
        success: true,
        data: {
          action: 'hidden',
          affectedTabsCount: tabsToHide.length,
          totalUserTabs: state.totalUserTabs
        }
      };
    } catch (error) {
      return {
        success: false,
        error: {
          code: ERROR_CODES.TAB_ERROR,
          message: 'Failed to hide workspace user tabs',
          details: error,
        },
      };
    }
  }

  /**
   * 继续隐藏工作区用户标签页
   */
  static async continueHideWorkspaceUserTabs(workspaceId: string): Promise<OperationResult<{
    action: 'continue_hidden';
    affectedTabsCount: number;
    totalUserTabs: number;
  }>> {
    try {
      // 复用隐藏逻辑
      const hideResult = await this.hideWorkspaceUserTabs(workspaceId);
      if (!hideResult.success) {
        return { success: false, error: hideResult.error };
      }

      return {
        success: true,
        data: {
          action: 'continue_hidden',
          affectedTabsCount: hideResult.data.affectedTabsCount,
          totalUserTabs: hideResult.data.totalUserTabs
        }
      };
    } catch (error) {
      return {
        success: false,
        error: {
          code: ERROR_CODES.TAB_ERROR,
          message: 'Failed to continue hide workspace user tabs',
          details: error,
        },
      };
    }
  }

  /**
   * 显示工作区用户标签页
   */
  static async showWorkspaceUserTabs(workspaceId: string): Promise<OperationResult<{
    action: 'shown';
    affectedTabsCount: number;
    totalUserTabs: number;
  }>> {
    try {
      // 获取所有窗口中的标签页
      const allWindows = await chrome.windows.getAll({ populate: true });
      const currentWindow = await chrome.windows.getCurrent();
      const tabsToShow = [];

      for (const window of allWindows) {
        if (window.id === currentWindow.id || !window.tabs) {
          continue;
        }

        for (const tab of window.tabs) {
          if (!tab.id || !tab.url) continue;

          // 跳过系统标签页
          if (TabClassificationUtils.isSystemTab(tab.url)) {
            continue;
          }

          // 检查是否为当前工作区的用户标签页
          const workonaIdResult = await WorkonaTabManager.getWorkonaIdByChromeId(tab.id);
          if (workonaIdResult.success && workonaIdResult.data) {
            const tabWorkspaceId = workonaIdResult.data.split('-')[1];
            if (tabWorkspaceId === workspaceId) {
              const metadataResult = await WorkonaTabManager.getTabMetadata(workonaIdResult.data);
              if (metadataResult.success && metadataResult.data) {
                const { isWorkspaceCore } = metadataResult.data;
                if (!isWorkspaceCore) {
                  tabsToShow.push(tab.id);
                }
              }
            }
          }
        }
      }

      // 移动标签页回当前窗口
      if (tabsToShow.length > 0) {
        await chrome.tabs.move(tabsToShow, {
          windowId: currentWindow.id!,
          index: -1
        });
      }

      const stateResult = await this.getWorkspaceUserTabsState(workspaceId);
      const totalUserTabs = stateResult.success ? stateResult.data!.totalUserTabs : 0;

      return {
        success: true,
        data: {
          action: 'shown',
          affectedTabsCount: tabsToShow.length,
          totalUserTabs
        }
      };
    } catch (error) {
      return {
        success: false,
        error: {
          code: ERROR_CODES.TAB_ERROR,
          message: 'Failed to show workspace user tabs',
          details: error,
        },
      };
    }
  }
}
