import { Settings } from '@/types/workspace';

/**
 * 存储键名常量
 */
export const STORAGE_KEYS = {
  WORKSPACES: 'workspaces',
  SETTINGS: 'settings',
  ACTIVE_WORKSPACE_ID: 'activeWorkspaceId',
  LAST_ACTIVE_WORKSPACE_IDS: 'lastActiveWorkspaceIds',
} as const;

/**
 * Workona 风格存储键名常量
 */
export const WORKONA_STORAGE_KEYS = {
  TAB_ID_MAPPINGS: 'workonaTabIdMappings',
  LOCAL_OPEN_WORKSPACES: 'localOpenWorkspaces',
  TAB_GROUPS: 'tabGroups',
  WORKSPACE_SESSIONS: 'workspaceSessions',
  GLOBAL_WORKSPACE_WINDOW_ID: 'globalWorkspaceWindowId',
  DATA_VERSION: 'workonaDataVersion',
  MIGRATION_BACKUP: 'migrationBackup',
} as const;

/**
 * 默认设置
 */
export const DEFAULT_SETTINGS: Settings = {
  autoCloseOtherTabs: true,
  preserveUserOpenedTabs: false,
  defaultWorkspaceOnStartup: '',
  sidebarWidth: 320,
  theme: 'dark',
  showFavicons: true,
  confirmBeforeDelete: true,
  maxRecentWorkspaces: 5,
};

/**
 * 工作区颜色预设
 */
export const WORKSPACE_COLORS = [
  '#3b82f6', // blue
  '#10b981', // emerald
  '#f59e0b', // amber
  '#ef4444', // red
  '#8b5cf6', // violet
  '#06b6d4', // cyan
  '#84cc16', // lime
  '#f97316', // orange
  '#ec4899', // pink
  '#6366f1', // indigo
] as const;

/**
 * 工作区图标预设
 */
export const WORKSPACE_ICONS = [
  '🚀', '💼', '🔬', '🎨', '📊', '🛠️', '📚', '💡', '🎯', '⚡',
  '🌟', '🔥', '💎', '🎪', '🎭', '🎨', '🎵', '🎮', '🏆', '🎊',
  '📱', '💻', '🖥️', '⌨️', '🖱️', '🖨️', '📷', '📹', '🎥', '📺',
  '🔍', '🔎', '🔬', '🔭', '📡', '🛰️', '🚁', '✈️', '🛸',
] as const;



/**
 * 错误代码常量
 */
export const ERROR_CODES = {
  WORKSPACE_NOT_FOUND: 'WORKSPACE_NOT_FOUND',
  WEBSITE_NOT_FOUND: 'WEBSITE_NOT_FOUND',
  STORAGE_ERROR: 'STORAGE_ERROR',
  TAB_ERROR: 'TAB_ERROR',
  WINDOW_ERROR: 'WINDOW_ERROR',
  PERMISSION_DENIED: 'PERMISSION_DENIED',
  INVALID_URL: 'INVALID_URL',
  DUPLICATE_WORKSPACE: 'DUPLICATE_WORKSPACE',
  DUPLICATE_WEBSITE: 'DUPLICATE_WEBSITE',
} as const;

/**
 * 事件名称常量
 */
export const EVENT_NAMES = {
  WORKSPACE_CHANGED: 'workspace-changed',
  SETTINGS_CHANGED: 'settings-changed',
  TAB_UPDATED: 'tab-updated',
} as const;

/**
 * 快捷键命令常量
 */
export const COMMANDS = {
  SWITCH_WORKSPACE_1: 'switch-workspace-1',
  SWITCH_WORKSPACE_2: 'switch-workspace-2',
  SWITCH_WORKSPACE_3: 'switch-workspace-3',
  TOGGLE_SIDEPANEL: 'toggle-sidepanel',
} as const;

/**
 * URL验证正则表达式
 */
export const URL_REGEX = /^https?:\/\/(www\.)?[-a-zA-Z0-9@:%._\+~#=]{1,256}\.[a-zA-Z0-9()]{1,6}\b([-a-zA-Z0-9()@:%_\+.~#?&//=]*)$/;

/**
 * 默认网站图标
 */
export const DEFAULT_FAVICON = 'data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><circle cx="12" cy="12" r="10"/><path d="m9 12 2 2 4-4"/></svg>';

/**
 * 侧边栏最小/最大宽度
 */
export const SIDEBAR_WIDTH = {
  MIN: 280,
  MAX: 500,
  DEFAULT: 320,
} as const;
