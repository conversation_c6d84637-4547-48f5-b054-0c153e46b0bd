/**
 * 工作区管理模块统一导出
 * 
 * 🎯 模块职责分离：
 * - WorkspaceCore: 工作区核心CRUD操作
 * - WorkspaceWebsiteManager: 网站管理和操作
 * - WorkspaceStateManager: 状态和配置管理
 * - WorkspaceUtilities: 工具方法和清理功能
 * - IdGenerator: ID生成器工具类
 * 
 * 📋 重构说明：
 * 原来的1,584行workspace.ts文件已按照单一职责原则拆分为4个专门的服务类
 * 每个类只负责一个核心功能，提高了代码的可维护性和可测试性
 */

// 工作区核心CRUD操作
export { WorkspaceCore } from './WorkspaceCore';

// 网站管理
export { WorkspaceWebsiteManager } from './WorkspaceWebsiteManager';

// 状态管理
export { WorkspaceStateManager } from './WorkspaceStateManager';

// 工具方法和清理
export { WorkspaceUtilities, IdGenerator } from './WorkspaceUtilities';
