import {
  WorkSpace,
  Website,
  AddWebsiteOptions,
  OperationResult
} from '@/types/workspace';
import { StorageManager } from '../storage';
import { ERROR_CODES, DEFAULT_FAVICON } from '../constants';

/**
 * 工作区网站管理器
 * 职责：处理工作区中网站的增删改查操作
 * 
 * 🎯 核心职责：
 * 1. 网站的添加和验证
 * 2. 网站的删除和清理
 * 3. 网站的更新和修改
 * 4. 网站的重新排序
 * 5. 网站URL验证和格式化
 * 6. 网站元数据获取（标题、图标）
 */
export class WorkspaceWebsiteManager {
  /**
   * 辅助方法：更新工作区并保存
   */
  private static async saveUpdatedWorkspace(updatedWorkspace: WorkSpace): Promise<OperationResult<void>> {
    try {
      const workspacesResult = await StorageManager.getWorkspaces();
      if (!workspacesResult.success) {
        return { success: false, error: workspacesResult.error };
      }

      const workspaces = workspacesResult.data!;
      const workspaceIndex = workspaces.findIndex(w => w.id === updatedWorkspace.id);
      if (workspaceIndex !== -1) {
        workspaces[workspaceIndex] = updatedWorkspace;
      }

      return await StorageManager.saveWorkspaces(workspaces);
    } catch (error) {
      return {
        success: false,
        error: {
          code: ERROR_CODES.STORAGE_ERROR,
          message: 'Failed to save updated workspace',
          details: error,
        },
      };
    }
  }
  /**
   * 验证并格式化URL
   */
  static validateAndFormatUrl(url: string): { isValid: boolean; formattedUrl: string } {
    try {
      // 如果URL不包含协议，默认添加https://
      let formattedUrl = url.trim();
      if (!formattedUrl.startsWith('http://') && !formattedUrl.startsWith('https://')) {
        formattedUrl = `https://${formattedUrl}`;
      }

      // 验证URL格式
      const urlObj = new URL(formattedUrl);
      return {
        isValid: true,
        formattedUrl: urlObj.href,
      };
    } catch {
      return {
        isValid: false,
        formattedUrl: url,
      };
    }
  }

  /**
   * 获取网站标题
   */
  static async getWebsiteTitle(url: string): Promise<string> {
    try {
      // 这里可以实现获取网站标题的逻辑
      // 目前返回从URL提取的域名作为标题
      const urlObj = new URL(url);
      return urlObj.hostname.replace('www.', '');
    } catch {
      return 'Unknown Website';
    }
  }

  /**
   * 获取网站图标
   */
  static async getFavicon(url: string): Promise<string> {
    try {
      const urlObj = new URL(url);
      return `${urlObj.protocol}//${urlObj.hostname}/favicon.ico`;
    } catch {
      return DEFAULT_FAVICON;
    }
  }

  /**
   * 添加网站到工作区
   */
  static async addWebsite(workspaceId: string, url: string, options: AddWebsiteOptions = {}): Promise<OperationResult<Website>> {
    try {
      console.log(`🌐 添加网站到工作区 ${workspaceId}:`, url);

      // 验证URL
      const { isValid, formattedUrl } = this.validateAndFormatUrl(url);
      if (!isValid) {
        return {
          success: false,
          error: {
            code: ERROR_CODES.INVALID_URL,
            message: 'Invalid URL format',
          },
        };
      }

      // 获取工作区
      const workspaceResult = await StorageManager.getWorkspace(workspaceId);
      if (!workspaceResult.success) {
        return { success: false, error: workspaceResult.error };
      }

      const workspace = workspaceResult.data!;

      // 检查URL是否已存在
      const existingWebsite = workspace.websites.find(w => w.url === formattedUrl);
      if (existingWebsite) {
        return {
          success: false,
          error: {
            code: ERROR_CODES.DUPLICATE_WEBSITE,
            message: 'Website already exists in workspace',
          },
        };
      }

      // 生成网站ID
      const { IdGenerator } = await import('./WorkspaceUtilities');
      const websiteId = IdGenerator.generateWebsiteId();

      // 获取网站元数据
      const title = options.title || await this.getWebsiteTitle(formattedUrl);
      const favicon = options.favicon || await this.getFavicon(formattedUrl);

      // 创建网站对象
      const website: Website = {
        id: websiteId,
        url: formattedUrl,
        title,
        favicon,
        isPinned: options.isPinned || false,
        addedAt: Date.now(),
        order: workspace.websites.length,
      };

      // 添加到工作区
      workspace.websites.push(website);
      workspace.updatedAt = Date.now();

      // 保存工作区
      const saveResult = await this.saveUpdatedWorkspace(workspace);
      if (!saveResult.success) {
        return { success: false, error: saveResult.error };
      }

      console.log(`✅ 网站添加成功: ${title}`);
      return { success: true, data: website };
    } catch (error) {
      return {
        success: false,
        error: {
          code: ERROR_CODES.STORAGE_ERROR,
          message: 'Failed to add website',
          details: error,
        },
      };
    }
  }

  /**
   * 从工作区移除网站
   */
  static async removeWebsite(workspaceId: string, websiteId: string): Promise<OperationResult<void>> {
    try {
      console.log(`🗑️ 从工作区 ${workspaceId} 移除网站: ${websiteId}`);

      // 获取工作区
      const workspaceResult = await StorageManager.getWorkspace(workspaceId);
      if (!workspaceResult.success) {
        return { success: false, error: workspaceResult.error };
      }

      const workspace = workspaceResult.data!;

      // 查找网站
      const websiteIndex = workspace.websites.findIndex(w => w.id === websiteId);
      if (websiteIndex === -1) {
        return {
          success: false,
          error: {
            code: ERROR_CODES.WEBSITE_NOT_FOUND,
            message: 'Website not found in workspace',
          },
        };
      }

      const website = workspace.websites[websiteIndex];

      // 清理相关的Workona标签页映射
      try {
        const { WorkonaTabManager } = await import('../workonaTabManager');
        
        // 查找与此网站相关的Workona标签页
        for (const workonaId of workspace.workonaTabIds || []) {
          const metadataResult = await WorkonaTabManager.getTabMetadata(workonaId);
          if (metadataResult.success && metadataResult.data) {
            // 检查是否与要删除的网站相关（这里可以添加更复杂的逻辑）
            // 将工作区核心标签页降级为会话标签页
            await WorkonaTabManager.updateTabMetadata(workonaId, {
              isWorkspaceCore: false
            });
            console.log(`🔄 标签页降级为会话标签页: ${workonaId}`);
          }
        }
      } catch (error) {
        console.warn('清理Workona标签页映射失败:', error);
      }

      // 从工作区移除网站
      workspace.websites.splice(websiteIndex, 1);
      workspace.updatedAt = Date.now();

      // 重新排序剩余网站
      workspace.websites.forEach((w, index) => {
        w.order = index;
      });

      // 保存工作区
      const saveResult = await this.saveUpdatedWorkspace(workspace);
      if (!saveResult.success) {
        return { success: false, error: saveResult.error };
      }

      console.log(`✅ 网站移除成功: ${website.title}`);
      return { success: true };
    } catch (error) {
      return {
        success: false,
        error: {
          code: ERROR_CODES.STORAGE_ERROR,
          message: 'Failed to remove website',
          details: error,
        },
      };
    }
  }

  /**
   * 更新网站信息
   */
  static async updateWebsite(
    workspaceId: string, 
    websiteId: string, 
    updates: { url?: string; title?: string; isPinned?: boolean }
  ): Promise<OperationResult<Website>> {
    try {
      console.log(`🔄 更新工作区 ${workspaceId} 中的网站 ${websiteId}:`, updates);

      // 获取工作区
      const workspaceResult = await StorageManager.getWorkspace(workspaceId);
      if (!workspaceResult.success) {
        return { success: false, error: workspaceResult.error };
      }

      const workspace = workspaceResult.data!;

      // 查找网站
      const websiteIndex = workspace.websites.findIndex(w => w.id === websiteId);
      if (websiteIndex === -1) {
        return {
          success: false,
          error: {
            code: ERROR_CODES.WEBSITE_NOT_FOUND,
            message: 'Website not found in workspace',
          },
        };
      }

      const website = workspace.websites[websiteIndex];

      // 如果更新URL，验证新URL
      if (updates.url) {
        const { isValid, formattedUrl } = this.validateAndFormatUrl(updates.url);
        if (!isValid) {
          return {
            success: false,
            error: {
              code: ERROR_CODES.INVALID_URL,
              message: 'Invalid URL format',
            },
          };
        }

        // 检查新URL是否与其他网站重复
        const existingWebsite = workspace.websites.find(w => w.url === formattedUrl && w.id !== websiteId);
        if (existingWebsite) {
          return {
            success: false,
            error: {
              code: ERROR_CODES.DUPLICATE_WEBSITE,
              message: 'Website with this URL already exists in workspace',
            },
          };
        }

        updates.url = formattedUrl;
      }

      // 更新网站属性
      const updatedWebsite: Website = {
        ...website,
        url: updates.url || website.url,
        title: updates.title || website.title,
        isPinned: updates.isPinned ?? website.isPinned,
      };

      // 更新工作区中的网站
      workspace.websites[websiteIndex] = updatedWebsite;
      workspace.updatedAt = Date.now();

      // 保存工作区
      const saveResult = await this.saveUpdatedWorkspace(workspace);
      if (!saveResult.success) {
        return { success: false, error: saveResult.error };
      }

      console.log(`✅ 网站更新成功: ${updatedWebsite.title}`);
      return { success: true, data: updatedWebsite };
    } catch (error) {
      return {
        success: false,
        error: {
          code: ERROR_CODES.STORAGE_ERROR,
          message: 'Failed to update website',
          details: error,
        },
      };
    }
  }

  /**
   * 重新排序工作区中的网站
   */
  static async reorderWebsites(workspaceId: string, websiteIds: string[]): Promise<OperationResult<void>> {
    try {
      console.log(`🔄 重新排序工作区 ${workspaceId} 中的网站:`, websiteIds);

      // 获取工作区
      const workspaceResult = await StorageManager.getWorkspace(workspaceId);
      if (!workspaceResult.success) {
        return { success: false, error: workspaceResult.error };
      }

      const workspace = workspaceResult.data!;

      // 验证所有网站ID都存在
      const existingIds = workspace.websites.map(w => w.id);
      const missingIds = websiteIds.filter(id => !existingIds.includes(id));
      if (missingIds.length > 0) {
        return {
          success: false,
          error: {
            code: ERROR_CODES.WEBSITE_NOT_FOUND,
            message: `Websites not found: ${missingIds.join(', ')}`,
          },
        };
      }

      // 重新排序网站
      const reorderedWebsites = websiteIds.map((id, index) => {
        const website = workspace.websites.find(w => w.id === id)!;
        return {
          ...website,
          order: index,
        };
      });

      // 更新工作区
      workspace.websites = reorderedWebsites;
      workspace.updatedAt = Date.now();

      // 保存工作区
      const saveResult = await this.saveUpdatedWorkspace(workspace);
      if (!saveResult.success) {
        return { success: false, error: saveResult.error };
      }

      console.log(`✅ 网站重新排序成功`);
      return { success: true };
    } catch (error) {
      return {
        success: false,
        error: {
          code: ERROR_CODES.STORAGE_ERROR,
          message: 'Failed to reorder websites',
          details: error,
        },
      };
    }
  }
}
