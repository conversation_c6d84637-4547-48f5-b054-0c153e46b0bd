import {
  WorkSpace,
  OperationResult,
  WorkspaceType,
  AddWebsiteOptions
} from '@/types/workspace';
import { StorageManager } from '../storage';
import { ERROR_CODES } from '../constants';

/**
 * ID生成器工具类
 * 职责：生成各种类型的唯一标识符
 */
export class IdGenerator {
  /**
   * 生成工作区ID（支持 Workona 风格）
   */
  static generateWorkspaceId(type: WorkspaceType = 'saved'): string {
    const timestamp = Date.now();
    const randomStr = Math.random().toString(36).substr(2, 9);

    // Workona 风格：临时工作区使用 temp_ 前缀
    if (type === 'temp') {
      return `temp_${timestamp}_${randomStr}`;
    }

    // 保持现有格式用于已保存和未保存的工作区
    return `ws_${timestamp}_${randomStr}`;
  }

  /**
   * 生成网站ID
   */
  static generateWebsiteId(): string {
    return `site_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }

  /**
   * 生成会话ID
   */
  static generateSessionId(): string {
    return `session_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }
}

/**
 * 工作区工具类
 * 职责：提供工作区相关的工具方法和清理功能
 * 
 * 🎯 核心职责：
 * 1. 临时工作区的清理
 * 2. 工作区数据的验证和修复
 * 3. 批量操作的工具方法
 * 4. 工作区统计和分析
 */
export class WorkspaceUtilities {
  /**
   * 验证URL格式
   */
  private static isValidUrl(url: string): boolean {
    const { WorkspaceWebsiteManager } = require('./WorkspaceWebsiteManager');
    const { isValid } = WorkspaceWebsiteManager.validateAndFormatUrl(url);
    return isValid;
  }

  /**
   * 清理临时工作区
   */
  static async cleanupTemporaryWorkspaces(maxAge: number = 24 * 60 * 60 * 1000): Promise<OperationResult<number>> {
    try {
      console.log(`🧹 清理超过 ${maxAge / (60 * 60 * 1000)} 小时的临时工作区`);

      // 获取所有工作区
      const workspacesResult = await StorageManager.getWorkspaces();
      if (!workspacesResult.success) {
        return { success: false, error: workspacesResult.error };
      }

      const workspaces = workspacesResult.data!;
      const now = Date.now();
      let cleanedCount = 0;

      // 筛选需要清理的临时工作区
      const workspacesToKeep = workspaces.filter(workspace => {
        // 保留非临时工作区
        if (workspace.type !== 'temp') {
          return true;
        }

        // 检查临时工作区是否过期
        const age = now - workspace.createdAt;
        if (age > maxAge) {
          console.log(`🗑️ 清理过期临时工作区: ${workspace.name} (创建于 ${new Date(workspace.createdAt).toLocaleString()})`);
          cleanedCount++;
          return false;
        }

        return true;
      });

      // 如果有工作区被清理，保存更新后的列表
      if (cleanedCount > 0) {
        const saveResult = await StorageManager.saveWorkspaces(workspacesToKeep);
        if (!saveResult.success) {
          return { success: false, error: saveResult.error };
        }
      }

      console.log(`✅ 临时工作区清理完成，清理了 ${cleanedCount} 个过期工作区`);
      return { success: true, data: cleanedCount };
    } catch (error) {
      return {
        success: false,
        error: {
          code: ERROR_CODES.STORAGE_ERROR,
          message: 'Failed to cleanup temporary workspaces',
          details: error,
        },
      };
    }
  }

  /**
   * 添加当前标签页到工作区（通过Workona ID）
   */
  static async addCurrentTabByWorkonaId(
    workspaceId: string,
    websiteId?: string,
    options: AddWebsiteOptions = {}
  ): Promise<OperationResult<void>> {
    try {
      console.log(`📌 通过Workona ID添加当前标签页到工作区: ${workspaceId}`);

      // 获取当前活跃标签页
      const { TabManager } = await import('../tabs');
      const activeTabResult = await TabManager.getActiveTab();
      if (!activeTabResult.success) {
        return { success: false, error: activeTabResult.error };
      }

      const activeTab = activeTabResult.data!;

      // 验证URL
      if (!this.isValidUrl(activeTab.url)) {
        return {
          success: false,
          error: {
            code: ERROR_CODES.INVALID_URL,
            message: 'Current tab URL is not valid',
          },
        };
      }

      // 获取工作区
      const workspaceResult = await StorageManager.getWorkspace(workspaceId);
      if (!workspaceResult.success) {
        return { success: false, error: workspaceResult.error };
      }

      const workspace = workspaceResult.data!;

      // 检查URL是否已存在于工作区中
      const existingWebsite = workspace.websites.find(w => w.url === activeTab.url);
      if (existingWebsite) {
        // 如果网站已存在，将现有标签页提升为工作区核心标签页
        await this.promoteExistingTabToWorkspaceCore(workspaceId, existingWebsite.id, activeTab.url);
        console.log(`✅ 现有网站的标签页已提升为工作区核心标签页: ${existingWebsite.title}`);
        return { success: true };
      }

      // 添加新网站到工作区
      const { WorkspaceWebsiteManager } = await import('./WorkspaceWebsiteManager');
      const addWebsiteResult = await WorkspaceWebsiteManager.addWebsite(
        workspaceId,
        activeTab.url,
        {
          title: options.title || activeTab.title,
          favicon: options.favicon || activeTab.favicon,
          isPinned: options.isPinned || activeTab.isPinned,
        }
      );

      if (!addWebsiteResult.success) {
        return { success: false, error: addWebsiteResult.error };
      }

      const newWebsite = addWebsiteResult.data!;

      // 为当前标签页创建Workona ID映射
      const { WorkonaTabManager } = await import('../workonaTabManager');
      const workonaId = WorkonaTabManager.generateWorkonaTabId(workspaceId);

      const mappingResult = await WorkonaTabManager.createTabIdMapping(
        workonaId,
        activeTab.id,
        workspaceId,
        websiteId || newWebsite.id,
        {
          isWorkspaceCore: true,
          source: 'user_added'
        }
      );

      if (!mappingResult.success) {
        console.warn('创建Workona ID映射失败:', mappingResult.error);
      } else {
        // 将Workona ID添加到工作区
        const { WorkspaceStateManager } = await import('./WorkspaceStateManager');
        await WorkspaceStateManager.addWorkonaTabId(workspaceId, workonaId);
        console.log(`✅ 为标签页创建了Workona ID映射: ${workonaId}`);
      }

      console.log(`✅ 当前标签页已添加到工作区: ${newWebsite.title}`);
      return { success: true };
    } catch (error) {
      return {
        success: false,
        error: {
          code: ERROR_CODES.STORAGE_ERROR,
          message: 'Failed to add current tab by Workona ID',
          details: error,
        },
      };
    }
  }

  /**
   * 将现有标签页提升为工作区核心标签页
   */
  private static async promoteExistingTabToWorkspaceCore(workspaceId: string, websiteId: string, url: string): Promise<void> {
    try {
      console.log(`🔄 将现有标签页提升为工作区核心标签页: ${url}`);

      // 查找匹配URL的标签页
      const tabs = await chrome.tabs.query({ url: url + '*' });
      if (tabs.length === 0) {
        console.log(`ℹ️ 未找到匹配的标签页: ${url}`);
        return;
      }

      const { WorkonaTabManager } = await import('../workonaTabManager');
      const { WorkspaceStateManager } = await import('./WorkspaceStateManager');

      // 为每个匹配的标签页创建或更新Workona ID映射
      for (const tab of tabs) {
        if (!tab.id) continue;

        // 检查是否已有Workona ID
        const existingResult = await WorkonaTabManager.getWorkonaIdByChromeId(tab.id);
        
        if (existingResult.success && existingResult.data) {
          // 更新现有映射为工作区核心标签页
          await WorkonaTabManager.updateTabMetadata(existingResult.data, {
            isWorkspaceCore: true,
            websiteId: websiteId
          });
          console.log(`🔄 更新现有映射为工作区核心标签页: ${existingResult.data}`);
        } else {
          // 创建新的Workona ID映射
          const workonaId = WorkonaTabManager.generateWorkonaTabId(workspaceId);
          const mappingResult = await WorkonaTabManager.createTabIdMapping(
            workonaId,
            tab.id,
            workspaceId,
            websiteId,
            {
              isWorkspaceCore: true,
              source: 'promoted'
            }
          );

          if (mappingResult.success) {
            await WorkspaceStateManager.addWorkonaTabId(workspaceId, workonaId);
            console.log(`✅ 创建新的工作区核心标签页映射: ${workonaId}`);
          }
        }
      }
    } catch (error) {
      console.error('提升标签页为工作区核心标签页失败:', error);
    }
  }

  /**
   * 添加当前标签页到工作区
   */
  private static async addCurrentTabsToWorkspace(workspaceId: string): Promise<void> {
    try {
      console.log(`📌 添加当前标签页到工作区: ${workspaceId}`);

      // 获取当前窗口的所有标签页
      const { TabClassificationService } = await import('../tabs');
      const currentTabsResult = await TabClassificationService.getCurrentWindowTabs();
      if (!currentTabsResult.success) {
        console.error('获取当前窗口标签页失败:', currentTabsResult.error);
        return;
      }

      const currentTabs = currentTabsResult.data!;
      const { WorkspaceWebsiteManager } = await import('./WorkspaceWebsiteManager');

      // 为每个有效的标签页添加到工作区
      for (const tab of currentTabs) {
        if (!tab.url || !this.isValidUrl(tab.url)) {
          continue;
        }

        try {
          await WorkspaceWebsiteManager.addWebsite(workspaceId, tab.url, {
            title: tab.title,
            favicon: tab.favicon,
            isPinned: tab.isPinned,
          });
          console.log(`✅ 添加标签页到工作区: ${tab.title}`);
        } catch (error) {
          console.warn(`添加标签页失败: ${tab.title}`, error);
        }
      }

      console.log(`✅ 当前标签页添加完成`);
    } catch (error) {
      console.error('添加当前标签页到工作区失败:', error);
    }
  }
}
