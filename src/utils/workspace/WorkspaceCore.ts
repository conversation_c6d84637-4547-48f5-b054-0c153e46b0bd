import {
  WorkSpace,
  CreateWorkspaceOptions,
  UpdateWorkspaceOptions,
  OperationResult,
  WorkspaceType
} from '@/types/workspace';
import { StorageManager } from '../storage';
import { ERROR_CODES, WORKSPACE_COLORS, WORKSPACE_ICONS } from '../constants';

/**
 * 工作区核心CRUD操作类
 * 职责：处理工作区的基础创建、读取、更新、删除操作
 * 
 * 🎯 核心职责：
 * 1. 工作区的创建和初始化
 * 2. 工作区的查询和获取
 * 3. 工作区的更新和修改
 * 4. 工作区的删除和清理
 * 5. 工作区基础属性管理
 */
export class WorkspaceCore {
  /**
   * 创建新工作区
   */
  static async createWorkspace(options: CreateWorkspaceOptions): Promise<OperationResult<WorkSpace>> {
    try {
      console.log('🆕 创建新工作区:', options);

      // 验证必需字段
      if (!options.name?.trim()) {
        return {
          success: false,
          error: {
            code: ERROR_CODES.INVALID_URL,
            message: 'Workspace name is required',
          },
        };
      }

      // 生成工作区ID
      const { IdGenerator } = await import('./WorkspaceUtilities');
      const workspaceId = IdGenerator.generateWorkspaceId(options.type);

      // 创建工作区对象
      const workspace: WorkSpace = {
        id: workspaceId,
        name: options.name.trim(),
        description: options.description?.trim() || '',
        color: options.color || WORKSPACE_COLORS[Math.floor(Math.random() * WORKSPACE_COLORS.length)],
        icon: options.icon || WORKSPACE_ICONS[Math.floor(Math.random() * WORKSPACE_ICONS.length)],
        websites: [],
        isActive: false,
        order: 0, // 将在后面设置正确的值
        createdAt: Date.now(),
        updatedAt: Date.now(),
        type: options.type || 'saved',
        state: options.state || 'active',
        position: options.position || 0,
        workonaTabIds: [],
        hiddenTabIds: [],
        pinnedTabIds: [],
        userTabsHidden: false,
      };

      // 获取现有工作区列表
      const workspacesResult = await StorageManager.getWorkspaces();
      if (!workspacesResult.success) {
        return { success: false, error: workspacesResult.error };
      }

      const workspaces = workspacesResult.data!;

      // 检查名称是否重复
      const existingWorkspace = workspaces.find(w => w.name === workspace.name);
      if (existingWorkspace) {
        return {
          success: false,
          error: {
            code: ERROR_CODES.DUPLICATE_WORKSPACE,
            message: 'Workspace with this name already exists',
          },
        };
      }

      // 设置位置和排序
      if (workspace.position === 0) {
        workspace.position = workspaces.length;
      }
      workspace.order = workspaces.length;

      // 添加到工作区列表
      workspaces.push(workspace);

      // 保存到存储
      const saveResult = await StorageManager.saveWorkspaces(workspaces);
      if (!saveResult.success) {
        return { success: false, error: saveResult.error };
      }

      console.log(`✅ 工作区创建成功: ${workspace.name} (${workspace.id})`);
      return { success: true, data: workspace };
    } catch (error) {
      return {
        success: false,
        error: {
          code: ERROR_CODES.STORAGE_ERROR,
          message: 'Failed to create workspace',
          details: error,
        },
      };
    }
  }

  /**
   * 更新工作区
   */
  static async updateWorkspace(id: string, options: UpdateWorkspaceOptions): Promise<OperationResult<WorkSpace>> {
    try {
      console.log(`🔄 更新工作区: ${id}`, options);

      // 获取现有工作区列表
      const workspacesResult = await StorageManager.getWorkspaces();
      if (!workspacesResult.success) {
        return { success: false, error: workspacesResult.error };
      }

      const workspaces = workspacesResult.data!;
      const workspaceIndex = workspaces.findIndex(w => w.id === id);

      if (workspaceIndex === -1) {
        return {
          success: false,
          error: {
            code: ERROR_CODES.WORKSPACE_NOT_FOUND,
            message: 'Workspace not found',
          },
        };
      }

      const workspace = workspaces[workspaceIndex];

      // 如果更新名称，检查是否重复
      if (options.name && options.name !== workspace.name) {
        const existingWorkspace = workspaces.find(w => w.name === options.name && w.id !== id);
        if (existingWorkspace) {
          return {
            success: false,
            error: {
              code: ERROR_CODES.DUPLICATE_WORKSPACE,
              message: 'Workspace with this name already exists',
            },
          };
        }
      }

      // 更新工作区属性
      const updatedWorkspace: WorkSpace = {
        ...workspace,
        name: options.name?.trim() || workspace.name,
        description: options.description?.trim() ?? workspace.description,
        color: options.color || workspace.color,
        icon: options.icon || workspace.icon,
        updatedAt: Date.now(),
      };

      // 更新数组中的工作区
      workspaces[workspaceIndex] = updatedWorkspace;

      // 保存到存储
      const saveResult = await StorageManager.saveWorkspaces(workspaces);
      if (!saveResult.success) {
        return { success: false, error: saveResult.error };
      }

      console.log(`✅ 工作区更新成功: ${updatedWorkspace.name}`);
      return { success: true, data: updatedWorkspace };
    } catch (error) {
      return {
        success: false,
        error: {
          code: ERROR_CODES.STORAGE_ERROR,
          message: 'Failed to update workspace',
          details: error,
        },
      };
    }
  }

  /**
   * 删除工作区
   */
  static async deleteWorkspace(id: string): Promise<OperationResult<void>> {
    try {
      console.log(`🗑️ 删除工作区: ${id}`);

      // 获取现有工作区列表
      const workspacesResult = await StorageManager.getWorkspaces();
      if (!workspacesResult.success) {
        return { success: false, error: workspacesResult.error };
      }

      const workspaces = workspacesResult.data!;
      const workspaceIndex = workspaces.findIndex(w => w.id === id);

      if (workspaceIndex === -1) {
        return {
          success: false,
          error: {
            code: ERROR_CODES.WORKSPACE_NOT_FOUND,
            message: 'Workspace not found',
          },
        };
      }

      const workspace = workspaces[workspaceIndex];

      // 清理相关的Workona标签页映射
      try {
        const { WorkonaTabManager } = await import('../workonaTabManager');
        for (const workonaId of workspace.workonaTabIds || []) {
          await WorkonaTabManager.removeTabMapping(workonaId);
        }
        console.log(`🧹 清理了 ${workspace.workonaTabIds?.length || 0} 个Workona标签页映射`);
      } catch (error) {
        console.warn('清理Workona标签页映射失败:', error);
      }

      // 从数组中移除工作区
      workspaces.splice(workspaceIndex, 1);

      // 如果删除的是活跃工作区，清除活跃状态
      if (workspace.isActive) {
        await StorageManager.setActiveWorkspaceId(null);
      }

      // 保存到存储
      const saveResult = await StorageManager.saveWorkspaces(workspaces);
      if (!saveResult.success) {
        return { success: false, error: saveResult.error };
      }

      console.log(`✅ 工作区删除成功: ${workspace.name}`);
      return { success: true };
    } catch (error) {
      return {
        success: false,
        error: {
          code: ERROR_CODES.STORAGE_ERROR,
          message: 'Failed to delete workspace',
          details: error,
        },
      };
    }
  }

  /**
   * 重新排序工作区
   */
  static async reorderWorkspaces(workspaceIds: string[]): Promise<OperationResult<void>> {
    try {
      console.log('🔄 重新排序工作区:', workspaceIds);

      // 获取现有工作区列表
      const workspacesResult = await StorageManager.getWorkspaces();
      if (!workspacesResult.success) {
        return { success: false, error: workspacesResult.error };
      }

      const workspaces = workspacesResult.data!;

      // 验证所有ID都存在
      const existingIds = workspaces.map(w => w.id);
      const missingIds = workspaceIds.filter(id => !existingIds.includes(id));
      if (missingIds.length > 0) {
        return {
          success: false,
          error: {
            code: ERROR_CODES.WORKSPACE_NOT_FOUND,
            message: `Workspaces not found: ${missingIds.join(', ')}`,
          },
        };
      }

      // 重新排序
      const reorderedWorkspaces = workspaceIds.map((id, index) => {
        const workspace = workspaces.find(w => w.id === id)!;
        return {
          ...workspace,
          position: index,
          updatedAt: Date.now(),
        };
      });

      // 保存到存储
      const saveResult = await StorageManager.saveWorkspaces(reorderedWorkspaces);
      if (!saveResult.success) {
        return { success: false, error: saveResult.error };
      }

      console.log('✅ 工作区重新排序成功');
      return { success: true };
    } catch (error) {
      return {
        success: false,
        error: {
          code: ERROR_CODES.STORAGE_ERROR,
          message: 'Failed to reorder workspaces',
          details: error,
        },
      };
    }
  }

  /**
   * 更新工作区类型
   */
  static async updateWorkspaceType(
    workspaceId: string,
    newType: WorkspaceType
  ): Promise<OperationResult<WorkSpace>> {
    try {
      console.log(`🔄 更新工作区类型: ${workspaceId} -> ${newType}`);

      // 获取现有工作区列表
      const workspacesResult = await StorageManager.getWorkspaces();
      if (!workspacesResult.success) {
        return { success: false, error: workspacesResult.error };
      }

      const workspaces = workspacesResult.data!;
      const workspaceIndex = workspaces.findIndex(w => w.id === workspaceId);

      if (workspaceIndex === -1) {
        return {
          success: false,
          error: {
            code: ERROR_CODES.WORKSPACE_NOT_FOUND,
            message: 'Workspace not found',
          },
        };
      }

      // 更新工作区类型
      const updatedWorkspace = {
        ...workspaces[workspaceIndex],
        type: newType,
        updatedAt: Date.now(),
      };

      workspaces[workspaceIndex] = updatedWorkspace;

      // 保存到存储
      const saveResult = await StorageManager.saveWorkspaces(workspaces);
      if (!saveResult.success) {
        return { success: false, error: saveResult.error };
      }

      console.log(`✅ 工作区类型更新成功: ${updatedWorkspace.name} -> ${newType}`);
      return { success: true, data: updatedWorkspace };
    } catch (error) {
      return {
        success: false,
        error: {
          code: ERROR_CODES.STORAGE_ERROR,
          message: 'Failed to update workspace type',
          details: error,
        },
      };
    }
  }
}
