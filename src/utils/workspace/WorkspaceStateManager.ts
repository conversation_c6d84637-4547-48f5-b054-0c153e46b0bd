import {
  WorkSpace,
  OperationResult,
  WorkspaceState
} from '@/types/workspace';
import { StorageManager } from '../storage';
import { ERROR_CODES } from '../constants';

/**
 * 工作区状态管理器
 * 职责：处理工作区的状态管理和配置
 * 
 * 🎯 核心职责：
 * 1. 工作区状态的更新和管理
 * 2. 工作区位置的管理
 * 3. 用户标签页隐藏状态管理
 * 4. Workona标签页ID管理
 * 5. 工作区配置的持久化
 */
export class WorkspaceStateManager {
  /**
   * 辅助方法：更新工作区并保存
   */
  private static async saveUpdatedWorkspace(updatedWorkspace: WorkSpace): Promise<OperationResult<void>> {
    try {
      const workspacesResult = await StorageManager.getWorkspaces();
      if (!workspacesResult.success) {
        return { success: false, error: workspacesResult.error };
      }

      const workspaces = workspacesResult.data!;
      const workspaceIndex = workspaces.findIndex(w => w.id === updatedWorkspace.id);
      if (workspaceIndex !== -1) {
        workspaces[workspaceIndex] = updatedWorkspace;
      }

      return await StorageManager.saveWorkspaces(workspaces);
    } catch (error) {
      return {
        success: false,
        error: {
          code: ERROR_CODES.STORAGE_ERROR,
          message: 'Failed to save updated workspace',
          details: error,
        },
      };
    }
  }
  /**
   * 更新工作区状态
   */
  static async updateWorkspaceState(
    workspaceId: string,
    newState: WorkspaceState
  ): Promise<OperationResult<WorkSpace>> {
    try {
      console.log(`🔄 更新工作区状态: ${workspaceId} -> ${newState}`);

      // 获取工作区
      const workspaceResult = await StorageManager.getWorkspace(workspaceId);
      if (!workspaceResult.success) {
        return { success: false, error: workspaceResult.error };
      }

      const workspace = workspaceResult.data!;

      // 更新状态
      const updatedWorkspace = {
        ...workspace,
        state: newState,
        updatedAt: Date.now(),
      };

      // 保存工作区
      const saveResult = await this.saveUpdatedWorkspace(updatedWorkspace);
      if (!saveResult.success) {
        return { success: false, error: saveResult.error };
      }

      console.log(`✅ 工作区状态更新成功: ${workspace.name} -> ${newState}`);
      return { success: true, data: updatedWorkspace };
    } catch (error) {
      return {
        success: false,
        error: {
          code: ERROR_CODES.STORAGE_ERROR,
          message: 'Failed to update workspace state',
          details: error,
        },
      };
    }
  }

  /**
   * 更新工作区位置
   */
  static async updateWorkspacePosition(
    workspaceId: string,
    newPosition: number
  ): Promise<OperationResult<WorkSpace>> {
    try {
      console.log(`🔄 更新工作区位置: ${workspaceId} -> ${newPosition}`);

      // 获取工作区
      const workspaceResult = await StorageManager.getWorkspace(workspaceId);
      if (!workspaceResult.success) {
        return { success: false, error: workspaceResult.error };
      }

      const workspace = workspaceResult.data!;

      // 更新位置
      const updatedWorkspace = {
        ...workspace,
        position: newPosition,
        updatedAt: Date.now(),
      };

      // 保存工作区
      const saveResult = await this.saveUpdatedWorkspace(updatedWorkspace);
      if (!saveResult.success) {
        return { success: false, error: saveResult.error };
      }

      console.log(`✅ 工作区位置更新成功: ${workspace.name} -> ${newPosition}`);
      return { success: true, data: updatedWorkspace };
    } catch (error) {
      return {
        success: false,
        error: {
          code: ERROR_CODES.STORAGE_ERROR,
          message: 'Failed to update workspace position',
          details: error,
        },
      };
    }
  }

  /**
   * 设置用户标签页隐藏状态
   */
  static async setUserTabsHiddenState(
    workspaceId: string,
    isHidden: boolean,
    hiddenTabIds: number[] = [],
    pinnedTabIds: number[] = []
  ): Promise<OperationResult<void>> {
    try {
      console.log(`🔄 设置工作区 ${workspaceId} 用户标签页隐藏状态: ${isHidden}`);

      // 获取工作区
      const workspaceResult = await StorageManager.getWorkspace(workspaceId);
      if (!workspaceResult.success) {
        return { success: false, error: workspaceResult.error };
      }

      const workspace = workspaceResult.data!;

      // 更新隐藏状态
      const updatedWorkspace = {
        ...workspace,
        userTabsHidden: isHidden,
        hiddenTabIds: [...hiddenTabIds],
        pinnedTabIds: [...pinnedTabIds],
        updatedAt: Date.now(),
      };

      // 保存工作区
      const saveResult = await this.saveUpdatedWorkspace(updatedWorkspace);
      if (!saveResult.success) {
        return { success: false, error: saveResult.error };
      }

      console.log(`✅ 用户标签页隐藏状态更新成功: ${workspace.name} -> ${isHidden}`);
      return { success: true };
    } catch (error) {
      return {
        success: false,
        error: {
          code: ERROR_CODES.STORAGE_ERROR,
          message: 'Failed to set user tabs hidden state',
          details: error,
        },
      };
    }
  }

  /**
   * 获取用户标签页隐藏状态
   */
  static async getUserTabsHiddenState(workspaceId: string): Promise<OperationResult<{
    isHidden: boolean;
    hiddenTabIds: number[];
    pinnedTabIds: number[];
  }>> {
    try {
      // 获取工作区
      const workspaceResult = await StorageManager.getWorkspace(workspaceId);
      if (!workspaceResult.success) {
        return { success: false, error: workspaceResult.error };
      }

      const workspace = workspaceResult.data!;

      return {
        success: true,
        data: {
          isHidden: workspace.userTabsHidden || false,
          hiddenTabIds: workspace.hiddenTabIds || [],
          pinnedTabIds: workspace.pinnedTabIds || [],
        },
      };
    } catch (error) {
      return {
        success: false,
        error: {
          code: ERROR_CODES.STORAGE_ERROR,
          message: 'Failed to get user tabs hidden state',
          details: error,
        },
      };
    }
  }

  /**
   * 清理隐藏的标签页ID
   */
  static async clearHiddenTabIds(workspaceId: string, tabIdsToRemove: number[]): Promise<OperationResult<void>> {
    try {
      console.log(`🧹 清理工作区 ${workspaceId} 的隐藏标签页ID:`, tabIdsToRemove);

      // 获取工作区
      const workspaceResult = await StorageManager.getWorkspace(workspaceId);
      if (!workspaceResult.success) {
        return { success: false, error: workspaceResult.error };
      }

      const workspace = workspaceResult.data!;

      // 过滤掉要移除的标签页ID
      const updatedHiddenTabIds = (workspace.hiddenTabIds || []).filter(
        id => !tabIdsToRemove.includes(id)
      );

      const updatedPinnedTabIds = (workspace.pinnedTabIds || []).filter(
        id => !tabIdsToRemove.includes(id)
      );

      // 更新工作区
      const updatedWorkspace = {
        ...workspace,
        hiddenTabIds: updatedHiddenTabIds,
        pinnedTabIds: updatedPinnedTabIds,
        updatedAt: Date.now(),
      };

      // 保存工作区
      const saveResult = await this.saveUpdatedWorkspace(updatedWorkspace);
      if (!saveResult.success) {
        return { success: false, error: saveResult.error };
      }

      console.log(`✅ 隐藏标签页ID清理成功，移除了 ${tabIdsToRemove.length} 个ID`);
      return { success: true };
    } catch (error) {
      return {
        success: false,
        error: {
          code: ERROR_CODES.STORAGE_ERROR,
          message: 'Failed to clear hidden tab IDs',
          details: error,
        },
      };
    }
  }

  /**
   * 添加Workona标签页ID
   */
  static async addWorkonaTabId(
    workspaceId: string,
    workonaId: string
  ): Promise<OperationResult<void>> {
    try {
      console.log(`➕ 添加Workona标签页ID到工作区 ${workspaceId}: ${workonaId}`);

      // 获取工作区
      const workspaceResult = await StorageManager.getWorkspace(workspaceId);
      if (!workspaceResult.success) {
        return { success: false, error: workspaceResult.error };
      }

      const workspace = workspaceResult.data!;

      // 检查是否已存在
      const workonaTabIds = workspace.workonaTabIds || [];
      if (workonaTabIds.includes(workonaId)) {
        console.log(`ℹ️ Workona标签页ID已存在: ${workonaId}`);
        return { success: true };
      }

      // 添加新的Workona标签页ID
      const updatedWorkspace = {
        ...workspace,
        workonaTabIds: [...workonaTabIds, workonaId],
        updatedAt: Date.now(),
      };

      // 保存工作区
      const saveResult = await this.saveUpdatedWorkspace(updatedWorkspace);
      if (!saveResult.success) {
        return { success: false, error: saveResult.error };
      }

      console.log(`✅ Workona标签页ID添加成功: ${workonaId}`);
      return { success: true };
    } catch (error) {
      return {
        success: false,
        error: {
          code: ERROR_CODES.STORAGE_ERROR,
          message: 'Failed to add Workona tab ID',
          details: error,
        },
      };
    }
  }

  /**
   * 移除Workona标签页ID
   */
  static async removeWorkonaTabId(
    workspaceId: string,
    workonaId: string
  ): Promise<OperationResult<void>> {
    try {
      console.log(`➖ 从工作区 ${workspaceId} 移除Workona标签页ID: ${workonaId}`);

      // 获取工作区
      const workspaceResult = await StorageManager.getWorkspace(workspaceId);
      if (!workspaceResult.success) {
        return { success: false, error: workspaceResult.error };
      }

      const workspace = workspaceResult.data!;

      // 移除Workona标签页ID
      const workonaTabIds = workspace.workonaTabIds || [];
      const updatedWorkonaTabIds = workonaTabIds.filter(id => id !== workonaId);

      // 更新工作区
      const updatedWorkspace = {
        ...workspace,
        workonaTabIds: updatedWorkonaTabIds,
        updatedAt: Date.now(),
      };

      // 保存工作区
      const saveResult = await this.saveUpdatedWorkspace(updatedWorkspace);
      if (!saveResult.success) {
        return { success: false, error: saveResult.error };
      }

      console.log(`✅ Workona标签页ID移除成功: ${workonaId}`);
      return { success: true };
    } catch (error) {
      return {
        success: false,
        error: {
          code: ERROR_CODES.STORAGE_ERROR,
          message: 'Failed to remove Workona tab ID',
          details: error,
        },
      };
    }
  }
}
