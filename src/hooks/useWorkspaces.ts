import { useState, useEffect, useCallback } from 'react';
import { WorkSpace, Settings } from '@/types/workspace';
import { StorageManager } from '@/utils/storage';
import { WorkspaceManager } from '@/utils/workspace';
import { WorkspaceSwitcher } from '@/utils/workspaceSwitcher';
import { MigrationManager } from '@/utils/dataMigration';

/**
 * 工作区管理Hook
 */
export const useWorkspaces = () => {
  const [workspaces, setWorkspaces] = useState<WorkSpace[]>([]);
  const [activeWorkspaceId, setActiveWorkspaceId] = useState<string | null>(null);
  const [settings, setSettings] = useState<Settings | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [workspaceSetupStatus, setWorkspaceSetupStatus] = useState<{
    isSetupInProgress: boolean;
    setupWorkspaceId: string | null;
  }>({
    isSetupInProgress: false,
    setupWorkspaceId: null
  });

  /**
   * 加载工作区数据
   */
  const loadWorkspaces = useCallback(async () => {
    try {
      setLoading(true);
      setError(null);

      // 首次加载时检查 Workona 迁移（概念性重构版）
      try {
        // 阶段1：基础 Workona 格式迁移
        await MigrationManager.migrateToWorkonaFormat({
          backupOriginalData: true,
          validateAfterMigration: true,
          rollbackOnError: true
        });

        // 阶段2：标签页元数据迁移（概念性重构）
        const metadataMigrationResult = await MigrationManager.migrateTabMappingsMetadata();
        if (metadataMigrationResult.success && metadataMigrationResult.data! > 0) {
          console.log(`✅ 概念性重构：成功迁移 ${metadataMigrationResult.data} 个标签页映射的元数据`);
        }
      } catch (migrationError) {
        console.warn('Workona 迁移检查失败，继续使用现有数据:', migrationError);
      }

      const [workspacesResult, activeIdResult, settingsResult] = await Promise.all([
        StorageManager.getWorkspaces(),
        StorageManager.getActiveWorkspaceId(),
        StorageManager.getSettings(),
      ]);

      if (!workspacesResult.success) {
        throw new Error(workspacesResult.error?.message || 'Failed to load workspaces');
      }

      if (!activeIdResult.success) {
        throw new Error(activeIdResult.error?.message || 'Failed to load active workspace');
      }

      if (!settingsResult.success) {
        throw new Error(settingsResult.error?.message || 'Failed to load settings');
      }

      setWorkspaces(workspacesResult.data!);
      setActiveWorkspaceId(activeIdResult.data || null);
      setSettings(settingsResult.data!);
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Unknown error');
      console.error('Error loading workspaces:', err);
    } finally {
      setLoading(false);
    }
  }, []);

  /**
   * 创建工作区
   */
  const createWorkspace = useCallback(async (name: string, options?: {
    icon?: string;
    color?: string;
    activate?: boolean;
    addCurrentTabs?: boolean;
  }) => {
    try {
      setError(null);
      const result = await WorkspaceManager.createWorkspace({
        name,
        icon: options?.icon,
        color: options?.color,
        activate: options?.activate,
        addCurrentTabs: options?.addCurrentTabs,
      });

      if (!result.success) {
        const errorMessage = result.error?.message || 'Failed to create workspace';
        setError(errorMessage);
        return null; // 返回null而不是抛出异常
      }

      await loadWorkspaces();
      return result.data!;
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to create workspace';
      setError(errorMessage);
      return null; // 返回null而不是抛出异常
    }
  }, [loadWorkspaces]);

  /**
   * 更新工作区
   */
  const updateWorkspace = useCallback(async (id: string, updates: {
    name?: string;
    icon?: string;
    color?: string;
  }) => {
    try {
      setError(null);
      const result = await WorkspaceManager.updateWorkspace(id, updates);

      if (!result.success) {
        throw new Error(result.error?.message || 'Failed to update workspace');
      }

      await loadWorkspaces();
      return result.data!;
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to update workspace';
      setError(errorMessage);
      throw new Error(errorMessage);
    }
  }, [loadWorkspaces]);

  /**
   * 删除工作区
   */
  const deleteWorkspace = useCallback(async (id: string) => {
    try {
      setError(null);
      const result = await WorkspaceManager.deleteWorkspace(id);

      if (!result.success) {
        throw new Error(result.error?.message || 'Failed to delete workspace');
      }

      await loadWorkspaces();
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to delete workspace';
      setError(errorMessage);
      throw new Error(errorMessage);
    }
  }, [loadWorkspaces]);

  /**
   * 切换工作区（增强版：支持 Workona 会话管理）
   */
  const switchWorkspace = useCallback(async (id: string) => {
    try {
      console.log(`🔄 切换到工作区: ${id} (使用 Workona 增强功能)`);
      setError(null);

      // 🚨 检查是否有后台设置正在进行
      if (workspaceSetupStatus.isSetupInProgress) {
        const currentSetupId = workspaceSetupStatus.setupWorkspaceId;
        console.warn(`⚠️ 工作区 ${currentSetupId} 正在后台设置中，无法切换到 ${id}`);
        const errorMessage = `工作区 ${currentSetupId} 正在设置中，请稍候再试`;
        setError(errorMessage);
        return; // 不抛出异常，避免重复错误处理
      }

      // 使用增强的工作区切换器（已集成 Workona 会话管理）
      const result = await WorkspaceSwitcher.switchToWorkspace(id);

      if (!result.success) {
        console.error('❌ 工作区切换失败:', result.error);
        let errorMessage = result.error?.message || 'Failed to switch workspace';

        // 特殊处理后台设置进行中的错误
        if (result.error?.message === 'Background workspace setup in progress') {
          errorMessage = '有工作区正在后台设置中，请稍候再试';
        }

        setError(errorMessage);
        return; // 不抛出异常，避免重复错误处理
      }

      console.log('✅ 工作区切换成功，重新加载数据...');
      await loadWorkspaces();
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to switch workspace';
      console.error('❌ 工作区切换错误:', errorMessage);
      setError(errorMessage);
      // 移除throw，避免重复错误处理
    }
  }, [loadWorkspaces, workspaceSetupStatus]);

  /**
   * 添加网站到工作区（Workona 风格：移除固定标签页选项）
   */
  const addWebsite = useCallback(async (workspaceId: string, url: string, options?: {
    title?: string;
    favicon?: string;
    openInNewTab?: boolean;
  }) => {
    try {
      setError(null);
      console.log(`🌐 添加网站到工作区 (Workona 风格): ${url} -> 工作区: ${workspaceId}`);

      const result = await WorkspaceManager.addWebsite(workspaceId, url, options);

      if (!result.success) {
        const errorMessage = result.error?.message || 'Failed to add website';
        console.error(`❌ 添加网站失败: ${errorMessage}`);
        setError(errorMessage);
        return null; // 返回null而不是抛出异常
      }

      console.log(`✅ 成功添加网站: ${result.data!.title}`);
      await loadWorkspaces();
      return result.data!;
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to add website';
      console.error(`❌ 添加网站失败: ${errorMessage}`);
      setError(errorMessage);
      return null; // 返回null而不是抛出异常
    }
  }, [loadWorkspaces]);

  /**
   * 基于Workona ID血缘关系添加当前标签页到工作区
   */
  const addCurrentTabByWorkonaId = useCallback(async (workspaceId: string, tabId: number, tabInfo: {
    url: string;
    title?: string;
    favicon?: string;
  }) => {
    try {
      setError(null);
      console.log(`🎯 基于Workona ID血缘关系添加当前标签页: ${tabInfo.url} -> 工作区: ${workspaceId}`);

      const result = await WorkspaceManager.addCurrentTabByWorkonaId(workspaceId, tabId?.toString(), tabInfo);

      if (!result.success) {
        throw new Error(result.error?.message || 'Failed to add current tab');
      }

      console.log(`✅ 成功添加当前标签页到工作区`);
      await loadWorkspaces();
      return true;
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to add current tab';
      console.error(`❌ 添加当前标签页失败: ${errorMessage}`);
      setError(errorMessage);
      throw new Error(errorMessage);
    }
  }, [loadWorkspaces]);

  /**
   * 从工作区移除网站
   */
  const removeWebsite = useCallback(async (workspaceId: string, websiteId: string) => {
    try {
      setError(null);
      const result = await WorkspaceManager.removeWebsite(workspaceId, websiteId);

      if (!result.success) {
        throw new Error(result.error?.message || 'Failed to remove website');
      }

      await loadWorkspaces();
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to remove website';
      setError(errorMessage);
      throw new Error(errorMessage);
    }
  }, [loadWorkspaces]);

  /**
   * 更新工作区中的网站
   */
  const updateWebsite = useCallback(async (workspaceId: string, websiteId: string, updates: { url?: string; title?: string; isPinned?: boolean }) => {
    try {
      setError(null);
      const result = await WorkspaceManager.updateWebsite(workspaceId, websiteId, updates);

      if (!result.success) {
        throw new Error(result.error?.message || 'Failed to update website');
      }

      await loadWorkspaces();
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to update website';
      setError(errorMessage);
      throw new Error(errorMessage);
    }
  }, [loadWorkspaces]);

  /**
   * 重新排序工作区
   */
  const reorderWorkspaces = useCallback(async (workspaceIds: string[]) => {
    try {
      setError(null);
      const result = await WorkspaceManager.reorderWorkspaces(workspaceIds);

      if (!result.success) {
        throw new Error(result.error?.message || 'Failed to reorder workspaces');
      }

      await loadWorkspaces();
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to reorder workspaces';
      setError(errorMessage);
      throw new Error(errorMessage);
    }
  }, [loadWorkspaces]);

  /**
   * 重新排序网站
   */
  const reorderWebsites = useCallback(async (workspaceId: string, websiteIds: string[]) => {
    try {
      setError(null);
      const result = await WorkspaceManager.reorderWebsites(workspaceId, websiteIds);

      if (!result.success) {
        throw new Error(result.error?.message || 'Failed to reorder websites');
      }

      await loadWorkspaces();
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to reorder websites';
      setError(errorMessage);
      throw new Error(errorMessage);
    }
  }, [loadWorkspaces]);

  /**
   * 更新设置
   */
  const updateSettings = useCallback(async (newSettings: Partial<Settings>) => {
    try {
      setError(null);
      const result = await StorageManager.saveSettings(newSettings);

      if (!result.success) {
        throw new Error(result.error?.message || 'Failed to update settings');
      }

      await loadWorkspaces();
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to update settings';
      setError(errorMessage);
      throw new Error(errorMessage);
    }
  }, [loadWorkspaces]);

  /**
   * 获取当前活跃的工作区
   */
  const activeWorkspace = workspaces.find(w => w.id === activeWorkspaceId) || null;

  // 初始化加载
  useEffect(() => {
    loadWorkspaces();
  }, [loadWorkspaces]);

  // 监听存储变化
  useEffect(() => {
    const handleStorageChange = () => {
      loadWorkspaces();
    };

    StorageManager.onChanged(handleStorageChange);
  }, [loadWorkspaces]);

  // 监听工作区状态重置消息和设置状态变更
  useEffect(() => {
    const handleMessage = (message: any) => {
      if (message.type === 'WORKSPACE_STATE_RESET') {
        console.log('🔄 收到工作区状态重置通知，刷新UI');
        loadWorkspaces();
      } else if (message.type === 'WORKSPACE_SETUP_STATUS_CHANGE') {
        console.log(`🔄 收到工作区设置状态变更通知: ${message.workspaceId} - ${message.isSetupInProgress ? '开始' : '完成'}`);
        const newStatus = {
          isSetupInProgress: message.isSetupInProgress,
          setupWorkspaceId: message.isSetupInProgress ? message.workspaceId : null
        };
        console.log(`📊 更新工作区设置状态:`, newStatus);
        setWorkspaceSetupStatus(newStatus);
      }
    };

    chrome.runtime.onMessage.addListener(handleMessage);

    return () => {
      chrome.runtime.onMessage.removeListener(handleMessage);
    };
  }, [loadWorkspaces]);

  return {
    workspaces,
    activeWorkspace,
    activeWorkspaceId,
    settings,
    loading,
    error,
    workspaceSetupStatus, // 工作区设置状态
    createWorkspace,
    updateWorkspace,
    deleteWorkspace,
    switchWorkspace,
    addWebsite,
    addCurrentTabByWorkonaId,
    removeWebsite,
    updateWebsite,
    reorderWorkspaces,
    reorderWebsites,
    updateSettings,
    reload: loadWorkspaces,
  };
};
