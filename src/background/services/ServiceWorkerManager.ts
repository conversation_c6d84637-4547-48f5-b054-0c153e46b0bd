import { StorageManager } from '../../utils/storage';

/**
 * Service Worker生命周期管理器
 * 职责：管理Service Worker的启动、休眠、唤醒状态
 * 
 * 🎯 核心职责：
 * 1. Service Worker生命周期检测
 * 2. 智能工作区状态管理
 * 3. 浏览器重启检测
 * 4. 扩展安装/更新处理
 */
export class ServiceWorkerManager {
  private static isFirstRealStartup = true;
  private static lastInitTimestamp = 0;
  private static readonly SERVICE_WORKER_WAKEUP_THRESHOLD = 5 * 60 * 1000; // 5分钟

  /**
   * 智能工作区状态管理
   */
  static async smartWorkspaceStateManagement(): Promise<void> {
    try {
      const now = Date.now();
      const timeSinceLastInit = now - ServiceWorkerManager.lastInitTimestamp;

      // 如果距离上次初始化时间很短，可能是Service Worker唤醒
      if (timeSinceLastInit < ServiceWorkerManager.SERVICE_WORKER_WAKEUP_THRESHOLD && !ServiceWorkerManager.isFirstRealStartup) {
        console.log('🔄 检测到Service Worker唤醒，保持工作区状态');
        ServiceWorkerManager.lastInitTimestamp = now;
        return;
      }

      // 🔄 真正的启动事件（浏览器重启或扩展首次加载）- 重置工作区状态
      console.log('🔄 检测到真正的启动事件 - 浏览器重启或扩展首次加载');
      console.log('🔄 根据用户需求，重置工作区选择状态');

      // 执行工作区状态重置
      await WorkspaceStateManager.resetWorkspaceStateOnBrowserRestart();

      ServiceWorkerManager.isFirstRealStartup = false;
      ServiceWorkerManager.lastInitTimestamp = now;
    } catch (error) {
      console.error('智能工作区状态管理失败:', error);
    }
  }

  /**
   * 设置Chrome运行时事件监听器
   */
  static setupRuntimeListeners(): void {
    // 监听浏览器启动事件
    if (chrome.runtime.onStartup) {
      chrome.runtime.onStartup.addListener(async () => {
        console.log('🚀 Chrome扩展启动事件触发 - 浏览器重启');
        await ServiceWorkerManager.smartWorkspaceStateManagement();
      });
    } else {
      console.warn('⚠️ chrome.runtime.onStartup 不可用');
    }

    // 监听扩展安装/更新事件
    if (chrome.runtime.onInstalled) {
      chrome.runtime.onInstalled.addListener(async (details) => {
        console.log('📦 Chrome扩展安装/更新事件:', details.reason);
        
        if (details.reason === 'install') {
          console.log('🎉 扩展首次安装');
          await ServiceWorkerManager.smartWorkspaceStateManagement();
        } else if (details.reason === 'update') {
          console.log('🔄 扩展更新');
          // 扩展更新时保持现有状态，不重置
        }
      });
    } else {
      console.warn('⚠️ chrome.runtime.onInstalled 不可用');
    }
  }

  /**
   * 获取Service Worker状态信息
   */
  static getServiceWorkerInfo(): {
    isFirstRealStartup: boolean;
    lastInitTimestamp: number;
    timeSinceLastInit: number;
  } {
    return {
      isFirstRealStartup: ServiceWorkerManager.isFirstRealStartup,
      lastInitTimestamp: ServiceWorkerManager.lastInitTimestamp,
      timeSinceLastInit: Date.now() - ServiceWorkerManager.lastInitTimestamp,
    };
  }

  /**
   * 重置Service Worker状态
   */
  static resetServiceWorkerState(): void {
    ServiceWorkerManager.isFirstRealStartup = true;
    ServiceWorkerManager.lastInitTimestamp = 0;
    console.log('🔄 Service Worker状态已重置');
  }
}

/**
 * 工作区状态管理器
 * 职责：处理浏览器重启后的工作区状态重置
 */
export class WorkspaceStateManager {
  /**
   * 浏览器重启后重置工作区状态
   */
  static async resetWorkspaceStateOnBrowserRestart(): Promise<void> {
    try {
      console.log('🔄 浏览器重启检测 - 重置工作区选择状态');
      
      // 清除活跃工作区ID，确保用户需要手动选择工作区
      const result = await StorageManager.setActiveWorkspaceId(null);
      if (result.success) {
        console.log('✅ 工作区状态已重置为未选择状态');
      } else {
        console.error('❌ 重置工作区状态失败:', result.error);
      }
    } catch (error) {
      console.error('重置工作区状态时发生错误:', error);
    }
  }

  /**
   * 检查是否需要重置工作区状态
   */
  static async shouldResetWorkspaceState(): Promise<boolean> {
    try {
      // 检查当前是否有活跃工作区
      const activeWorkspaceResult = await StorageManager.getActiveWorkspaceId();
      if (!activeWorkspaceResult.success) {
        return false;
      }

      // 如果有活跃工作区，检查是否应该重置
      const activeWorkspaceId = activeWorkspaceResult.data;
      if (activeWorkspaceId) {
        console.log(`当前活跃工作区: ${activeWorkspaceId}`);
        return true; // 根据用户偏好，浏览器重启后总是重置
      }

      return false;
    } catch (error) {
      console.error('检查工作区状态时发生错误:', error);
      return false;
    }
  }

  /**
   * 获取工作区状态信息
   */
  static async getWorkspaceStateInfo(): Promise<{
    hasActiveWorkspace: boolean;
    activeWorkspaceId: string | null;
    shouldReset: boolean;
  }> {
    try {
      const activeWorkspaceResult = await StorageManager.getActiveWorkspaceId();
      const activeWorkspaceId = activeWorkspaceResult.success ? activeWorkspaceResult.data : null;
      const shouldReset = await WorkspaceStateManager.shouldResetWorkspaceState();

      return {
        hasActiveWorkspace: !!activeWorkspaceId,
        activeWorkspaceId: activeWorkspaceId || null,
        shouldReset,
      };
    } catch (error) {
      console.error('获取工作区状态信息失败:', error);
      return {
        hasActiveWorkspace: false,
        activeWorkspaceId: null,
        shouldReset: false,
      };
    }
  }
}
