import { TabClassificationService, UserTabsRealTimeMonitor } from '../../utils/tabs';
import { WorkonaTabManager } from '../../utils/workonaTabManager';
import { WorkspaceSessionManager } from '../../utils/workspaceSessionManager';
import { StorageManager } from '../../utils/storage';

/**
 * 后台标签页管理器
 * 职责：处理标签页相关的后台操作和监听
 * 
 * 🎯 核心职责：
 * 1. 标签页事件监听设置
 * 2. 标签页状态同步
 * 3. Workona ID映射管理
 * 4. 用户标签页实时监控
 */
export class BackgroundTabManager {
  /**
   * 启动用户标签页实时监控
   */
  static async startUserTabsRealTimeMonitoring(): Promise<void> {
    try {
      console.log('🔄 启动用户标签页实时监控...');
      await UserTabsRealTimeMonitor.startMonitoring();
      console.log('✅ 用户标签页实时监控已启动');
    } catch (error) {
      console.error('启动用户标签页实时监控失败:', error);
    }
  }

  /**
   * 刷新用户标签页监控
   */
  static async refreshUserTabsMonitoring(): Promise<void> {
    try {
      console.log('🔄 刷新用户标签页监控状态...');
      
      // 停止现有监控
      await UserTabsRealTimeMonitor.stopMonitoring();
      
      // 重新启动监控
      await BackgroundTabManager.startUserTabsRealTimeMonitoring();
      
      console.log('✅ 用户标签页监控已刷新');
    } catch (error) {
      console.error('刷新用户标签页监控失败:', error);
    }
  }

  /**
   * 标签页编辑后同步
   */
  static async syncTabAfterEdit(tabId: number, changeInfo: chrome.tabs.TabChangeInfo, _tab: chrome.tabs.Tab): Promise<void> {
    try {
      // 如果标签页URL或标题发生变化，同步到Workona映射
      if (changeInfo.url || changeInfo.title) {
        const workonaIdResult = await WorkonaTabManager.getWorkonaIdByChromeId(tabId);
        if (workonaIdResult.success && workonaIdResult.data) {
          console.log(`🔄 同步标签页变化: ${tabId} -> ${workonaIdResult.data}`);
          // 这里可以添加更多同步逻辑
        }
      }
    } catch (error) {
      console.error('同步标签页编辑失败:', error);
    }
  }

  /**
   * 处理标签页固定状态变化
   */
  static async handleTabPinnedStateChange(tabId: number, isPinned: boolean, _tab: chrome.tabs.Tab): Promise<void> {
    try {
      console.log(`🔄 标签页固定状态变化: ${tabId} -> ${isPinned ? '固定' : '取消固定'}`);
      
      // 获取标签页的Workona ID
      const workonaIdResult = await WorkonaTabManager.getWorkonaIdByChromeId(tabId);
      if (!workonaIdResult.success || !workonaIdResult.data) {
        return;
      }

      const workonaId = workonaIdResult.data;
      
      // 获取标签页元数据
      const metadataResult = await WorkonaTabManager.getTabMetadata(workonaId);
      if (!metadataResult.success || !metadataResult.data) {
        return;
      }

      const { workspaceId } = metadataResult.data;
      if (!workspaceId) {
        return;
      }

      if (isPinned) {
        // 添加到工作区的固定标签页列表
        await BackgroundTabManager.updateWorkspacePinnedTabIds(workspaceId, tabId);
      } else {
        // 从工作区的固定标签页列表中移除
        await BackgroundTabManager.removeFromWorkspacePinnedTabIds(workspaceId, tabId);
      }
    } catch (error) {
      console.error('处理标签页固定状态变化失败:', error);
    }
  }

  /**
   * 恢复标签页映射（浏览器重启后）
   */
  static async restoreTabMappingsAfterRestart(): Promise<void> {
    try {
      console.log('🔄 浏览器重启后恢复标签页映射...');
      
      // 获取所有现有标签页
      const tabs = await chrome.tabs.query({});
      console.log(`发现 ${tabs.length} 个标签页`);

      // 获取所有工作区
      const workspacesResult = await StorageManager.getWorkspaces();
      if (!workspacesResult.success) {
        console.error('获取工作区失败:', workspacesResult.error);
        return;
      }

      const workspaces = workspacesResult.data!;
      let restoredCount = 0;

      // 为每个工作区的网站恢复标签页映射
      for (const workspace of workspaces) {
        for (const website of workspace.websites) {
          // 查找匹配的标签页
          const matchingTabs = tabs.filter(tab => 
            tab.url && tab.url.includes(new URL(website.url).hostname)
          );

          for (const tab of matchingTabs) {
            if (!tab.id) continue;

            // 检查是否已有映射
            const existingResult = await WorkonaTabManager.getWorkonaIdByChromeId(tab.id);
            if (existingResult.success && existingResult.data) {
              continue; // 已有映射，跳过
            }

            // 创建新的映射
            const workonaId = WorkonaTabManager.generateWorkonaTabId(workspace.id);
            const mappingResult = await WorkonaTabManager.createTabIdMapping(
              workonaId,
              tab.id,
              workspace.id,
              website.id,
              {
                isWorkspaceCore: true,
                source: 'session_restored'
              }
            );

            if (mappingResult.success) {
              restoredCount++;
              console.log(`✅ 恢复标签页映射: ${tab.title} -> ${workonaId}`);
            }
          }
        }
      }

      console.log(`✅ 标签页映射恢复完成，共恢复 ${restoredCount} 个映射`);
    } catch (error) {
      console.error('恢复标签页映射失败:', error);
    }
  }

  /**
   * 更新工作区固定标签页ID
   */
  private static async updateWorkspacePinnedTabIds(workspaceId: string, newChromeId: number): Promise<void> {
    try {
      // 获取工作区
      const workspaceResult = await StorageManager.getWorkspace(workspaceId);
      if (!workspaceResult.success) {
        return;
      }

      const workspace = workspaceResult.data!;
      const pinnedTabIds = workspace.pinnedTabIds || [];

      // 添加新的固定标签页ID（如果不存在）
      if (!pinnedTabIds.includes(newChromeId)) {
        pinnedTabIds.push(newChromeId);
        
        // 更新工作区
        const updatedWorkspace = {
          ...workspace,
          pinnedTabIds,
          updatedAt: Date.now(),
        };

        // 保存更新
        const workspacesResult = await StorageManager.getWorkspaces();
        if (workspacesResult.success) {
          const workspaces = workspacesResult.data!;
          const workspaceIndex = workspaces.findIndex(w => w.id === workspaceId);
          if (workspaceIndex !== -1) {
            workspaces[workspaceIndex] = updatedWorkspace;
            await StorageManager.saveWorkspaces(workspaces);
          }
        }
      }
    } catch (error) {
      console.error('更新工作区固定标签页ID失败:', error);
    }
  }

  /**
   * 从工作区固定标签页ID中移除
   */
  private static async removeFromWorkspacePinnedTabIds(workspaceId: string, chromeId: number): Promise<void> {
    try {
      // 获取工作区
      const workspaceResult = await StorageManager.getWorkspace(workspaceId);
      if (!workspaceResult.success) {
        return;
      }

      const workspace = workspaceResult.data!;
      const pinnedTabIds = workspace.pinnedTabIds || [];

      // 移除固定标签页ID
      const updatedPinnedTabIds = pinnedTabIds.filter(id => id !== chromeId);
      
      if (updatedPinnedTabIds.length !== pinnedTabIds.length) {
        // 更新工作区
        const updatedWorkspace = {
          ...workspace,
          pinnedTabIds: updatedPinnedTabIds,
          updatedAt: Date.now(),
        };

        // 保存更新
        const workspacesResult = await StorageManager.getWorkspaces();
        if (workspacesResult.success) {
          const workspaces = workspacesResult.data!;
          const workspaceIndex = workspaces.findIndex(w => w.id === workspaceId);
          if (workspaceIndex !== -1) {
            workspaces[workspaceIndex] = updatedWorkspace;
            await StorageManager.saveWorkspaces(workspaces);
          }
        }
      }
    } catch (error) {
      console.error('从工作区固定标签页ID中移除失败:', error);
    }
  }

  /**
   * 清理所有工作区的固定状态
   */
  static async cleanupAllWorkspacePinnedStates(): Promise<void> {
    try {
      console.log('🧹 清理所有工作区的固定标签页状态...');
      
      // 获取所有工作区
      const workspacesResult = await StorageManager.getWorkspaces();
      if (!workspacesResult.success) {
        return;
      }

      const workspaces = workspacesResult.data!;
      let cleanedCount = 0;

      // 清理每个工作区的固定标签页ID
      for (const workspace of workspaces) {
        if (workspace.pinnedTabIds && workspace.pinnedTabIds.length > 0) {
          workspace.pinnedTabIds = [];
          workspace.updatedAt = Date.now();
          cleanedCount++;
        }
      }

      if (cleanedCount > 0) {
        // 保存更新
        await StorageManager.saveWorkspaces(workspaces);
        console.log(`✅ 已清理 ${cleanedCount} 个工作区的固定标签页状态`);
      }
    } catch (error) {
      console.error('清理工作区固定状态失败:', error);
    }
  }

  /**
   * 设置标签页事件监听器
   */
  static setupTabListeners(): void {
    // 监听标签页激活（记录活跃标签页状态）
    chrome.tabs.onActivated.addListener(async (activeInfo) => {
      try {
        console.log(`标签页激活: ${activeInfo.tabId}`);

        // 同步当前工作区状态
        await WorkspaceSessionManager.syncCurrentWorkspaceState();

        // 记录标签页活跃状态
        const workonaIdResult = await WorkonaTabManager.getWorkonaIdByChromeId(activeInfo.tabId);
        if (workonaIdResult.success && workonaIdResult.data) {
          console.log(`活跃标签页Workona ID: ${workonaIdResult.data}`);
        }
      } catch (error) {
        console.error('处理标签页激活事件失败:', error);
      }
    });

    // 监听标签页移动（记录标签页顺序变化）
    chrome.tabs.onMoved.addListener(async (_tabId, _moveInfo) => {
      try {
        // 延迟一点时间确保移动完成
        setTimeout(async () => {
          await WorkspaceSessionManager.syncCurrentWorkspaceState();
        }, 100);
      } catch (error) {
        console.error('处理标签页移动事件失败:', error);
      }
    });

    // 监听标签页创建（概念性重构：自动分类）
    chrome.tabs.onCreated.addListener(async (tab) => {
      try {
        console.log(`新标签页创建: ${tab.id} - ${tab.url}`);

        // 如果是新标签页或空白页，跳过自动分类
        if (!tab.url || tab.url === 'chrome://newtab/' || tab.url === 'about:blank') {
          return;
        }

        // 延迟执行自动分类，等待页面加载
        setTimeout(async () => {
          try {
            await TabClassificationService.autoClassifyNewTab(tab.id!, tab.url!);
          } catch (error) {
            console.error('自动分类标签页失败:', error);
          }
        }, 1000);
      } catch (error) {
        console.error('处理标签页创建事件失败:', error);
      }
    });

    // 监听标签页更新（增强版：包含自动分类）
    chrome.tabs.onUpdated.addListener(async (tabId, changeInfo, tab) => {
      try {
        // 处理固定状态变化
        if (changeInfo.pinned !== undefined) {
          await BackgroundTabManager.handleTabPinnedStateChange(tabId, changeInfo.pinned, tab);
        }

        // 处理URL或标题变化
        if (changeInfo.url || changeInfo.title) {
          await BackgroundTabManager.syncTabAfterEdit(tabId, changeInfo, tab);
        }

        // 如果标签页加载完成且有URL，尝试自动分类
        if (changeInfo.status === 'complete' && tab.url &&
            !tab.url.startsWith('chrome://') &&
            !tab.url.startsWith('chrome-extension://')) {

          // 检查是否已有Workona映射
          const workonaIdResult = await WorkonaTabManager.getWorkonaIdByChromeId(tabId);
          if (!workonaIdResult.success || !workonaIdResult.data) {
            // 没有映射，尝试自动分类
            setTimeout(async () => {
              try {
                await TabClassificationService.autoClassifyNewTab(tabId, tab.url!);
              } catch (error) {
                console.error('自动分类标签页失败:', error);
              }
            }, 500);
          }
        }
      } catch (error) {
        console.error('处理标签页更新事件失败:', error);
      }
    });

    // 监听标签页移除（增强版：清理 Workona ID 映射）
    chrome.tabs.onRemoved.addListener(async (tabId, _removeInfo) => {
      try {
        console.log(`标签页移除: ${tabId}`);

        // 清理Workona ID映射
        const workonaIdResult = await WorkonaTabManager.getWorkonaIdByChromeId(tabId);
        if (workonaIdResult.success && workonaIdResult.data) {
          const workonaId = workonaIdResult.data;
          console.log(`清理Workona ID映射: ${workonaId}`);

          // 移除映射
          await WorkonaTabManager.removeTabMapping(workonaId);

          // 从工作区的Workona标签页ID列表中移除
          const metadataResult = await WorkonaTabManager.getTabMetadata(workonaId);
          if (metadataResult.success && metadataResult.data) {
            const { workspaceId } = metadataResult.data;
            if (workspaceId) {
              // 这里可以添加从工作区移除Workona ID的逻辑
            }
          }
        }

        // 同步工作区状态
        await WorkspaceSessionManager.syncCurrentWorkspaceState();
      } catch (error) {
        console.error('处理标签页移除事件失败:', error);
      }
    });
  }

  /**
   * 获取标签页管理器状态
   */
  static async getTabManagerStatus(): Promise<{
    monitoringActive: boolean;
    totalTabs: number;
    mappedTabs: number;
    pinnedTabs: number;
  }> {
    try {
      const tabs = await chrome.tabs.query({});
      const pinnedTabs = tabs.filter(tab => tab.pinned);

      // 统计已映射的标签页
      let mappedCount = 0;
      for (const tab of tabs) {
        if (tab.id) {
          const result = await WorkonaTabManager.getWorkonaIdByChromeId(tab.id);
          if (result.success && result.data) {
            mappedCount++;
          }
        }
      }

      return {
        monitoringActive: UserTabsRealTimeMonitor.getMonitoringStatus().isMonitoring,
        totalTabs: tabs.length,
        mappedTabs: mappedCount,
        pinnedTabs: pinnedTabs.length,
      };
    } catch (error) {
      console.error('获取标签页管理器状态失败:', error);
      return {
        monitoringActive: false,
        totalTabs: 0,
        mappedTabs: 0,
        pinnedTabs: 0,
      };
    }
  }
}
