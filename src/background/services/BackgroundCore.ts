import { MigrationManager } from '../../utils/dataMigration';
import { StorageManager } from '../../utils/storage';
import { ServiceWorkerManager } from './ServiceWorkerManager';
import { BackgroundEventHandlers } from './BackgroundEventHandlers';
import { BackgroundTabManager } from './BackgroundTabManager';
import { BackgroundNotificationManager } from './BackgroundNotificationManager';

/**
 * 后台服务核心管理器
 * 职责：协调和管理所有后台服务的初始化和运行
 * 
 * 🎯 核心职责：
 * 1. 服务初始化协调
 * 2. 数据迁移管理
 * 3. 基础功能设置
 * 4. 服务状态监控
 */
export class BackgroundCore {
  private static isInitialized = false;
  private static initStartTime = 0;

  /**
   * 初始化后台服务
   */
  static async initialize(): Promise<void> {
    if (BackgroundCore.isInitialized) {
      console.log('⚠️ 后台服务已初始化，跳过重复初始化');
      return;
    }

    BackgroundCore.initStartTime = Date.now();
    console.log('🚀 开始初始化后台服务...');

    try {
      // 1. 设置基础功能
      await BackgroundCore.setupBasicFeatures();

      // 2. 初始化数据
      await BackgroundCore.initializeData();

      // 3. 启动监控服务
      await BackgroundCore.startMonitoringServices();

      // 4. 设置Service Worker生命周期管理
      ServiceWorkerManager.setupRuntimeListeners();

      // 5. 执行智能工作区状态管理
      await ServiceWorkerManager.smartWorkspaceStateManagement();

      BackgroundCore.isInitialized = true;
      const initTime = Date.now() - BackgroundCore.initStartTime;
      console.log(`✅ 后台服务初始化完成，耗时: ${initTime}ms`);
    } catch (error) {
      console.error('❌ 后台服务初始化失败:', error);
      throw error;
    }
  }

  /**
   * 设置基础功能
   */
  private static async setupBasicFeatures(): Promise<void> {
    console.log('🔧 设置基础功能...');
    
    try {
      // 初始化所有事件处理器
      await BackgroundEventHandlers.initializeAllEventHandlers();
      
      // 设置标签页监听器
      BackgroundTabManager.setupTabListeners();
      
      // 初始化通知管理器
      BackgroundNotificationManager.initializeNotificationManager();
      
      console.log('✅ 基础功能设置完成');
    } catch (error) {
      console.error('设置基础功能失败:', error);
      throw error;
    }
  }

  /**
   * 初始化数据
   */
  private static async initializeData(): Promise<void> {
    console.log('📊 初始化数据...');
    
    try {
      // 检查并执行数据迁移
      await BackgroundCore.checkAndMigrateData();
      
      // 初始化默认数据
      await BackgroundCore.initializeDefaultData();
      
      // 恢复标签页映射
      await BackgroundTabManager.restoreTabMappingsAfterRestart();
      
      console.log('✅ 数据初始化完成');
    } catch (error) {
      console.error('数据初始化失败:', error);
      throw error;
    }
  }

  /**
   * 启动监控服务
   */
  private static async startMonitoringServices(): Promise<void> {
    console.log('👁️ 启动监控服务...');
    
    try {
      // 启动用户标签页实时监控
      await BackgroundTabManager.startUserTabsRealTimeMonitoring();
      
      console.log('✅ 监控服务启动完成');
    } catch (error) {
      console.error('启动监控服务失败:', error);
      throw error;
    }
  }

  /**
   * 检查并执行数据迁移
   */
  private static async checkAndMigrateData(): Promise<void> {
    try {
      console.log('🔄 检查数据迁移需求...');
      
      const migrationResult = await MigrationManager.migrateToWorkonaFormat();
      if (migrationResult.success) {
        if (migrationResult.data) {
          console.log(`✅ 数据迁移完成`);
          BackgroundNotificationManager.showSuccessNotification('数据迁移完成');
        } else {
          console.log('ℹ️ 无需数据迁移');
        }
      } else {
        console.error('数据迁移失败:', migrationResult.error);
        BackgroundNotificationManager.showErrorNotification('数据迁移失败，请检查扩展状态');
      }
    } catch (error) {
      console.error('检查数据迁移时发生错误:', error);
    }
  }

  /**
   * 初始化默认数据
   */
  private static async initializeDefaultData(): Promise<void> {
    try {
      // 检查是否需要创建默认工作区
      const workspacesResult = await StorageManager.getWorkspaces();
      if (workspacesResult.success && workspacesResult.data!.length === 0) {
        console.log('🆕 创建默认工作区...');
        // 这里可以添加创建默认工作区的逻辑
      }
    } catch (error) {
      console.error('初始化默认数据失败:', error);
    }
  }

  /**
   * 重启后台服务
   */
  static async restart(): Promise<void> {
    console.log('🔄 重启后台服务...');
    
    try {
      // 重置初始化状态
      BackgroundCore.isInitialized = false;
      
      // 重新初始化
      await BackgroundCore.initialize();
      
      console.log('✅ 后台服务重启完成');
      BackgroundNotificationManager.showInfoNotification('后台服务已重启');
    } catch (error) {
      console.error('重启后台服务失败:', error);
      BackgroundNotificationManager.showErrorNotification('后台服务重启失败');
    }
  }

  /**
   * 获取后台服务状态
   */
  static async getServiceStatus(): Promise<{
    isInitialized: boolean;
    initTime: number;
    serviceWorkerInfo: any;
    eventHandlerStatus: any;
    tabManagerStatus: any;
    notificationManagerStatus: any;
  }> {
    try {
      const [tabManagerStatus] = await Promise.all([
        BackgroundTabManager.getTabManagerStatus(),
      ]);

      return {
        isInitialized: BackgroundCore.isInitialized,
        initTime: BackgroundCore.initStartTime,
        serviceWorkerInfo: ServiceWorkerManager.getServiceWorkerInfo(),
        eventHandlerStatus: BackgroundEventHandlers.getEventHandlerStatus(),
        tabManagerStatus,
        notificationManagerStatus: BackgroundNotificationManager.getNotificationManagerStatus(),
      };
    } catch (error) {
      console.error('获取服务状态失败:', error);
      return {
        isInitialized: false,
        initTime: 0,
        serviceWorkerInfo: null,
        eventHandlerStatus: null,
        tabManagerStatus: null,
        notificationManagerStatus: null,
      };
    }
  }

  /**
   * 清理后台服务
   */
  static async cleanup(): Promise<void> {
    console.log('🧹 清理后台服务...');
    
    try {
      // 清理工作区固定状态
      await BackgroundTabManager.cleanupAllWorkspacePinnedStates();
      
      // 清理通知历史
      await BackgroundNotificationManager.clearNotificationHistory();
      
      // 重置Service Worker状态
      ServiceWorkerManager.resetServiceWorkerState();
      
      console.log('✅ 后台服务清理完成');
    } catch (error) {
      console.error('清理后台服务失败:', error);
    }
  }

  /**
   * 检查服务健康状态
   */
  static async healthCheck(): Promise<{
    healthy: boolean;
    issues: string[];
    recommendations: string[];
  }> {
    const issues: string[] = [];
    const recommendations: string[] = [];

    try {
      // 检查初始化状态
      if (!BackgroundCore.isInitialized) {
        issues.push('后台服务未初始化');
        recommendations.push('重新启动扩展');
      }

      // 检查标签页管理器状态
      const tabStatus = await BackgroundTabManager.getTabManagerStatus();
      if (!tabStatus.monitoringActive) {
        issues.push('标签页监控未激活');
        recommendations.push('刷新标签页监控');
      }

      // 检查事件处理器状态
      const eventStatus = BackgroundEventHandlers.getEventHandlerStatus();
      if (!eventStatus.commandListenersActive) {
        issues.push('命令监听器未激活');
        recommendations.push('检查扩展权限');
      }

      return {
        healthy: issues.length === 0,
        issues,
        recommendations,
      };
    } catch (error) {
      console.error('健康检查失败:', error);
      return {
        healthy: false,
        issues: ['健康检查执行失败'],
        recommendations: ['重新启动扩展'],
      };
    }
  }
}
