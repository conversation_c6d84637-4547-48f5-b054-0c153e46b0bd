/**
 * 后台服务模块统一导出
 * 
 * 🎯 模块职责分离：
 * - ServiceWorkerManager: Service Worker生命周期管理
 * - BackgroundEventHandlers: 事件监听器管理
 * - BackgroundTabManager: 标签页相关的后台处理
 * - BackgroundNotificationManager: 通知管理
 * - BackgroundCore: 核心初始化和协调
 * 
 * 📋 重构说明：
 * 原来的1,146行background.ts文件已按照单一职责原则拆分为5个专门的服务类
 * 每个类只负责一个核心功能，提高了代码的可维护性和可测试性
 */

// Service Worker生命周期管理
export { ServiceWorkerManager, WorkspaceStateManager } from './ServiceWorkerManager';

// 事件处理器
export { BackgroundEventHandlers } from './BackgroundEventHandlers';

// 标签页管理
export { BackgroundTabManager } from './BackgroundTabManager';

// 通知管理
export { BackgroundNotificationManager } from './BackgroundNotificationManager';

// 核心协调器
export { BackgroundCore } from './BackgroundCore';
