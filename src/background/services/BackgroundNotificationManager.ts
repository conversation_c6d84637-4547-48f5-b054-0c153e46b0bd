/**
 * 后台通知管理器
 * 职责：处理各种通知和消息传递
 * 
 * 🎯 核心职责：
 * 1. 系统通知管理
 * 2. 用户标签页状态变化通知
 * 3. 工作区状态变化通知
 * 4. 侧边栏消息传递
 */
export class BackgroundNotificationManager {
  /**
   * 显示系统通知
   */
  static showNotification(message: string, icon?: string): void {
    try {
      if (chrome.notifications) {
        chrome.notifications.create({
          type: 'basic',
          iconUrl: icon || '/icons/icon-48.png',
          title: 'Workspace Pro',
          message: message
        });
      } else {
        console.log('通知:', message);
      }
    } catch (error) {
      console.error('显示通知失败:', error);
    }
  }

  /**
   * 通知全局用户标签页状态变化
   */
  static async notifyGlobalUserTabsStateChange(eventType: string): Promise<void> {
    try {
      // 立即发送通知，然后再延迟发送一次确保操作完成
      const sendNotification = async () => {
        try {
          await chrome.runtime.sendMessage({
            type: 'USER_TABS_STATE_CHANGED',
            eventType: eventType,
            timestamp: Date.now()
          });
        } catch (error) {
          // 忽略发送失败的错误（可能没有接收者）
        }
      };

      // 立即发送
      await sendNotification();
      
      // 延迟发送确保操作完成
      setTimeout(async () => {
        await sendNotification();
      }, 100);

      console.log(`📢 已发送用户标签页状态变化通知: ${eventType}`);
    } catch (error) {
      console.error('发送用户标签页状态变化通知失败:', error);
    }
  }

  /**
   * 通知工作区状态变化
   */
  static async notifyWorkspaceStateChange(workspaceId: string, eventType: string, data?: any): Promise<void> {
    try {
      await chrome.runtime.sendMessage({
        type: 'WORKSPACE_STATE_CHANGED',
        workspaceId: workspaceId,
        eventType: eventType,
        data: data,
        timestamp: Date.now()
      });

      console.log(`📢 已发送工作区状态变化通知: ${workspaceId} - ${eventType}`);
    } catch (error) {
      // 忽略发送失败的错误（可能没有接收者）
    }
  }

  /**
   * 通知标签页状态变化
   */
  static async notifyTabStateChange(tabId: number, eventType: string, data?: any): Promise<void> {
    try {
      await chrome.runtime.sendMessage({
        type: 'TAB_STATE_CHANGED',
        tabId: tabId,
        eventType: eventType,
        data: data,
        timestamp: Date.now()
      });

      console.log(`📢 已发送标签页状态变化通知: ${tabId} - ${eventType}`);
    } catch (error) {
      // 忽略发送失败的错误（可能没有接收者）
    }
  }

  /**
   * 通知侧边栏数据更新
   */
  static notifySidePanelUpdate(changes: { [key: string]: chrome.storage.StorageChange }): void {
    try {
      // 发送消息到侧边栏，通知数据更新
      chrome.runtime.sendMessage({
        type: 'STORAGE_CHANGED',
        changes: changes,
        timestamp: Date.now()
      }).catch(() => {
        // 忽略发送失败的错误（侧边栏可能未打开）
      });
    } catch (error) {
      console.error('通知侧边栏更新失败:', error);
    }
  }

  /**
   * 发送自定义消息
   */
  static async sendCustomMessage(type: string, data: any): Promise<void> {
    try {
      await chrome.runtime.sendMessage({
        type: type,
        data: data,
        timestamp: Date.now()
      });

      console.log(`📢 已发送自定义消息: ${type}`);
    } catch (error) {
      // 忽略发送失败的错误（可能没有接收者）
    }
  }

  /**
   * 批量发送通知
   */
  static async sendBatchNotifications(notifications: Array<{
    type: string;
    data: any;
  }>): Promise<void> {
    try {
      for (const notification of notifications) {
        await BackgroundNotificationManager.sendCustomMessage(notification.type, notification.data);
        
        // 添加小延迟避免消息过于频繁
        await new Promise(resolve => setTimeout(resolve, 10));
      }

      console.log(`📢 已发送 ${notifications.length} 个批量通知`);
    } catch (error) {
      console.error('发送批量通知失败:', error);
    }
  }

  /**
   * 显示成功通知
   */
  static showSuccessNotification(message: string): void {
    BackgroundNotificationManager.showNotification(`✅ ${message}`, '/icons/success.png');
  }

  /**
   * 显示错误通知
   */
  static showErrorNotification(message: string): void {
    BackgroundNotificationManager.showNotification(`❌ ${message}`, '/icons/error.png');
  }

  /**
   * 显示警告通知
   */
  static showWarningNotification(message: string): void {
    BackgroundNotificationManager.showNotification(`⚠️ ${message}`, '/icons/warning.png');
  }

  /**
   * 显示信息通知
   */
  static showInfoNotification(message: string): void {
    BackgroundNotificationManager.showNotification(`ℹ️ ${message}`, '/icons/info.png');
  }

  /**
   * 获取通知管理器状态
   */
  static getNotificationManagerStatus(): {
    notificationsSupported: boolean;
    runtimeMessagingSupported: boolean;
    lastNotificationTime: number | null;
  } {
    return {
      notificationsSupported: !!chrome.notifications,
      runtimeMessagingSupported: !!chrome.runtime?.sendMessage,
      lastNotificationTime: null, // 可以添加时间戳跟踪
    };
  }

  /**
   * 清理通知历史
   */
  static async clearNotificationHistory(): Promise<void> {
    try {
      if (chrome.notifications && chrome.notifications.clear) {
        // 清理所有通知
        chrome.notifications.getAll((notifications) => {
          for (const notificationId in notifications) {
            chrome.notifications.clear(notificationId);
          }
        });
        console.log('✅ 通知历史已清理');
      }
    } catch (error) {
      console.error('清理通知历史失败:', error);
    }
  }

  /**
   * 设置通知点击监听器
   */
  static setupNotificationListeners(): void {
    if (chrome.notifications && chrome.notifications.onClicked) {
      chrome.notifications.onClicked.addListener((notificationId) => {
        console.log(`通知被点击: ${notificationId}`);
        
        // 可以根据通知ID执行相应操作
        // 例如：打开侧边栏、切换工作区等
      });
    }

    if (chrome.notifications && chrome.notifications.onClosed) {
      chrome.notifications.onClosed.addListener((notificationId, byUser) => {
        console.log(`通知被关闭: ${notificationId}, 用户操作: ${byUser}`);
      });
    }
  }

  /**
   * 初始化通知管理器
   */
  static initializeNotificationManager(): void {
    try {
      console.log('🔧 初始化通知管理器...');
      
      // 设置通知监听器
      BackgroundNotificationManager.setupNotificationListeners();
      
      console.log('✅ 通知管理器初始化完成');
    } catch (error) {
      console.error('初始化通知管理器失败:', error);
    }
  }
}
