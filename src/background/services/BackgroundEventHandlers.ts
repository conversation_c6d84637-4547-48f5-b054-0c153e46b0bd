import { StorageManager } from '../../utils/storage';
import { WorkspaceSwitcher } from '../../utils/workspaceSwitcher';
import { COMMANDS } from '../../utils/constants';

/**
 * 后台事件处理器
 * 职责：处理Chrome扩展的各种事件监听和响应
 * 
 * 🎯 核心职责：
 * 1. 命令监听器设置
 * 2. 存储变化监听
 * 3. 侧边栏管理
 * 4. 快捷键处理
 */
export class BackgroundEventHandlers {
  /**
   * 设置侧边栏
   */
  static async setupSidePanel(): Promise<void> {
    try {
      if (chrome.sidePanel) {
        await chrome.sidePanel.setPanelBehavior({ openPanelOnActionClick: true });
        console.log('✅ 侧边栏设置完成');
      } else {
        console.warn('⚠️ chrome.sidePanel API 不可用');
      }
    } catch (error) {
      console.error('设置侧边栏失败:', error);
    }
  }

  /**
   * 设置命令监听器
   */
  static setupCommandListeners(): void {
    chrome.commands.onCommand.addListener(async (command) => {
      console.log('Command received:', command);
      
      try {
        switch (command) {
          case COMMANDS.TOGGLE_SIDEPANEL:
            await BackgroundEventHandlers.toggleSidePanel();
            break;
          case COMMANDS.SWITCH_WORKSPACE_1:
            await BackgroundEventHandlers.switchToWorkspaceByIndex(0);
            break;
          case COMMANDS.SWITCH_WORKSPACE_2:
            await BackgroundEventHandlers.switchToWorkspaceByIndex(1);
            break;
          case COMMANDS.SWITCH_WORKSPACE_3:
            await BackgroundEventHandlers.switchToWorkspaceByIndex(2);
            break;
          default:
            console.log('未知命令:', command);
        }
      } catch (error) {
        console.error('处理命令时发生错误:', error);
      }
    });
  }

  /**
   * 设置存储监听器
   */
  static setupStorageListeners(): void {
    StorageManager.onChanged((changes) => {
      BackgroundEventHandlers.notifySidePanelUpdate(changes);
    });
  }

  /**
   * 切换到指定索引的工作区
   */
  private static async switchToWorkspaceByIndex(index: number): Promise<void> {
    try {
      console.log(`🔄 切换到工作区索引: ${index}`);
      
      // 获取所有工作区
      const workspacesResult = await StorageManager.getWorkspaces();
      if (!workspacesResult.success) {
        console.error('获取工作区列表失败:', workspacesResult.error);
        return;
      }

      const workspaces = workspacesResult.data!;
      if (index >= workspaces.length) {
        console.log(`工作区索引 ${index} 超出范围，总共有 ${workspaces.length} 个工作区`);
        return;
      }

      const targetWorkspace = workspaces[index];
      console.log(`切换到工作区: ${targetWorkspace.name}`);

      // 执行工作区切换
      const switchResult = await WorkspaceSwitcher.switchToWorkspace(targetWorkspace.id);
      if (switchResult.success) {
        console.log(`✅ 成功切换到工作区: ${targetWorkspace.name}`);
      } else {
        console.error('工作区切换失败:', switchResult.error);
      }
    } catch (error) {
      console.error('切换工作区时发生错误:', error);
    }
  }

  /**
   * 切换侧边栏显示状态
   */
  private static async toggleSidePanel(): Promise<void> {
    try {
      if (chrome.sidePanel) {
        // 获取当前活跃标签页
        const tabs = await chrome.tabs.query({ active: true, currentWindow: true });
        if (tabs.length > 0) {
          const tabId = tabs[0].id!;
          
          // 切换侧边栏
          await chrome.sidePanel.open({ tabId });
          console.log('✅ 侧边栏已打开');
        }
      } else {
        console.warn('⚠️ chrome.sidePanel API 不可用');
      }
    } catch (error) {
      console.error('切换侧边栏失败:', error);
    }
  }

  /**
   * 通知侧边栏更新
   */
  private static notifySidePanelUpdate(changes: { [key: string]: chrome.storage.StorageChange }): void {
    try {
      // 发送消息到侧边栏，通知数据更新
      chrome.runtime.sendMessage({
        type: 'STORAGE_CHANGED',
        changes: changes
      }).catch(() => {
        // 忽略发送失败的错误（侧边栏可能未打开）
      });
    } catch (error) {
      console.error('通知侧边栏更新失败:', error);
    }
  }

  /**
   * 显示通知
   */
  static showNotification(message: string, icon?: string): void {
    try {
      if (chrome.notifications) {
        chrome.notifications.create({
          type: 'basic',
          iconUrl: icon || '/icons/icon-48.png',
          title: 'Workspace Pro',
          message: message
        });
      } else {
        console.log('通知:', message);
      }
    } catch (error) {
      console.error('显示通知失败:', error);
    }
  }

  /**
   * 获取事件处理器状态
   */
  static getEventHandlerStatus(): {
    commandListenersActive: boolean;
    storageListenersActive: boolean;
    sidePanelSupported: boolean;
    notificationsSupported: boolean;
  } {
    return {
      commandListenersActive: !!chrome.commands?.onCommand,
      storageListenersActive: !!chrome.storage?.onChanged,
      sidePanelSupported: !!chrome.sidePanel,
      notificationsSupported: !!chrome.notifications,
    };
  }

  /**
   * 初始化所有事件监听器
   */
  static async initializeAllEventHandlers(): Promise<void> {
    try {
      console.log('🔧 初始化事件处理器...');
      
      // 设置侧边栏
      await BackgroundEventHandlers.setupSidePanel();
      
      // 设置命令监听器
      BackgroundEventHandlers.setupCommandListeners();
      
      // 设置存储监听器
      BackgroundEventHandlers.setupStorageListeners();
      
      console.log('✅ 所有事件处理器初始化完成');
    } catch (error) {
      console.error('初始化事件处理器失败:', error);
    }
  }
}
