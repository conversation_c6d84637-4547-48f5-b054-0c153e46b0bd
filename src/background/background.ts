// Service Worker 环境 polyfill
if (typeof window === 'undefined') {
  // 创建一个简单的 window 对象 polyfill
  (globalThis as any).window = {
    dispatchEvent: (event: Event) => {
      // 在 Service Worker 中，我们可以简单地忽略这些事件
      console.log('Service Worker: 忽略 window.dispatchEvent 调用:', event.type);
      return true;
    }
  };
}

import { BackgroundCore } from './services/BackgroundCore';

/**
 * 后台服务主入口 - 重构后的简化版本
 * 
 * 🎯 重构说明：
 * 原来的1,146行background.ts文件已按照单一职责原则拆分为5个专门的服务类：
 * - ServiceWorkerManager: Service Worker生命周期管理 (./services/ServiceWorkerManager.ts)
 * - BackgroundEventHandlers: 事件监听器管理 (./services/BackgroundEventHandlers.ts)
 * - BackgroundTabManager: 标签页相关的后台处理 (./services/BackgroundTabManager.ts)
 * - BackgroundNotificationManager: 通知管理 (./services/BackgroundNotificationManager.ts)
 * - BackgroundCore: 核心初始化和协调 (./services/BackgroundCore.ts)
 * 
 * 📋 向后兼容：
 * 所有原有功能都通过BackgroundCore进行协调，确保扩展功能完全正常
 */

/**
 * 后台服务类（简化版）
 * 职责：作为所有后台服务的统一入口点
 */
class BackgroundService {
  constructor() {
    this.init();
  }

  /**
   * 初始化后台服务
   */
  private async init(): Promise<void> {
    try {
      console.log('🚀 启动后台服务...');
      
      // 使用BackgroundCore进行统一初始化
      await BackgroundCore.initialize();
      
      console.log('✅ 后台服务启动完成');
    } catch (error) {
      console.error('❌ 后台服务启动失败:', error);
      
      // 尝试重启
      setTimeout(async () => {
        console.log('🔄 尝试重启后台服务...');
        try {
          await BackgroundCore.restart();
        } catch (restartError) {
          console.error('❌ 重启失败:', restartError);
        }
      }, 5000);
    }
  }
}

// 启动后台服务
new BackgroundService();

// 导出服务类供其他模块使用
export { BackgroundService };

// 重新导出拆分后的服务类，保持向后兼容
export {
  ServiceWorkerManager,
  WorkspaceStateManager,
  BackgroundEventHandlers,
  BackgroundTabManager,
  BackgroundNotificationManager,
  BackgroundCore
} from './services';
