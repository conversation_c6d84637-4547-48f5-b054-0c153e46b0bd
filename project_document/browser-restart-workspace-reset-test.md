# 浏览器重启后工作区状态重置功能测试指南

## 🎯 功能概述

实现了浏览器重启后自动重置工作区选择状态的功能，确保用户在浏览器重启后需要手动选择工作区，而不是自动恢复之前的工作区状态。

## 🔧 实现的功能

### 1. **初始状态重置**
- 浏览器重启后，扩展清除之前选中的工作区状态
- 不保持上次关闭前的工作区选择

### 2. **UI状态同步**
- 侧边栏界面显示为无工作区被激活的状态
- 所有工作区按钮/指示器都处于未选中状态

### 3. **存储清理**
- 清除chrome.storage中的当前工作区ID
- 重置所有工作区的isActive状态
- 清除工作区会话状态

### 4. **标签页管理**
- 重启后不自动激活任何工作区的标签页
- 让用户手动选择要使用的工作区

### 5. **状态一致性**
- 确保扩展内部状态、UI显示状态、实际标签页状态都保持一致的"未选择"状态

## 🧪 测试步骤

### 测试前准备
1. 确保Chrome扩展已安装并启用
2. 创建至少2个工作区，每个工作区添加一些网站
3. 选择其中一个工作区作为活跃工作区

### 测试步骤1：基本重启测试
1. **选择工作区**：在扩展中选择一个工作区，确保它显示为激活状态（绿色圆点）
2. **确认状态**：验证侧边栏显示该工作区为激活状态
3. **关闭浏览器**：完全关闭Chrome浏览器（确保所有Chrome进程都结束）
4. **重新打开浏览器**：启动Chrome浏览器
5. **检查扩展状态**：打开扩展侧边栏
6. **验证结果**：
   - ✅ 应该看到没有任何工作区显示为激活状态
   - ✅ 所有工作区都没有绿色圆点指示器
   - ✅ 需要手动点击工作区才能激活

### 测试步骤2：多次重启测试
1. **选择工作区A**：激活工作区A
2. **第一次重启**：关闭并重新打开浏览器
3. **验证重置**：确认没有工作区被激活
4. **选择工作区B**：激活工作区B
5. **第二次重启**：关闭并重新打开浏览器
6. **验证结果**：再次确认没有工作区被激活

### 测试步骤3：扩展重新加载测试
1. **选择工作区**：激活一个工作区
2. **重新加载扩展**：在Chrome扩展管理页面重新加载扩展
3. **验证结果**：确认工作区状态被重置

### 测试步骤4：Service Worker唤醒测试
1. **选择工作区**：激活一个工作区
2. **等待Service Worker休眠**：等待几分钟让Service Worker进入休眠状态
3. **触发唤醒**：点击扩展图标或执行其他操作唤醒Service Worker
4. **验证结果**：工作区状态应该保持不变（这是正常的Service Worker唤醒，不是浏览器重启）

## 🔍 验证要点

### UI状态验证
- [ ] 侧边栏中没有工作区显示绿色圆点
- [ ] 工作区列表中所有项目都显示为未激活状态
- [ ] 点击工作区时能正常激活并显示绿色圆点

### 存储状态验证
可以在Chrome开发者工具中检查：
```javascript
// 在扩展的Service Worker控制台中执行
chrome.storage.local.get(['activeWorkspaceId'], (result) => {
  console.log('Active workspace ID:', result.activeWorkspaceId); // 应该是null
});

chrome.storage.local.get(['workspaces'], (result) => {
  console.log('Workspaces:', result.workspaces);
  // 所有工作区的isActive属性都应该是false
});
```

### 日志验证
在扩展的Service Worker控制台中应该看到：
```
🚀 Chrome扩展启动事件触发 - 浏览器重启
🔄 浏览器重启检测 - 开始重置工作区选择状态
✅ 已清除活跃工作区ID
✅ 已重置所有工作区的激活状态
✅ 已清除工作区会话状态
✅ 已通知侧边栏工作区状态重置
✅ 浏览器重启后工作区状态重置完成
```

## 🐛 可能的问题和解决方案

### 问题1：重启后工作区仍然显示为激活
**可能原因**：
- Service Worker没有正确检测到浏览器重启
- 存储清理失败

**解决方案**：
- 检查Service Worker控制台日志
- 手动清理存储数据

### 问题2：UI没有更新
**可能原因**：
- 消息传递失败
- React组件没有正确监听状态变化

**解决方案**：
- 检查消息监听器是否正确设置
- 手动刷新侧边栏

### 问题3：Service Worker频繁重启导致状态丢失
**可能原因**：
- Service Worker生命周期管理问题

**解决方案**：
- 检查时间阈值设置
- 验证isFirstRealStartup标志

## 📝 技术实现细节

### 关键文件修改
1. `src/background/background.ts`：添加了浏览器重启检测和状态重置逻辑
2. `src/utils/workspaceSessionManager.ts`：添加了clearCurrentSession方法
3. `src/hooks/useWorkspaces.ts`：添加了状态重置消息监听

### 核心方法
- `resetWorkspaceStateOnBrowserRestart()`：主要的状态重置方法
- `clearWorkspaceSessionState()`：清除会话状态
- `notifyWorkspaceStateReset()`：通知UI更新

### 触发条件
- Chrome扩展的onStartup事件
- Chrome扩展的onInstalled事件（首次安装时）
- Service Worker真正的启动（区别于唤醒）

## ✅ 预期行为

**正常情况下**：
- 浏览器重启 → 工作区状态重置 → 用户需要手动选择工作区
- Service Worker唤醒 → 保持现有工作区状态 → 不影响用户体验

**异常情况下**：
- 即使发生错误，也会尝试重置状态以确保用户需求得到满足

## 🚀 快速测试命令

在Chrome扩展的Service Worker控制台中可以手动测试：

```javascript
// 检查当前状态
chrome.storage.local.get(['activeWorkspaceId', 'workspaces'], console.log);

// 手动触发重置（仅用于测试）
// 注意：这需要访问BackgroundService实例
```
