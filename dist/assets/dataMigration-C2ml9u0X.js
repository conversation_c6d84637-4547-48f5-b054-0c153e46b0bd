const STORAGE_KEYS = {
  WORKSPACES: "workspaces",
  SETTINGS: "settings",
  ACTIVE_WORKSPACE_ID: "activeWorkspaceId",
  LAST_ACTIVE_WORKSPACE_IDS: "lastActiveWorkspaceIds"
};
const WORKONA_STORAGE_KEYS = {
  TAB_ID_MAPPINGS: "workonaTabIdMappings",
  LOCAL_OPEN_WORKSPACES: "localOpenWorkspaces",
  TAB_GROUPS: "tabGroups",
  WORKSPACE_SESSIONS: "workspaceSessions",
  GLOBAL_WORKSPACE_WINDOW_ID: "globalWorkspaceWindowId",
  DATA_VERSION: "workonaDataVersion",
  MIGRATION_BACKUP: "migrationBackup"
};
const DEFAULT_SETTINGS = {
  autoCloseOtherTabs: true,
  preserveUserOpenedTabs: false,
  defaultWorkspaceOnStartup: "",
  sidebarWidth: 320,
  theme: "dark",
  showFavicons: true,
  confirmBeforeDelete: true,
  maxRecentWorkspaces: 5
};
const WORKSPACE_COLORS = [
  "#3b82f6",
  // blue
  "#10b981",
  // emerald
  "#f59e0b",
  // amber
  "#ef4444",
  // red
  "#8b5cf6",
  // violet
  "#06b6d4",
  // cyan
  "#84cc16",
  // lime
  "#f97316",
  // orange
  "#ec4899",
  // pink
  "#6366f1"
  // indigo
];
const WORKSPACE_ICONS = [
  "🚀",
  "💼",
  "🔬",
  "🎨",
  "📊",
  "🛠️",
  "📚",
  "💡",
  "🎯",
  "⚡",
  "🌟",
  "🔥",
  "💎",
  "🎪",
  "🎭",
  "🎨",
  "🎵",
  "🎮",
  "🏆",
  "🎊",
  "📱",
  "💻",
  "🖥️",
  "⌨️",
  "🖱️",
  "🖨️",
  "📷",
  "📹",
  "🎥",
  "📺",
  "🔍",
  "🔎",
  "🔬",
  "🔭",
  "📡",
  "🛰️",
  "🚁",
  "✈️",
  "🛸"
];
const ERROR_CODES = {
  WORKSPACE_NOT_FOUND: "WORKSPACE_NOT_FOUND",
  WEBSITE_NOT_FOUND: "WEBSITE_NOT_FOUND",
  STORAGE_ERROR: "STORAGE_ERROR",
  TAB_ERROR: "TAB_ERROR",
  WINDOW_ERROR: "WINDOW_ERROR",
  PERMISSION_DENIED: "PERMISSION_DENIED",
  INVALID_URL: "INVALID_URL",
  DUPLICATE_WORKSPACE: "DUPLICATE_WORKSPACE",
  DUPLICATE_WEBSITE: "DUPLICATE_WEBSITE",
  WORKSPACE_ERROR: "WORKSPACE_ERROR",
  SYSTEM_ERROR: "SYSTEM_ERROR"
};
const COMMANDS = {
  SWITCH_WORKSPACE_1: "switch-workspace-1",
  SWITCH_WORKSPACE_2: "switch-workspace-2",
  SWITCH_WORKSPACE_3: "switch-workspace-3",
  TOGGLE_SIDEPANEL: "toggle-sidepanel"
};
const DEFAULT_FAVICON = 'data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><circle cx="12" cy="12" r="10"/><path d="m9 12 2 2 4-4"/></svg>';

class ImportDataProcessor {
  /**
   * 处理导入数据后的自动映射补全
   */
  static async processImportedData(importData) {
    try {
      console.log("🔄 开始处理导入数据的系统映射补全...");
      await this.completeWorkspaceWorkonaFields(importData.workspaces);
      await this.generateTabIdMappings(importData.workspaces);
      await this.generateWorkspaceSessions(importData.workspaces);
      await this.initializeSystemMappings();
      console.log("✅ 导入数据系统映射补全完成");
      return { success: true };
    } catch (error) {
      console.error("❌ 导入数据处理失败:", error);
      return {
        success: false,
        error: {
          code: ERROR_CODES.STORAGE_ERROR,
          message: "Failed to process imported data",
          details: error
        }
      };
    }
  }
  /**
   * 补全工作区的 Workona 风格字段
   */
  static async completeWorkspaceWorkonaFields(workspaces) {
    console.log("📝 补全工作区 Workona 风格字段...");
    const updatedWorkspaces = workspaces.map((workspace, index) => {
      const updatedWorkspace = {
        ...workspace,
        // 如果缺少 Workona 字段，自动补全
        type: workspace.type || "saved",
        pos: workspace.pos || Date.now() + index,
        state: workspace.state || "inactive",
        workonaTabIds: workspace.workonaTabIds || [],
        sessionId: workspace.sessionId || `session_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
        tabOrder: workspace.tabOrder || [],
        isActive: false
        // 导入后所有工作区都不激活
      };
      if (!updatedWorkspace.workonaTabIds || updatedWorkspace.workonaTabIds.length === 0 && updatedWorkspace.websites.length > 0) {
        updatedWorkspace.workonaTabIds = updatedWorkspace.websites.map(
          (website) => `t-${updatedWorkspace.id}-${website.id}`
        );
      }
      if (!updatedWorkspace.tabOrder || updatedWorkspace.tabOrder.length === 0 && updatedWorkspace.websites.length > 0) {
        updatedWorkspace.tabOrder = updatedWorkspace.websites.map((website) => website.id);
      }
      return updatedWorkspace;
    });
    await StorageManager.saveWorkspaces(updatedWorkspaces);
    console.log(`✅ 已补全 ${updatedWorkspaces.length} 个工作区的 Workona 字段`);
  }
  /**
   * 生成标签页ID映射
   */
  static async generateTabIdMappings(workspaces) {
    console.log("🔗 生成标签页ID映射...");
    const tabIdMappings = [];
    for (const workspace of workspaces) {
      for (const website of workspace.websites) {
        const workonaId = `t-${workspace.id}-${website.id}`;
        const mapping = {
          workonaId,
          chromeId: -1,
          // 导入时没有实际的 Chrome 标签页ID，使用-1表示无效
          workspaceId: workspace.id,
          websiteId: website.id,
          isWorkspaceCore: true,
          // 工作区网站默认为核心标签页
          tabType: "core",
          // 添加必需的tabType字段
          createdAt: Date.now(),
          lastSyncAt: Date.now(),
          // 添加必需的lastSyncAt字段
          metadata: {
            source: "workspace_website",
            addedToWorkspaceAt: Date.now(),
            isPinned: website.isPinned || false,
            pinnedAt: website.isPinned ? Date.now() : void 0
          }
        };
        tabIdMappings.push(mapping);
      }
    }
    await StorageManager.saveTabIdMappings(tabIdMappings);
    console.log(`✅ 已生成 ${tabIdMappings.length} 个标签页ID映射`);
  }
  /**
   * 生成工作区会话数据
   */
  static async generateWorkspaceSessions(workspaces) {
    console.log("📊 生成工作区会话数据...");
    const workspaceSessions = {};
    for (const workspace of workspaces) {
      const session = {
        workspaceId: workspace.id,
        tabs: {},
        // 空的标签页映射，导入时没有实际标签页
        tabOrder: workspace.tabOrder || workspace.websites.map((w) => w.id),
        activeTabId: workspace.websites.length > 0 ? workspace.websites[0].id : void 0,
        lastActiveAt: Date.now(),
        windowId: void 0
        // 导入时没有实际的窗口ID
      };
      workspaceSessions[workspace.id] = session;
    }
    await StorageManager.saveWorkspaceSessions(workspaceSessions);
    console.log(`✅ 已生成 ${Object.keys(workspaceSessions).length} 个工作区会话`);
  }
  /**
   * 初始化其他系统映射
   */
  static async initializeSystemMappings() {
    console.log("🔧 初始化其他系统映射...");
    await StorageManager.saveLocalOpenWorkspaces({});
    await StorageManager.saveTabGroups({});
    await StorageManager.clearGlobalWorkspaceWindowId();
    await StorageManager.saveDataVersion("2.0.0");
    console.log("✅ 系统映射初始化完成");
  }
  /**
   * 验证导入数据的完整性
   */
  static validateImportedData(importData) {
    const errors = [];
    if (!importData.workspaces || !Array.isArray(importData.workspaces)) {
      errors.push("缺少工作区数据或格式错误");
    }
    if (importData.workspaces) {
      for (let i = 0; i < importData.workspaces.length; i++) {
        const workspace = importData.workspaces[i];
        if (!workspace.id) {
          errors.push(`工作区 ${i + 1} 缺少ID`);
        }
        if (!workspace.name) {
          errors.push(`工作区 ${i + 1} 缺少名称`);
        }
        if (!workspace.websites || !Array.isArray(workspace.websites)) {
          errors.push(`工作区 ${i + 1} 缺少网站数据或格式错误`);
        } else {
          for (let j = 0; j < workspace.websites.length; j++) {
            const website = workspace.websites[j];
            if (!website.id) {
              errors.push(`工作区 ${i + 1} 的网站 ${j + 1} 缺少ID`);
            }
            if (!website.url) {
              errors.push(`工作区 ${i + 1} 的网站 ${j + 1} 缺少URL`);
            }
          }
        }
      }
    }
    return {
      isValid: errors.length === 0,
      errors
    };
  }
  /**
   * 清理导入数据中的无效字段
   */
  static cleanImportData(importData) {
    const cleanedData = { ...importData };
    if (cleanedData.workspaces) {
      cleanedData.workspaces = cleanedData.workspaces.map((workspace) => ({
        ...workspace,
        isActive: false,
        // 确保导入后所有工作区都不激活
        // 移除可能导致冲突的字段
        windowId: void 0,
        lastActiveAt: void 0
      }));
    }
    cleanedData.activeWorkspaceId = null;
    cleanedData.globalWorkspaceWindowId = null;
    return cleanedData;
  }
}

class StorageManager {
  /**
   * 获取所有存储数据
   */
  static async getAllData() {
    try {
      const result = await chrome.storage.local.get([
        STORAGE_KEYS.WORKSPACES,
        STORAGE_KEYS.SETTINGS,
        STORAGE_KEYS.ACTIVE_WORKSPACE_ID,
        STORAGE_KEYS.LAST_ACTIVE_WORKSPACE_IDS,
        // Workona 风格数据
        WORKONA_STORAGE_KEYS.TAB_ID_MAPPINGS,
        WORKONA_STORAGE_KEYS.LOCAL_OPEN_WORKSPACES,
        WORKONA_STORAGE_KEYS.TAB_GROUPS,
        WORKONA_STORAGE_KEYS.WORKSPACE_SESSIONS,
        WORKONA_STORAGE_KEYS.GLOBAL_WORKSPACE_WINDOW_ID,
        WORKONA_STORAGE_KEYS.DATA_VERSION
      ]);
      const data = {
        workspaces: result[STORAGE_KEYS.WORKSPACES] || [],
        settings: { ...DEFAULT_SETTINGS, ...result[STORAGE_KEYS.SETTINGS] },
        activeWorkspaceId: result[STORAGE_KEYS.ACTIVE_WORKSPACE_ID] || null,
        lastActiveWorkspaceIds: result[STORAGE_KEYS.LAST_ACTIVE_WORKSPACE_IDS] || [],
        // Workona 风格扩展数据（可选）
        tabIdMappings: result[WORKONA_STORAGE_KEYS.TAB_ID_MAPPINGS] || [],
        localOpenWorkspaces: result[WORKONA_STORAGE_KEYS.LOCAL_OPEN_WORKSPACES] || {},
        tabGroups: result[WORKONA_STORAGE_KEYS.TAB_GROUPS] || {},
        workspaceSessions: result[WORKONA_STORAGE_KEYS.WORKSPACE_SESSIONS] || {},
        globalWorkspaceWindowId: result[WORKONA_STORAGE_KEYS.GLOBAL_WORKSPACE_WINDOW_ID] || void 0,
        dataVersion: result[WORKONA_STORAGE_KEYS.DATA_VERSION] || "1.0.0"
      };
      return { success: true, data };
    } catch (error) {
      return {
        success: false,
        error: {
          code: ERROR_CODES.STORAGE_ERROR,
          message: "Failed to get storage data",
          details: error
        }
      };
    }
  }
  /**
   * 获取所有工作区
   */
  static async getWorkspaces() {
    try {
      const result = await chrome.storage.local.get(STORAGE_KEYS.WORKSPACES);
      const workspaces = result[STORAGE_KEYS.WORKSPACES] || [];
      workspaces.sort((a, b) => a.order - b.order);
      return { success: true, data: workspaces };
    } catch (error) {
      return {
        success: false,
        error: {
          code: ERROR_CODES.STORAGE_ERROR,
          message: "Failed to get workspaces",
          details: error
        }
      };
    }
  }
  /**
   * 保存工作区列表
   */
  static async saveWorkspaces(workspaces) {
    try {
      await chrome.storage.local.set({
        [STORAGE_KEYS.WORKSPACES]: workspaces
      });
      return { success: true };
    } catch (error) {
      return {
        success: false,
        error: {
          code: ERROR_CODES.STORAGE_ERROR,
          message: "Failed to save workspaces",
          details: error
        }
      };
    }
  }
  /**
   * 获取单个工作区
   */
  static async getWorkspace(id) {
    const result = await this.getWorkspaces();
    if (!result.success) {
      return {
        success: false,
        error: result.error
      };
    }
    const workspace = result.data.find((w) => w.id === id);
    if (!workspace) {
      return {
        success: false,
        error: {
          code: ERROR_CODES.WORKSPACE_NOT_FOUND,
          message: `Workspace with id ${id} not found`
        }
      };
    }
    return { success: true, data: workspace };
  }
  /**
   * 获取设置
   */
  static async getSettings() {
    try {
      const result = await chrome.storage.local.get(STORAGE_KEYS.SETTINGS);
      const settings = { ...DEFAULT_SETTINGS, ...result[STORAGE_KEYS.SETTINGS] };
      return { success: true, data: settings };
    } catch (error) {
      return {
        success: false,
        error: {
          code: ERROR_CODES.STORAGE_ERROR,
          message: "Failed to get settings",
          details: error
        }
      };
    }
  }
  /**
   * 保存设置
   */
  static async saveSettings(settings) {
    try {
      const currentResult = await this.getSettings();
      if (!currentResult.success) {
        return {
          success: false,
          error: currentResult.error
        };
      }
      const updatedSettings = { ...currentResult.data, ...settings };
      await chrome.storage.local.set({
        [STORAGE_KEYS.SETTINGS]: updatedSettings
      });
      return { success: true };
    } catch (error) {
      return {
        success: false,
        error: {
          code: ERROR_CODES.STORAGE_ERROR,
          message: "Failed to save settings",
          details: error
        }
      };
    }
  }
  /**
   * 获取当前活跃工作区ID
   */
  static async getActiveWorkspaceId() {
    try {
      const result = await chrome.storage.local.get(STORAGE_KEYS.ACTIVE_WORKSPACE_ID);
      return { success: true, data: result[STORAGE_KEYS.ACTIVE_WORKSPACE_ID] || null };
    } catch (error) {
      return {
        success: false,
        error: {
          code: ERROR_CODES.STORAGE_ERROR,
          message: "Failed to get active workspace ID",
          details: error
        }
      };
    }
  }
  /**
   * 设置当前活跃工作区ID
   */
  static async setActiveWorkspaceId(id) {
    try {
      const currentIdResult = await this.getActiveWorkspaceId();
      const currentId = currentIdResult.success ? currentIdResult.data : null;
      if (currentId !== id) {
        const stack = new Error().stack;
        const caller = stack?.split("\n")[2]?.trim() || "unknown";
        if (id === null) {
          console.warn(`🚨 工作区状态被清除: ${currentId} -> null`);
          console.warn(`🔍 调用来源: ${caller}`);
          console.warn(`📋 调用栈:`, stack);
        } else {
          console.log(`🔄 工作区状态变化: ${currentId} -> ${id}`);
          console.log(`🔍 调用来源: ${caller}`);
        }
      }
      await chrome.storage.local.set({
        [STORAGE_KEYS.ACTIVE_WORKSPACE_ID]: id
      });
      if (id) {
        await this.updateLastActiveWorkspaces(id);
      }
      return { success: true };
    } catch (error) {
      return {
        success: false,
        error: {
          code: ERROR_CODES.STORAGE_ERROR,
          message: "Failed to set active workspace ID",
          details: error
        }
      };
    }
  }
  /**
   * 更新最近使用的工作区列表
   */
  static async updateLastActiveWorkspaces(workspaceId) {
    try {
      const result = await chrome.storage.local.get(STORAGE_KEYS.LAST_ACTIVE_WORKSPACE_IDS);
      let lastActiveIds = result[STORAGE_KEYS.LAST_ACTIVE_WORKSPACE_IDS] || [];
      lastActiveIds = lastActiveIds.filter((id) => id !== workspaceId);
      lastActiveIds.unshift(workspaceId);
      const settingsResult = await this.getSettings();
      const maxRecent = settingsResult.success ? settingsResult.data.maxRecentWorkspaces : 5;
      lastActiveIds = lastActiveIds.slice(0, maxRecent);
      await chrome.storage.local.set({
        [STORAGE_KEYS.LAST_ACTIVE_WORKSPACE_IDS]: lastActiveIds
      });
      return { success: true };
    } catch (error) {
      return {
        success: false,
        error: {
          code: ERROR_CODES.STORAGE_ERROR,
          message: "Failed to update last active workspaces",
          details: error
        }
      };
    }
  }
  /**
   * 清除所有数据（增强版：确保清除所有存储键）
   */
  static async clearAll() {
    try {
      console.log("🗑️ 开始清除所有数据...");
      const allKeys = [
        // 基础存储键
        ...Object.values(STORAGE_KEYS),
        // Workona 存储键
        ...Object.values(WORKONA_STORAGE_KEYS)
      ];
      const allStoredData = await chrome.storage.local.get(null);
      const storedKeys = Object.keys(allStoredData);
      const keysToRemove = storedKeys.filter((key) => {
        return key.startsWith("workspacePinnedTabIds_") || key.includes("-hidden-tabs") || allKeys.includes(key);
      });
      console.log(`🔍 发现 ${keysToRemove.length} 个存储键需要清除`);
      await chrome.storage.local.clear();
      console.log("✅ 所有数据已清除");
      return { success: true };
    } catch (error) {
      console.error("❌ 清除数据失败:", error);
      return {
        success: false,
        error: {
          code: ERROR_CODES.STORAGE_ERROR,
          message: "Failed to clear storage",
          details: error
        }
      };
    }
  }
  /**
   * 监听存储变化
   */
  static onChanged(callback) {
    chrome.storage.onChanged.addListener((changes, areaName) => {
      if (areaName === "local") {
        callback(changes);
      }
    });
  }
  /**
   * 导出数据（增强版：包含完整的工作区数据结构）
   */
  static async exportData() {
    try {
      const dataResult = await this.getAllData();
      if (!dataResult.success) {
        return {
          success: false,
          error: dataResult.error
        };
      }
      const data = dataResult.data;
      const exportData = {
        version: "2.0.0",
        // 升级版本号以支持新的数据结构
        exportedAt: Date.now(),
        // 基础数据
        workspaces: data.workspaces,
        settings: data.settings,
        activeWorkspaceId: data.activeWorkspaceId,
        lastActiveWorkspaceIds: data.lastActiveWorkspaceIds,
        // 工作区增强数据
        workspaceSessions: data.workspaceSessions || {},
        tabIdMappings: data.tabIdMappings || [],
        localOpenWorkspaces: data.localOpenWorkspaces || {},
        tabGroups: data.tabGroups || {},
        globalWorkspaceWindowId: data.globalWorkspaceWindowId,
        dataVersion: data.dataVersion || "1.0.0",
        // 导出元数据
        exportMetadata: {
          totalWorkspaces: data.workspaces.length,
          totalWebsites: data.workspaces.reduce((sum, ws) => sum + ws.websites.length, 0),
          totalSessions: Object.keys(data.workspaceSessions || {}).length,
          totalTabMappings: (data.tabIdMappings || []).length,
          hasActiveWorkspace: !!data.activeWorkspaceId,
          exportSource: "WorkSpace Pro Chrome Extension"
        }
      };
      console.log(`📦 导出完整数据: ${exportData.exportMetadata.totalWorkspaces} 个工作区, ${exportData.exportMetadata.totalWebsites} 个网站, ${exportData.exportMetadata.totalSessions} 个会话`);
      return { success: true, data: JSON.stringify(exportData, null, 2) };
    } catch (error) {
      return {
        success: false,
        error: {
          code: ERROR_CODES.STORAGE_ERROR,
          message: "Failed to export data",
          details: error
        }
      };
    }
  }
  /**
   * 导入数据（增强版：支持完整数据结构和版本兼容）
   */
  static async importData(jsonData) {
    try {
      let importData = JSON.parse(jsonData);
      const validation = ImportDataProcessor.validateImportedData(importData);
      if (!validation.isValid) {
        throw new Error(`数据验证失败: ${validation.errors.join(", ")}`);
      }
      importData = ImportDataProcessor.cleanImportData(importData);
      const version = importData.version || "1.0.0";
      console.log(`📥 导入数据版本: ${version}`);
      if (!importData.workspaces || !Array.isArray(importData.workspaces)) {
        throw new Error("Invalid data format: workspaces array is required");
      }
      const existingWorkspacesResult = await this.getWorkspaces();
      const existingWorkspaces = existingWorkspacesResult.success ? existingWorkspacesResult.data : [];
      console.log(`📋 开始增量导入 ${importData.workspaces.length} 个工作区（现有 ${existingWorkspaces.length} 个）`);
      const importResult = await this.performIncrementalImport(existingWorkspaces, importData.workspaces);
      await this.saveWorkspaces(importResult.mergedWorkspaces);
      console.log(`✅ 增量导入完成: 新增 ${importResult.addedWorkspaces} 个工作区, 新增 ${importResult.addedWebsites} 个网站, 跳过 ${importResult.skippedWorkspaces} 个重复工作区`);
      if (importData.settings) {
        console.log("⚙️ 导入设置配置");
        await this.saveSettings(importData.settings);
      }
      if (version === "2.0.0" || importData.workspaceSessions) {
        console.log("🔄 导入工作区会话数据");
        if (importData.workspaceSessions) {
          await this.saveWorkspaceSessions(importData.workspaceSessions);
          console.log(`📊 导入 ${Object.keys(importData.workspaceSessions).length} 个工作区会话`);
        }
        if (importData.tabIdMappings) {
          await this.saveTabIdMappings(importData.tabIdMappings);
          console.log(`🔗 导入 ${importData.tabIdMappings.length} 个标签页ID映射`);
        }
        if (importData.localOpenWorkspaces) {
          await this.saveLocalOpenWorkspaces(importData.localOpenWorkspaces);
          console.log(`💻 导入本地打开工作区数据`);
        }
        if (importData.tabGroups) {
          await this.saveTabGroups(importData.tabGroups);
          console.log(`📁 导入标签组数据`);
        }
        if (importData.globalWorkspaceWindowId) {
          await chrome.storage.local.set({
            [WORKONA_STORAGE_KEYS.GLOBAL_WORKSPACE_WINDOW_ID]: importData.globalWorkspaceWindowId
          });
          console.log(`🪟 导入全局工作区窗口ID`);
        }
        if (importData.dataVersion) {
          await this.saveDataVersion(importData.dataVersion);
        }
      } else {
        console.log("🔄 初始化增强数据结构（兼容旧版本）");
        await this.saveWorkspaceSessions({});
        await this.saveTabIdMappings([]);
        await this.saveLocalOpenWorkspaces({});
        await this.saveTabGroups({});
        await this.saveDataVersion("2.0.0");
      }
      console.log("🔄 开始系统映射自动补全...");
      const processingResult = await ImportDataProcessor.processImportedData(importData);
      if (!processingResult.success) {
        console.warn("⚠️ 系统映射补全失败，但导入数据已保存:", processingResult.error);
      }
      console.log(`✅ 增量导入完成: 新增 ${importResult.addedWorkspaces} 个工作区, 新增 ${importResult.addedWebsites} 个网站`);
      if (importResult.skippedWorkspaces > 0) {
        console.log(`ℹ️ 跳过 ${importResult.skippedWorkspaces} 个重复工作区`);
      }
      return { success: true };
    } catch (error) {
      console.error("❌ 导入数据失败:", error);
      return {
        success: false,
        error: {
          code: ERROR_CODES.STORAGE_ERROR,
          message: "Failed to import data",
          details: error
        }
      };
    }
  }
  // ===== Workona 风格存储方法 =====
  /**
   * 保存标签页ID映射表
   */
  static async saveTabIdMappings(mappings) {
    try {
      await chrome.storage.local.set({
        [WORKONA_STORAGE_KEYS.TAB_ID_MAPPINGS]: mappings
      });
      return { success: true };
    } catch (error) {
      return {
        success: false,
        error: {
          code: ERROR_CODES.STORAGE_ERROR,
          message: "Failed to save tab ID mappings",
          details: error
        }
      };
    }
  }
  /**
   * 获取标签页ID映射表
   */
  static async getTabIdMappings() {
    try {
      const result = await chrome.storage.local.get([WORKONA_STORAGE_KEYS.TAB_ID_MAPPINGS]);
      const mappings = result[WORKONA_STORAGE_KEYS.TAB_ID_MAPPINGS] || [];
      return { success: true, data: mappings };
    } catch (error) {
      return {
        success: false,
        error: {
          code: ERROR_CODES.STORAGE_ERROR,
          message: "Failed to get tab ID mappings",
          details: error
        }
      };
    }
  }
  /**
   * 保存本地打开工作区
   */
  static async saveLocalOpenWorkspaces(workspaces) {
    try {
      await chrome.storage.local.set({
        [WORKONA_STORAGE_KEYS.LOCAL_OPEN_WORKSPACES]: workspaces
      });
      return { success: true };
    } catch (error) {
      return {
        success: false,
        error: {
          code: ERROR_CODES.STORAGE_ERROR,
          message: "Failed to save local open workspaces",
          details: error
        }
      };
    }
  }
  /**
   * 获取本地打开工作区
   */
  static async getLocalOpenWorkspaces() {
    try {
      const result = await chrome.storage.local.get([WORKONA_STORAGE_KEYS.LOCAL_OPEN_WORKSPACES]);
      const workspaces = result[WORKONA_STORAGE_KEYS.LOCAL_OPEN_WORKSPACES] || {};
      return { success: true, data: workspaces };
    } catch (error) {
      return {
        success: false,
        error: {
          code: ERROR_CODES.STORAGE_ERROR,
          message: "Failed to get local open workspaces",
          details: error
        }
      };
    }
  }
  /**
   * 保存标签组信息
   */
  static async saveTabGroups(tabGroups) {
    try {
      await chrome.storage.local.set({
        [WORKONA_STORAGE_KEYS.TAB_GROUPS]: tabGroups
      });
      return { success: true };
    } catch (error) {
      return {
        success: false,
        error: {
          code: ERROR_CODES.STORAGE_ERROR,
          message: "Failed to save tab groups",
          details: error
        }
      };
    }
  }
  /**
   * 获取标签组信息
   */
  static async getTabGroups() {
    try {
      const result = await chrome.storage.local.get([WORKONA_STORAGE_KEYS.TAB_GROUPS]);
      const tabGroups = result[WORKONA_STORAGE_KEYS.TAB_GROUPS] || {};
      return { success: true, data: tabGroups };
    } catch (error) {
      return {
        success: false,
        error: {
          code: ERROR_CODES.STORAGE_ERROR,
          message: "Failed to get tab groups",
          details: error
        }
      };
    }
  }
  /**
   * 保存工作区会话
   */
  static async saveWorkspaceSessions(sessions) {
    try {
      await chrome.storage.local.set({
        [WORKONA_STORAGE_KEYS.WORKSPACE_SESSIONS]: sessions
      });
      return { success: true };
    } catch (error) {
      return {
        success: false,
        error: {
          code: ERROR_CODES.STORAGE_ERROR,
          message: "Failed to save workspace sessions",
          details: error
        }
      };
    }
  }
  /**
   * 获取工作区会话
   */
  static async getWorkspaceSessions() {
    try {
      const result = await chrome.storage.local.get([WORKONA_STORAGE_KEYS.WORKSPACE_SESSIONS]);
      const sessions = result[WORKONA_STORAGE_KEYS.WORKSPACE_SESSIONS] || {};
      return { success: true, data: sessions };
    } catch (error) {
      return {
        success: false,
        error: {
          code: ERROR_CODES.STORAGE_ERROR,
          message: "Failed to get workspace sessions",
          details: error
        }
      };
    }
  }
  /**
   * 保存全局工作区窗口ID
   */
  static async saveGlobalWorkspaceWindowId(windowId) {
    try {
      await chrome.storage.local.set({
        [WORKONA_STORAGE_KEYS.GLOBAL_WORKSPACE_WINDOW_ID]: windowId
      });
      return { success: true };
    } catch (error) {
      return {
        success: false,
        error: {
          code: ERROR_CODES.STORAGE_ERROR,
          message: "Failed to save global workspace window ID",
          details: error
        }
      };
    }
  }
  /**
   * 获取全局工作区窗口ID
   */
  static async getGlobalWorkspaceWindowId() {
    try {
      const result = await chrome.storage.local.get([WORKONA_STORAGE_KEYS.GLOBAL_WORKSPACE_WINDOW_ID]);
      const windowId = result[WORKONA_STORAGE_KEYS.GLOBAL_WORKSPACE_WINDOW_ID] || null;
      return { success: true, data: windowId };
    } catch (error) {
      return {
        success: false,
        error: {
          code: ERROR_CODES.STORAGE_ERROR,
          message: "Failed to get global workspace window ID",
          details: error
        }
      };
    }
  }
  /**
   * 保存数据版本
   */
  static async saveDataVersion(version) {
    try {
      await chrome.storage.local.set({
        [WORKONA_STORAGE_KEYS.DATA_VERSION]: version
      });
      return { success: true };
    } catch (error) {
      return {
        success: false,
        error: {
          code: ERROR_CODES.STORAGE_ERROR,
          message: "Failed to save data version",
          details: error
        }
      };
    }
  }
  /**
   * 获取数据版本
   */
  static async getDataVersion() {
    try {
      const result = await chrome.storage.local.get([WORKONA_STORAGE_KEYS.DATA_VERSION]);
      const version = result[WORKONA_STORAGE_KEYS.DATA_VERSION] || "1.0.0";
      return { success: true, data: version };
    } catch (error) {
      return {
        success: false,
        error: {
          code: ERROR_CODES.STORAGE_ERROR,
          message: "Failed to get data version",
          details: error
        }
      };
    }
  }
  /**
   * 清除全局工作区窗口ID
   */
  static async clearGlobalWorkspaceWindowId() {
    try {
      await chrome.storage.local.remove([WORKONA_STORAGE_KEYS.GLOBAL_WORKSPACE_WINDOW_ID]);
      return { success: true };
    } catch (error) {
      return {
        success: false,
        error: {
          code: ERROR_CODES.STORAGE_ERROR,
          message: "Failed to clear global workspace window ID",
          details: error
        }
      };
    }
  }
  /**
   * 渐进式数据迁移检查
   * 检测现有数据格式，逐步迁移到 Workona 格式
   */
  static async migrateToWorkonaFormat() {
    try {
      console.log("🔄 开始检查 Workona 数据迁移需求...");
      const versionResult = await this.getDataVersion();
      if (!versionResult.success) {
        return { success: false, error: versionResult.error };
      }
      const currentVersion = versionResult.data;
      const targetVersion = "1.0.0";
      if (currentVersion === targetVersion) {
        console.log("✅ 数据版本已是最新，无需迁移");
        return { success: true, data: false };
      }
      console.log(`📦 检测到数据版本 ${currentVersion}，开始迁移到 ${targetVersion}...`);
      const allDataResult = await this.getAllData();
      if (!allDataResult.success) {
        return { success: false, error: allDataResult.error };
      }
      const data = allDataResult.data;
      if (!data.tabIdMappings) {
        await this.saveTabIdMappings([]);
      }
      if (!data.localOpenWorkspaces) {
        await this.saveLocalOpenWorkspaces({});
      }
      if (!data.tabGroups) {
        await this.saveTabGroups({});
      }
      if (!data.workspaceSessions) {
        await this.saveWorkspaceSessions({});
      }
      await this.saveDataVersion(targetVersion);
      console.log("✅ Workona 数据迁移完成");
      return { success: true, data: true };
    } catch (error) {
      console.error("❌ Workona 数据迁移失败:", error);
      return {
        success: false,
        error: {
          code: ERROR_CODES.STORAGE_ERROR,
          message: "Failed to migrate to Workona format",
          details: error
        }
      };
    }
  }
  /**
   * 执行增量导入逻辑（极简重构版 - 绝对安全）
   *
   * 核心原则：
   * 1. 永远不删除现有数据
   * 2. 只进行添加操作
   * 3. 使用最简单的逻辑
   * 4. 每个操作都是原子的和可验证的
   */
  static async performIncrementalImport(existingWorkspaces, importWorkspaces) {
    console.log(`🚀 开始极简增量导入`);
    console.log(`📊 现有工作区: ${existingWorkspaces.length} 个, 导入工作区: ${importWorkspaces.length} 个`);
    const result = JSON.parse(JSON.stringify(existingWorkspaces));
    console.log(`✅ 深拷贝现有工作区完成: ${result.length} 个`);
    let addedWorkspaces = 0;
    let addedWebsites = 0;
    let skippedWorkspaces = 0;
    for (const importWorkspace of importWorkspaces) {
      console.log(`
🔄 处理导入工作区: "${importWorkspace.name}"`);
      const existingWorkspace = result.find((ws) => ws.name.toLowerCase() === importWorkspace.name.toLowerCase());
      if (existingWorkspace) {
        console.log(`📝 发现同名工作区，开始合并网站...`);
        const addedCount = this.safelyMergeWebsites(existingWorkspace, importWorkspace);
        addedWebsites += addedCount;
        skippedWorkspaces++;
        console.log(`✅ 合并完成: 向 "${importWorkspace.name}" 添加了 ${addedCount} 个新网站`);
      } else {
        console.log(`🆕 创建新工作区...`);
        const newWorkspace = this.safelyCreateNewWorkspace(importWorkspace, result);
        result.push(newWorkspace);
        addedWorkspaces++;
        const websiteCount = newWorkspace.websites ? newWorkspace.websites.length : 0;
        addedWebsites += websiteCount;
        console.log(`✅ 创建完成: "${newWorkspace.name}" 包含 ${websiteCount} 个网站`);
      }
    }
    console.log(`
🎉 增量导入完成!`);
    console.log(`📊 最终结果: 工作区 ${result.length} 个, 新增工作区 ${addedWorkspaces} 个, 合并工作区 ${skippedWorkspaces} 个, 新增网站 ${addedWebsites} 个`);
    return {
      mergedWorkspaces: result,
      addedWorkspaces,
      addedWebsites,
      skippedWorkspaces
    };
  }
  /**
   * 安全地合并网站到现有工作区（极简版 - 绝对不会删除现有网站）
   */
  static safelyMergeWebsites(existingWorkspace, importWorkspace) {
    console.log(`📊 合并前: 现有工作区 "${existingWorkspace.name}" 有 ${existingWorkspace.websites?.length || 0} 个网站`);
    if (!existingWorkspace.websites) {
      existingWorkspace.websites = [];
    }
    const originalCount = existingWorkspace.websites.length;
    console.log(`📋 现有网站列表:`, existingWorkspace.websites.map((w) => `${w.title} (${w.url})`));
    const existingUrls = /* @__PURE__ */ new Set();
    existingWorkspace.websites.forEach((website) => {
      existingUrls.add(website.url.toLowerCase());
    });
    let addedCount = 0;
    if (importWorkspace.websites && importWorkspace.websites.length > 0) {
      console.log(`📋 导入网站列表:`, importWorkspace.websites.map((w) => `${w.title} (${w.url})`));
      for (const importWebsite of importWorkspace.websites) {
        const urlLower = importWebsite.url.toLowerCase();
        if (!existingUrls.has(urlLower)) {
          const newWebsite = {
            ...JSON.parse(JSON.stringify(importWebsite)),
            id: `site_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`
          };
          existingWorkspace.websites.push(newWebsite);
          existingUrls.add(urlLower);
          addedCount++;
          console.log(`✅ 添加新网站: ${importWebsite.title} (${importWebsite.url})`);
        } else {
          console.log(`⏭️ 跳过重复网站: ${importWebsite.title} (${importWebsite.url})`);
        }
      }
    }
    existingWorkspace.updatedAt = Date.now();
    const finalCount = existingWorkspace.websites.length;
    const expectedCount = originalCount + addedCount;
    console.log(`📊 合并后: 工作区 "${existingWorkspace.name}" 有 ${finalCount} 个网站`);
    console.log(`📋 最终网站列表:`, existingWorkspace.websites.map((w) => `${w.title} (${w.url})`));
    if (finalCount !== expectedCount) {
      console.error(`❌ 严重错误! 网站数量不匹配: 预期 ${expectedCount}, 实际 ${finalCount}`);
      throw new Error(`网站合并验证失败: ${existingWorkspace.name}`);
    }
    console.log(`✅ 验证通过: 保留 ${originalCount} 个现有网站, 新增 ${addedCount} 个网站`);
    return addedCount;
  }
  /**
   * 安全地创建新工作区（极简版 - 检查全局URL重复）
   */
  static safelyCreateNewWorkspace(importWorkspace, existingWorkspaces) {
    console.log(`🆕 创建新工作区: "${importWorkspace.name}"`);
    const globalUrls = /* @__PURE__ */ new Set();
    existingWorkspaces.forEach((workspace) => {
      if (workspace.websites) {
        workspace.websites.forEach((website) => {
          globalUrls.add(website.url.toLowerCase());
        });
      }
    });
    const processedWebsites = [];
    if (importWorkspace.websites && importWorkspace.websites.length > 0) {
      console.log(`📋 导入网站列表:`, importWorkspace.websites.map((w) => `${w.title} (${w.url})`));
      for (const importWebsite of importWorkspace.websites) {
        const urlLower = importWebsite.url.toLowerCase();
        if (!globalUrls.has(urlLower)) {
          const newWebsite = {
            ...JSON.parse(JSON.stringify(importWebsite)),
            id: `site_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`
          };
          processedWebsites.push(newWebsite);
          globalUrls.add(urlLower);
          console.log(`✅ 添加网站: ${importWebsite.title} (${importWebsite.url})`);
        } else {
          console.log(`⏭️ 跳过全局重复网站: ${importWebsite.title} (${importWebsite.url})`);
        }
      }
    }
    const newWorkspace = {
      ...JSON.parse(JSON.stringify(importWorkspace)),
      id: `ws_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
      websites: processedWebsites,
      isActive: false,
      createdAt: Date.now(),
      updatedAt: Date.now()
    };
    console.log(`✅ 新工作区创建完成: "${newWorkspace.name}" 包含 ${processedWebsites.length} 个网站`);
    console.log(`📋 网站列表:`, processedWebsites.map((w) => `${w.title} (${w.url})`));
    return newWorkspace;
  }
}

const scriptRel = 'modulepreload';const assetsURL = function(dep) { return "/"+dep };const seen = {};const __vitePreload = function preload(baseModule, deps, importerUrl) {
  let promise = Promise.resolve();
  if (true && deps && deps.length > 0) {
    document.getElementsByTagName("link");
    const cspNonceMeta = document.querySelector(
      "meta[property=csp-nonce]"
    );
    const cspNonce = cspNonceMeta?.nonce || cspNonceMeta?.getAttribute("nonce");
    promise = Promise.allSettled(
      deps.map((dep) => {
        dep = assetsURL(dep);
        if (dep in seen) return;
        seen[dep] = true;
        const isCss = dep.endsWith(".css");
        const cssSelector = isCss ? '[rel="stylesheet"]' : "";
        if (document.querySelector(`link[href="${dep}"]${cssSelector}`)) {
          return;
        }
        const link = document.createElement("link");
        link.rel = isCss ? "stylesheet" : scriptRel;
        if (!isCss) {
          link.as = "script";
        }
        link.crossOrigin = "";
        link.href = dep;
        if (cspNonce) {
          link.setAttribute("nonce", cspNonce);
        }
        document.head.appendChild(link);
        if (isCss) {
          return new Promise((res, rej) => {
            link.addEventListener("load", res);
            link.addEventListener(
              "error",
              () => rej(new Error(`Unable to preload CSS for ${dep}`))
            );
          });
        }
      })
    );
  }
  function handlePreloadError(err) {
    const e = new Event("vite:preloadError", {
      cancelable: true
    });
    e.payload = err;
    window.dispatchEvent(e);
    if (!e.defaultPrevented) {
      throw err;
    }
  }
  return promise.then((res) => {
    for (const item of res || []) {
      if (item.status !== "rejected") continue;
      handlePreloadError(item.reason);
    }
    return baseModule().catch(handlePreloadError);
  });
};

class WorkspaceCore {
  /**
   * 创建新工作区
   */
  static async createWorkspace(options) {
    try {
      console.log("🆕 创建新工作区:", options);
      if (!options.name?.trim()) {
        return {
          success: false,
          error: {
            code: ERROR_CODES.INVALID_URL,
            message: "Workspace name is required"
          }
        };
      }
      const { IdGenerator } = await __vitePreload(async () => { const { IdGenerator } = await Promise.resolve().then(() => WorkspaceUtilities$1);return { IdGenerator }},true?void 0:void 0);
      const workspaceId = IdGenerator.generateWorkspaceId(options.type);
      const workspace = {
        id: workspaceId,
        name: options.name.trim(),
        description: options.description?.trim() || "",
        color: options.color || WORKSPACE_COLORS[Math.floor(Math.random() * WORKSPACE_COLORS.length)],
        icon: options.icon || WORKSPACE_ICONS[Math.floor(Math.random() * WORKSPACE_ICONS.length)],
        websites: [],
        isActive: false,
        order: 0,
        // 将在后面设置正确的值
        createdAt: Date.now(),
        updatedAt: Date.now(),
        type: options.type || "saved",
        state: options.state || "active",
        position: options.position || 0,
        workonaTabIds: [],
        hiddenTabIds: [],
        pinnedTabIds: [],
        userTabsHidden: false
      };
      const workspacesResult = await StorageManager.getWorkspaces();
      if (!workspacesResult.success) {
        return { success: false, error: workspacesResult.error };
      }
      const workspaces = workspacesResult.data;
      const existingWorkspace = workspaces.find((w) => w.name === workspace.name);
      if (existingWorkspace) {
        return {
          success: false,
          error: {
            code: ERROR_CODES.DUPLICATE_WORKSPACE,
            message: "Workspace with this name already exists"
          }
        };
      }
      if (workspace.position === 0) {
        workspace.position = workspaces.length;
      }
      workspace.order = workspaces.length;
      workspaces.push(workspace);
      const saveResult = await StorageManager.saveWorkspaces(workspaces);
      if (!saveResult.success) {
        return { success: false, error: saveResult.error };
      }
      console.log(`✅ 工作区创建成功: ${workspace.name} (${workspace.id})`);
      return { success: true, data: workspace };
    } catch (error) {
      return {
        success: false,
        error: {
          code: ERROR_CODES.STORAGE_ERROR,
          message: "Failed to create workspace",
          details: error
        }
      };
    }
  }
  /**
   * 更新工作区
   */
  static async updateWorkspace(id, options) {
    try {
      console.log(`🔄 更新工作区: ${id}`, options);
      const workspacesResult = await StorageManager.getWorkspaces();
      if (!workspacesResult.success) {
        return { success: false, error: workspacesResult.error };
      }
      const workspaces = workspacesResult.data;
      const workspaceIndex = workspaces.findIndex((w) => w.id === id);
      if (workspaceIndex === -1) {
        return {
          success: false,
          error: {
            code: ERROR_CODES.WORKSPACE_NOT_FOUND,
            message: "Workspace not found"
          }
        };
      }
      const workspace = workspaces[workspaceIndex];
      if (options.name && options.name !== workspace.name) {
        const existingWorkspace = workspaces.find((w) => w.name === options.name && w.id !== id);
        if (existingWorkspace) {
          return {
            success: false,
            error: {
              code: ERROR_CODES.DUPLICATE_WORKSPACE,
              message: "Workspace with this name already exists"
            }
          };
        }
      }
      const updatedWorkspace = {
        ...workspace,
        name: options.name?.trim() || workspace.name,
        description: options.description?.trim() ?? workspace.description,
        color: options.color || workspace.color,
        icon: options.icon || workspace.icon,
        updatedAt: Date.now()
      };
      workspaces[workspaceIndex] = updatedWorkspace;
      const saveResult = await StorageManager.saveWorkspaces(workspaces);
      if (!saveResult.success) {
        return { success: false, error: saveResult.error };
      }
      console.log(`✅ 工作区更新成功: ${updatedWorkspace.name}`);
      return { success: true, data: updatedWorkspace };
    } catch (error) {
      return {
        success: false,
        error: {
          code: ERROR_CODES.STORAGE_ERROR,
          message: "Failed to update workspace",
          details: error
        }
      };
    }
  }
  /**
   * 删除工作区
   */
  static async deleteWorkspace(id) {
    try {
      console.log(`🗑️ 删除工作区: ${id}`);
      const workspacesResult = await StorageManager.getWorkspaces();
      if (!workspacesResult.success) {
        return { success: false, error: workspacesResult.error };
      }
      const workspaces = workspacesResult.data;
      const workspaceIndex = workspaces.findIndex((w) => w.id === id);
      if (workspaceIndex === -1) {
        return {
          success: false,
          error: {
            code: ERROR_CODES.WORKSPACE_NOT_FOUND,
            message: "Workspace not found"
          }
        };
      }
      const workspace = workspaces[workspaceIndex];
      try {
        const { WorkonaTabManager } = await __vitePreload(async () => { const { WorkonaTabManager } = await Promise.resolve().then(() => workonaTabManager);return { WorkonaTabManager }},true?void 0:void 0);
        for (const workonaId of workspace.workonaTabIds || []) {
          await WorkonaTabManager.removeTabMapping(workonaId);
        }
        console.log(`🧹 清理了 ${workspace.workonaTabIds?.length || 0} 个Workona标签页映射`);
      } catch (error) {
        console.warn("清理Workona标签页映射失败:", error);
      }
      workspaces.splice(workspaceIndex, 1);
      if (workspace.isActive) {
        await StorageManager.setActiveWorkspaceId(null);
      }
      const saveResult = await StorageManager.saveWorkspaces(workspaces);
      if (!saveResult.success) {
        return { success: false, error: saveResult.error };
      }
      console.log(`✅ 工作区删除成功: ${workspace.name}`);
      return { success: true };
    } catch (error) {
      return {
        success: false,
        error: {
          code: ERROR_CODES.STORAGE_ERROR,
          message: "Failed to delete workspace",
          details: error
        }
      };
    }
  }
  /**
   * 重新排序工作区
   */
  static async reorderWorkspaces(workspaceIds) {
    try {
      console.log("🔄 重新排序工作区:", workspaceIds);
      const workspacesResult = await StorageManager.getWorkspaces();
      if (!workspacesResult.success) {
        return { success: false, error: workspacesResult.error };
      }
      const workspaces = workspacesResult.data;
      const existingIds = workspaces.map((w) => w.id);
      const missingIds = workspaceIds.filter((id) => !existingIds.includes(id));
      if (missingIds.length > 0) {
        return {
          success: false,
          error: {
            code: ERROR_CODES.WORKSPACE_NOT_FOUND,
            message: `Workspaces not found: ${missingIds.join(", ")}`
          }
        };
      }
      const reorderedWorkspaces = workspaceIds.map((id, index) => {
        const workspace = workspaces.find((w) => w.id === id);
        return {
          ...workspace,
          position: index,
          updatedAt: Date.now()
        };
      });
      const saveResult = await StorageManager.saveWorkspaces(reorderedWorkspaces);
      if (!saveResult.success) {
        return { success: false, error: saveResult.error };
      }
      console.log("✅ 工作区重新排序成功");
      return { success: true };
    } catch (error) {
      return {
        success: false,
        error: {
          code: ERROR_CODES.STORAGE_ERROR,
          message: "Failed to reorder workspaces",
          details: error
        }
      };
    }
  }
  /**
   * 更新工作区类型
   */
  static async updateWorkspaceType(workspaceId, newType) {
    try {
      console.log(`🔄 更新工作区类型: ${workspaceId} -> ${newType}`);
      const workspacesResult = await StorageManager.getWorkspaces();
      if (!workspacesResult.success) {
        return { success: false, error: workspacesResult.error };
      }
      const workspaces = workspacesResult.data;
      const workspaceIndex = workspaces.findIndex((w) => w.id === workspaceId);
      if (workspaceIndex === -1) {
        return {
          success: false,
          error: {
            code: ERROR_CODES.WORKSPACE_NOT_FOUND,
            message: "Workspace not found"
          }
        };
      }
      const updatedWorkspace = {
        ...workspaces[workspaceIndex],
        type: newType,
        updatedAt: Date.now()
      };
      workspaces[workspaceIndex] = updatedWorkspace;
      const saveResult = await StorageManager.saveWorkspaces(workspaces);
      if (!saveResult.success) {
        return { success: false, error: saveResult.error };
      }
      console.log(`✅ 工作区类型更新成功: ${updatedWorkspace.name} -> ${newType}`);
      return { success: true, data: updatedWorkspace };
    } catch (error) {
      return {
        success: false,
        error: {
          code: ERROR_CODES.STORAGE_ERROR,
          message: "Failed to update workspace type",
          details: error
        }
      };
    }
  }
}

class WorkspaceWebsiteManager {
  /**
   * 辅助方法：更新工作区并保存
   */
  static async saveUpdatedWorkspace(updatedWorkspace) {
    try {
      const workspacesResult = await StorageManager.getWorkspaces();
      if (!workspacesResult.success) {
        return { success: false, error: workspacesResult.error };
      }
      const workspaces = workspacesResult.data;
      const workspaceIndex = workspaces.findIndex((w) => w.id === updatedWorkspace.id);
      if (workspaceIndex !== -1) {
        workspaces[workspaceIndex] = updatedWorkspace;
      }
      return await StorageManager.saveWorkspaces(workspaces);
    } catch (error) {
      return {
        success: false,
        error: {
          code: ERROR_CODES.STORAGE_ERROR,
          message: "Failed to save updated workspace",
          details: error
        }
      };
    }
  }
  /**
   * 验证并格式化URL
   */
  static validateAndFormatUrl(url) {
    try {
      let formattedUrl = url.trim();
      if (!formattedUrl.startsWith("http://") && !formattedUrl.startsWith("https://")) {
        formattedUrl = `https://${formattedUrl}`;
      }
      const urlObj = new URL(formattedUrl);
      return {
        isValid: true,
        formattedUrl: urlObj.href
      };
    } catch {
      return {
        isValid: false,
        formattedUrl: url
      };
    }
  }
  /**
   * 获取网站标题
   */
  static async getWebsiteTitle(url) {
    try {
      const urlObj = new URL(url);
      return urlObj.hostname.replace("www.", "");
    } catch {
      return "Unknown Website";
    }
  }
  /**
   * 获取网站图标
   */
  static async getFavicon(url) {
    try {
      const urlObj = new URL(url);
      return `${urlObj.protocol}//${urlObj.hostname}/favicon.ico`;
    } catch {
      return DEFAULT_FAVICON;
    }
  }
  /**
   * 添加网站到工作区
   */
  static async addWebsite(workspaceId, url, options = {}) {
    try {
      console.log(`🌐 添加网站到工作区 ${workspaceId}:`, url);
      const { isValid, formattedUrl } = this.validateAndFormatUrl(url);
      if (!isValid) {
        return {
          success: false,
          error: {
            code: ERROR_CODES.INVALID_URL,
            message: "Invalid URL format"
          }
        };
      }
      const workspaceResult = await StorageManager.getWorkspace(workspaceId);
      if (!workspaceResult.success) {
        return { success: false, error: workspaceResult.error };
      }
      const workspace = workspaceResult.data;
      const existingWebsite = workspace.websites.find((w) => w.url === formattedUrl);
      if (existingWebsite) {
        return {
          success: false,
          error: {
            code: ERROR_CODES.DUPLICATE_WEBSITE,
            message: "Website already exists in workspace"
          }
        };
      }
      const { IdGenerator } = await __vitePreload(async () => { const { IdGenerator } = await Promise.resolve().then(() => WorkspaceUtilities$1);return { IdGenerator }},true?void 0:void 0);
      const websiteId = IdGenerator.generateWebsiteId();
      const title = options.title || await this.getWebsiteTitle(formattedUrl);
      const favicon = options.favicon || await this.getFavicon(formattedUrl);
      const website = {
        id: websiteId,
        url: formattedUrl,
        title,
        favicon,
        isPinned: options.isPinned || false,
        addedAt: Date.now(),
        order: workspace.websites.length
      };
      workspace.websites.push(website);
      workspace.updatedAt = Date.now();
      const saveResult = await this.saveUpdatedWorkspace(workspace);
      if (!saveResult.success) {
        return { success: false, error: saveResult.error };
      }
      console.log(`✅ 网站添加成功: ${title}`);
      return { success: true, data: website };
    } catch (error) {
      return {
        success: false,
        error: {
          code: ERROR_CODES.STORAGE_ERROR,
          message: "Failed to add website",
          details: error
        }
      };
    }
  }
  /**
   * 从工作区移除网站
   */
  static async removeWebsite(workspaceId, websiteId) {
    try {
      console.log(`🗑️ 从工作区 ${workspaceId} 移除网站: ${websiteId}`);
      const workspaceResult = await StorageManager.getWorkspace(workspaceId);
      if (!workspaceResult.success) {
        return { success: false, error: workspaceResult.error };
      }
      const workspace = workspaceResult.data;
      const websiteIndex = workspace.websites.findIndex((w) => w.id === websiteId);
      if (websiteIndex === -1) {
        return {
          success: false,
          error: {
            code: ERROR_CODES.WEBSITE_NOT_FOUND,
            message: "Website not found in workspace"
          }
        };
      }
      const website = workspace.websites[websiteIndex];
      try {
        const { WorkonaTabManager } = await __vitePreload(async () => { const { WorkonaTabManager } = await Promise.resolve().then(() => workonaTabManager);return { WorkonaTabManager }},true?void 0:void 0);
        for (const workonaId of workspace.workonaTabIds || []) {
          const metadataResult = await WorkonaTabManager.getTabMetadata(workonaId);
          if (metadataResult.success && metadataResult.data) {
            await WorkonaTabManager.updateTabMetadata(workonaId, {
              isWorkspaceCore: false
            });
            console.log(`🔄 标签页降级为会话标签页: ${workonaId}`);
          }
        }
      } catch (error) {
        console.warn("清理Workona标签页映射失败:", error);
      }
      workspace.websites.splice(websiteIndex, 1);
      workspace.updatedAt = Date.now();
      workspace.websites.forEach((w, index) => {
        w.order = index;
      });
      const saveResult = await this.saveUpdatedWorkspace(workspace);
      if (!saveResult.success) {
        return { success: false, error: saveResult.error };
      }
      console.log(`✅ 网站移除成功: ${website.title}`);
      return { success: true };
    } catch (error) {
      return {
        success: false,
        error: {
          code: ERROR_CODES.STORAGE_ERROR,
          message: "Failed to remove website",
          details: error
        }
      };
    }
  }
  /**
   * 更新网站信息
   */
  static async updateWebsite(workspaceId, websiteId, updates) {
    try {
      console.log(`🔄 更新工作区 ${workspaceId} 中的网站 ${websiteId}:`, updates);
      const workspaceResult = await StorageManager.getWorkspace(workspaceId);
      if (!workspaceResult.success) {
        return { success: false, error: workspaceResult.error };
      }
      const workspace = workspaceResult.data;
      const websiteIndex = workspace.websites.findIndex((w) => w.id === websiteId);
      if (websiteIndex === -1) {
        return {
          success: false,
          error: {
            code: ERROR_CODES.WEBSITE_NOT_FOUND,
            message: "Website not found in workspace"
          }
        };
      }
      const website = workspace.websites[websiteIndex];
      if (updates.url) {
        const { isValid, formattedUrl } = this.validateAndFormatUrl(updates.url);
        if (!isValid) {
          return {
            success: false,
            error: {
              code: ERROR_CODES.INVALID_URL,
              message: "Invalid URL format"
            }
          };
        }
        const existingWebsite = workspace.websites.find((w) => w.url === formattedUrl && w.id !== websiteId);
        if (existingWebsite) {
          return {
            success: false,
            error: {
              code: ERROR_CODES.DUPLICATE_WEBSITE,
              message: "Website with this URL already exists in workspace"
            }
          };
        }
        updates.url = formattedUrl;
      }
      const updatedWebsite = {
        ...website,
        url: updates.url || website.url,
        title: updates.title || website.title,
        isPinned: updates.isPinned ?? website.isPinned
      };
      workspace.websites[websiteIndex] = updatedWebsite;
      workspace.updatedAt = Date.now();
      const saveResult = await this.saveUpdatedWorkspace(workspace);
      if (!saveResult.success) {
        return { success: false, error: saveResult.error };
      }
      console.log(`✅ 网站更新成功: ${updatedWebsite.title}`);
      return { success: true, data: updatedWebsite };
    } catch (error) {
      return {
        success: false,
        error: {
          code: ERROR_CODES.STORAGE_ERROR,
          message: "Failed to update website",
          details: error
        }
      };
    }
  }
  /**
   * 重新排序工作区中的网站
   */
  static async reorderWebsites(workspaceId, websiteIds) {
    try {
      console.log(`🔄 重新排序工作区 ${workspaceId} 中的网站:`, websiteIds);
      const workspaceResult = await StorageManager.getWorkspace(workspaceId);
      if (!workspaceResult.success) {
        return { success: false, error: workspaceResult.error };
      }
      const workspace = workspaceResult.data;
      const existingIds = workspace.websites.map((w) => w.id);
      const missingIds = websiteIds.filter((id) => !existingIds.includes(id));
      if (missingIds.length > 0) {
        return {
          success: false,
          error: {
            code: ERROR_CODES.WEBSITE_NOT_FOUND,
            message: `Websites not found: ${missingIds.join(", ")}`
          }
        };
      }
      const reorderedWebsites = websiteIds.map((id, index) => {
        const website = workspace.websites.find((w) => w.id === id);
        return {
          ...website,
          order: index
        };
      });
      workspace.websites = reorderedWebsites;
      workspace.updatedAt = Date.now();
      const saveResult = await this.saveUpdatedWorkspace(workspace);
      if (!saveResult.success) {
        return { success: false, error: saveResult.error };
      }
      console.log(`✅ 网站重新排序成功`);
      return { success: true };
    } catch (error) {
      return {
        success: false,
        error: {
          code: ERROR_CODES.STORAGE_ERROR,
          message: "Failed to reorder websites",
          details: error
        }
      };
    }
  }
}

const WorkspaceWebsiteManager$1 = /*#__PURE__*/Object.freeze(/*#__PURE__*/Object.defineProperty({
  __proto__: null,
  WorkspaceWebsiteManager
}, Symbol.toStringTag, { value: 'Module' }));

class WorkspaceStateManager {
  /**
   * 辅助方法：更新工作区并保存
   */
  static async saveUpdatedWorkspace(updatedWorkspace) {
    try {
      const workspacesResult = await StorageManager.getWorkspaces();
      if (!workspacesResult.success) {
        return { success: false, error: workspacesResult.error };
      }
      const workspaces = workspacesResult.data;
      const workspaceIndex = workspaces.findIndex((w) => w.id === updatedWorkspace.id);
      if (workspaceIndex !== -1) {
        workspaces[workspaceIndex] = updatedWorkspace;
      }
      return await StorageManager.saveWorkspaces(workspaces);
    } catch (error) {
      return {
        success: false,
        error: {
          code: ERROR_CODES.STORAGE_ERROR,
          message: "Failed to save updated workspace",
          details: error
        }
      };
    }
  }
  /**
   * 更新工作区状态
   */
  static async updateWorkspaceState(workspaceId, newState) {
    try {
      console.log(`🔄 更新工作区状态: ${workspaceId} -> ${newState}`);
      const workspaceResult = await StorageManager.getWorkspace(workspaceId);
      if (!workspaceResult.success) {
        return { success: false, error: workspaceResult.error };
      }
      const workspace = workspaceResult.data;
      const updatedWorkspace = {
        ...workspace,
        state: newState,
        updatedAt: Date.now()
      };
      const saveResult = await this.saveUpdatedWorkspace(updatedWorkspace);
      if (!saveResult.success) {
        return { success: false, error: saveResult.error };
      }
      console.log(`✅ 工作区状态更新成功: ${workspace.name} -> ${newState}`);
      return { success: true, data: updatedWorkspace };
    } catch (error) {
      return {
        success: false,
        error: {
          code: ERROR_CODES.STORAGE_ERROR,
          message: "Failed to update workspace state",
          details: error
        }
      };
    }
  }
  /**
   * 更新工作区位置
   */
  static async updateWorkspacePosition(workspaceId, newPosition) {
    try {
      console.log(`🔄 更新工作区位置: ${workspaceId} -> ${newPosition}`);
      const workspaceResult = await StorageManager.getWorkspace(workspaceId);
      if (!workspaceResult.success) {
        return { success: false, error: workspaceResult.error };
      }
      const workspace = workspaceResult.data;
      const updatedWorkspace = {
        ...workspace,
        position: newPosition,
        updatedAt: Date.now()
      };
      const saveResult = await this.saveUpdatedWorkspace(updatedWorkspace);
      if (!saveResult.success) {
        return { success: false, error: saveResult.error };
      }
      console.log(`✅ 工作区位置更新成功: ${workspace.name} -> ${newPosition}`);
      return { success: true, data: updatedWorkspace };
    } catch (error) {
      return {
        success: false,
        error: {
          code: ERROR_CODES.STORAGE_ERROR,
          message: "Failed to update workspace position",
          details: error
        }
      };
    }
  }
  /**
   * 设置用户标签页隐藏状态
   */
  static async setUserTabsHiddenState(workspaceId, isHidden, hiddenTabIds = [], pinnedTabIds = []) {
    try {
      console.log(`🔄 设置工作区 ${workspaceId} 用户标签页隐藏状态: ${isHidden}`);
      const workspaceResult = await StorageManager.getWorkspace(workspaceId);
      if (!workspaceResult.success) {
        return { success: false, error: workspaceResult.error };
      }
      const workspace = workspaceResult.data;
      const updatedWorkspace = {
        ...workspace,
        userTabsHidden: isHidden,
        hiddenTabIds: [...hiddenTabIds],
        pinnedTabIds: [...pinnedTabIds],
        updatedAt: Date.now()
      };
      const saveResult = await this.saveUpdatedWorkspace(updatedWorkspace);
      if (!saveResult.success) {
        return { success: false, error: saveResult.error };
      }
      console.log(`✅ 用户标签页隐藏状态更新成功: ${workspace.name} -> ${isHidden}`);
      return { success: true };
    } catch (error) {
      return {
        success: false,
        error: {
          code: ERROR_CODES.STORAGE_ERROR,
          message: "Failed to set user tabs hidden state",
          details: error
        }
      };
    }
  }
  /**
   * 获取用户标签页隐藏状态
   */
  static async getUserTabsHiddenState(workspaceId) {
    try {
      const workspaceResult = await StorageManager.getWorkspace(workspaceId);
      if (!workspaceResult.success) {
        return { success: false, error: workspaceResult.error };
      }
      const workspace = workspaceResult.data;
      return {
        success: true,
        data: {
          isHidden: workspace.userTabsHidden || false,
          hiddenTabIds: workspace.hiddenTabIds || [],
          pinnedTabIds: workspace.pinnedTabIds || []
        }
      };
    } catch (error) {
      return {
        success: false,
        error: {
          code: ERROR_CODES.STORAGE_ERROR,
          message: "Failed to get user tabs hidden state",
          details: error
        }
      };
    }
  }
  /**
   * 清理隐藏的标签页ID
   */
  static async clearHiddenTabIds(workspaceId, tabIdsToRemove) {
    try {
      console.log(`🧹 清理工作区 ${workspaceId} 的隐藏标签页ID:`, tabIdsToRemove);
      const workspaceResult = await StorageManager.getWorkspace(workspaceId);
      if (!workspaceResult.success) {
        return { success: false, error: workspaceResult.error };
      }
      const workspace = workspaceResult.data;
      const updatedHiddenTabIds = (workspace.hiddenTabIds || []).filter(
        (id) => !tabIdsToRemove.includes(id)
      );
      const updatedPinnedTabIds = (workspace.pinnedTabIds || []).filter(
        (id) => !tabIdsToRemove.includes(id)
      );
      const updatedWorkspace = {
        ...workspace,
        hiddenTabIds: updatedHiddenTabIds,
        pinnedTabIds: updatedPinnedTabIds,
        updatedAt: Date.now()
      };
      const saveResult = await this.saveUpdatedWorkspace(updatedWorkspace);
      if (!saveResult.success) {
        return { success: false, error: saveResult.error };
      }
      console.log(`✅ 隐藏标签页ID清理成功，移除了 ${tabIdsToRemove.length} 个ID`);
      return { success: true };
    } catch (error) {
      return {
        success: false,
        error: {
          code: ERROR_CODES.STORAGE_ERROR,
          message: "Failed to clear hidden tab IDs",
          details: error
        }
      };
    }
  }
  /**
   * 添加Workona标签页ID
   */
  static async addWorkonaTabId(workspaceId, workonaId) {
    try {
      console.log(`➕ 添加Workona标签页ID到工作区 ${workspaceId}: ${workonaId}`);
      const workspaceResult = await StorageManager.getWorkspace(workspaceId);
      if (!workspaceResult.success) {
        return { success: false, error: workspaceResult.error };
      }
      const workspace = workspaceResult.data;
      const workonaTabIds = workspace.workonaTabIds || [];
      if (workonaTabIds.includes(workonaId)) {
        console.log(`ℹ️ Workona标签页ID已存在: ${workonaId}`);
        return { success: true };
      }
      const updatedWorkspace = {
        ...workspace,
        workonaTabIds: [...workonaTabIds, workonaId],
        updatedAt: Date.now()
      };
      const saveResult = await this.saveUpdatedWorkspace(updatedWorkspace);
      if (!saveResult.success) {
        return { success: false, error: saveResult.error };
      }
      console.log(`✅ Workona标签页ID添加成功: ${workonaId}`);
      return { success: true };
    } catch (error) {
      return {
        success: false,
        error: {
          code: ERROR_CODES.STORAGE_ERROR,
          message: "Failed to add Workona tab ID",
          details: error
        }
      };
    }
  }
  /**
   * 移除Workona标签页ID
   */
  static async removeWorkonaTabId(workspaceId, workonaId) {
    try {
      console.log(`➖ 从工作区 ${workspaceId} 移除Workona标签页ID: ${workonaId}`);
      const workspaceResult = await StorageManager.getWorkspace(workspaceId);
      if (!workspaceResult.success) {
        return { success: false, error: workspaceResult.error };
      }
      const workspace = workspaceResult.data;
      const workonaTabIds = workspace.workonaTabIds || [];
      const updatedWorkonaTabIds = workonaTabIds.filter((id) => id !== workonaId);
      const updatedWorkspace = {
        ...workspace,
        workonaTabIds: updatedWorkonaTabIds,
        updatedAt: Date.now()
      };
      const saveResult = await this.saveUpdatedWorkspace(updatedWorkspace);
      if (!saveResult.success) {
        return { success: false, error: saveResult.error };
      }
      console.log(`✅ Workona标签页ID移除成功: ${workonaId}`);
      return { success: true };
    } catch (error) {
      return {
        success: false,
        error: {
          code: ERROR_CODES.STORAGE_ERROR,
          message: "Failed to remove Workona tab ID",
          details: error
        }
      };
    }
  }
}

const WorkspaceStateManager$1 = /*#__PURE__*/Object.freeze(/*#__PURE__*/Object.defineProperty({
  __proto__: null,
  WorkspaceStateManager
}, Symbol.toStringTag, { value: 'Module' }));

class IdGenerator {
  /**
   * 生成工作区ID（支持 Workona 风格）
   */
  static generateWorkspaceId(type = "saved") {
    const timestamp = Date.now();
    const randomStr = Math.random().toString(36).substr(2, 9);
    if (type === "temp") {
      return `temp_${timestamp}_${randomStr}`;
    }
    return `ws_${timestamp}_${randomStr}`;
  }
  /**
   * 生成网站ID
   */
  static generateWebsiteId() {
    return `site_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }
  /**
   * 生成会话ID
   */
  static generateSessionId() {
    return `session_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }
}
class WorkspaceUtilities {
  /**
   * 验证URL格式
   */
  static isValidUrl(url) {
    const { WorkspaceWebsiteManager } = require("./WorkspaceWebsiteManager");
    const { isValid } = WorkspaceWebsiteManager.validateAndFormatUrl(url);
    return isValid;
  }
  /**
   * 清理临时工作区
   */
  static async cleanupTemporaryWorkspaces(maxAge = 24 * 60 * 60 * 1e3) {
    try {
      console.log(`🧹 清理超过 ${maxAge / (60 * 60 * 1e3)} 小时的临时工作区`);
      const workspacesResult = await StorageManager.getWorkspaces();
      if (!workspacesResult.success) {
        return { success: false, error: workspacesResult.error };
      }
      const workspaces = workspacesResult.data;
      const now = Date.now();
      let cleanedCount = 0;
      const workspacesToKeep = workspaces.filter((workspace) => {
        if (workspace.type !== "temp") {
          return true;
        }
        const age = now - workspace.createdAt;
        if (age > maxAge) {
          console.log(`🗑️ 清理过期临时工作区: ${workspace.name} (创建于 ${new Date(workspace.createdAt).toLocaleString()})`);
          cleanedCount++;
          return false;
        }
        return true;
      });
      if (cleanedCount > 0) {
        const saveResult = await StorageManager.saveWorkspaces(workspacesToKeep);
        if (!saveResult.success) {
          return { success: false, error: saveResult.error };
        }
      }
      console.log(`✅ 临时工作区清理完成，清理了 ${cleanedCount} 个过期工作区`);
      return { success: true, data: cleanedCount };
    } catch (error) {
      return {
        success: false,
        error: {
          code: ERROR_CODES.STORAGE_ERROR,
          message: "Failed to cleanup temporary workspaces",
          details: error
        }
      };
    }
  }
  /**
   * 添加当前标签页到工作区（通过Workona ID）
   */
  static async addCurrentTabByWorkonaId(workspaceId, websiteId, options = {}) {
    try {
      console.log(`📌 通过Workona ID添加当前标签页到工作区: ${workspaceId}`);
      const { TabManager } = await __vitePreload(async () => { const { TabManager } = await Promise.resolve().then(() => tabs);return { TabManager }},true?void 0:void 0);
      const activeTabResult = await TabManager.getActiveTab();
      if (!activeTabResult.success) {
        return { success: false, error: activeTabResult.error };
      }
      const activeTab = activeTabResult.data;
      if (!this.isValidUrl(activeTab.url)) {
        return {
          success: false,
          error: {
            code: ERROR_CODES.INVALID_URL,
            message: "Current tab URL is not valid"
          }
        };
      }
      const workspaceResult = await StorageManager.getWorkspace(workspaceId);
      if (!workspaceResult.success) {
        return { success: false, error: workspaceResult.error };
      }
      const workspace = workspaceResult.data;
      const existingWebsite = workspace.websites.find((w) => w.url === activeTab.url);
      if (existingWebsite) {
        await this.promoteExistingTabToWorkspaceCore(workspaceId, existingWebsite.id, activeTab.url);
        console.log(`✅ 现有网站的标签页已提升为工作区核心标签页: ${existingWebsite.title}`);
        return { success: true };
      }
      const { WorkspaceWebsiteManager } = await __vitePreload(async () => { const { WorkspaceWebsiteManager } = await Promise.resolve().then(() => WorkspaceWebsiteManager$1);return { WorkspaceWebsiteManager }},true?void 0:void 0);
      const addWebsiteResult = await WorkspaceWebsiteManager.addWebsite(
        workspaceId,
        activeTab.url,
        {
          title: options.title || activeTab.title,
          favicon: options.favicon || activeTab.favicon,
          isPinned: options.isPinned || activeTab.isPinned
        }
      );
      if (!addWebsiteResult.success) {
        return { success: false, error: addWebsiteResult.error };
      }
      const newWebsite = addWebsiteResult.data;
      const { WorkonaTabManager } = await __vitePreload(async () => { const { WorkonaTabManager } = await Promise.resolve().then(() => workonaTabManager);return { WorkonaTabManager }},true?void 0:void 0);
      const workonaId = WorkonaTabManager.generateWorkonaTabId(workspaceId);
      const mappingResult = await WorkonaTabManager.createTabIdMapping(
        workonaId,
        activeTab.id,
        workspaceId,
        websiteId || newWebsite.id,
        {
          isWorkspaceCore: true,
          source: "user_opened"
        }
      );
      if (!mappingResult.success) {
        console.warn("创建Workona ID映射失败:", mappingResult.error);
      } else {
        const { WorkspaceStateManager } = await __vitePreload(async () => { const { WorkspaceStateManager } = await Promise.resolve().then(() => WorkspaceStateManager$1);return { WorkspaceStateManager }},true?void 0:void 0);
        await WorkspaceStateManager.addWorkonaTabId(workspaceId, workonaId);
        console.log(`✅ 为标签页创建了Workona ID映射: ${workonaId}`);
      }
      console.log(`✅ 当前标签页已添加到工作区: ${newWebsite.title}`);
      return { success: true };
    } catch (error) {
      return {
        success: false,
        error: {
          code: ERROR_CODES.STORAGE_ERROR,
          message: "Failed to add current tab by Workona ID",
          details: error
        }
      };
    }
  }
  /**
   * 将现有标签页提升为工作区核心标签页
   */
  static async promoteExistingTabToWorkspaceCore(workspaceId, websiteId, url) {
    try {
      console.log(`🔄 将现有标签页提升为工作区核心标签页: ${url}`);
      const tabs = await chrome.tabs.query({ url: url + "*" });
      if (tabs.length === 0) {
        console.log(`ℹ️ 未找到匹配的标签页: ${url}`);
        return;
      }
      const { WorkonaTabManager } = await __vitePreload(async () => { const { WorkonaTabManager } = await Promise.resolve().then(() => workonaTabManager);return { WorkonaTabManager }},true?void 0:void 0);
      const { WorkspaceStateManager } = await __vitePreload(async () => { const { WorkspaceStateManager } = await Promise.resolve().then(() => WorkspaceStateManager$1);return { WorkspaceStateManager }},true?void 0:void 0);
      for (const tab of tabs) {
        if (!tab.id) continue;
        const existingResult = await WorkonaTabManager.getWorkonaIdByChromeId(tab.id);
        if (existingResult.success && existingResult.data) {
          await WorkonaTabManager.updateTabMetadata(existingResult.data, {
            isWorkspaceCore: true
          });
          console.log(`🔄 更新现有映射为工作区核心标签页: ${existingResult.data}`);
        } else {
          const workonaId = WorkonaTabManager.generateWorkonaTabId(workspaceId);
          const mappingResult = await WorkonaTabManager.createTabIdMapping(
            workonaId,
            tab.id,
            workspaceId,
            websiteId,
            {
              isWorkspaceCore: true,
              source: "user_opened"
            }
          );
          if (mappingResult.success) {
            await WorkspaceStateManager.addWorkonaTabId(workspaceId, workonaId);
            console.log(`✅ 创建新的工作区核心标签页映射: ${workonaId}`);
          }
        }
      }
    } catch (error) {
      console.error("提升标签页为工作区核心标签页失败:", error);
    }
  }
}

const WorkspaceUtilities$1 = /*#__PURE__*/Object.freeze(/*#__PURE__*/Object.defineProperty({
  __proto__: null,
  IdGenerator,
  WorkspaceUtilities
}, Symbol.toStringTag, { value: 'Module' }));

class WorkspaceManager {
  // ==================== 工作区核心操作 ====================
  /**
   * 创建新工作区
   */
  static async createWorkspace(options) {
    return WorkspaceCore.createWorkspace(options);
  }
  /**
   * 更新工作区
   */
  static async updateWorkspace(id, options) {
    return WorkspaceCore.updateWorkspace(id, options);
  }
  /**
   * 删除工作区
   */
  static async deleteWorkspace(id) {
    return WorkspaceCore.deleteWorkspace(id);
  }
  /**
   * 重新排序工作区
   */
  static async reorderWorkspaces(workspaceIds) {
    return WorkspaceCore.reorderWorkspaces(workspaceIds);
  }
  // ==================== 网站管理操作 ====================
  /**
   * 添加网站到工作区
   */
  static async addWebsite(workspaceId, url, options = {}) {
    return WorkspaceWebsiteManager.addWebsite(workspaceId, url, options);
  }
  /**
   * 从工作区移除网站
   */
  static async removeWebsite(workspaceId, websiteId) {
    return WorkspaceWebsiteManager.removeWebsite(workspaceId, websiteId);
  }
  /**
   * 更新网站信息
   */
  static async updateWebsite(workspaceId, websiteId, updates) {
    return WorkspaceWebsiteManager.updateWebsite(workspaceId, websiteId, updates);
  }
  /**
   * 重新排序工作区中的网站
   */
  static async reorderWebsites(workspaceId, websiteIds) {
    return WorkspaceWebsiteManager.reorderWebsites(workspaceId, websiteIds);
  }
  // ==================== 状态管理操作 ====================
  /**
   * 设置用户标签页隐藏状态
   */
  static async setUserTabsHiddenState(workspaceId, isHidden, hiddenTabIds = [], pinnedTabIds = []) {
    return WorkspaceStateManager.setUserTabsHiddenState(workspaceId, isHidden, hiddenTabIds, pinnedTabIds);
  }
  /**
   * 获取用户标签页隐藏状态
   */
  static async getUserTabsHiddenState(workspaceId) {
    return WorkspaceStateManager.getUserTabsHiddenState(workspaceId);
  }
  /**
   * 清理隐藏的标签页ID
   */
  static async clearHiddenTabIds(workspaceId, tabIdsToRemove) {
    return WorkspaceStateManager.clearHiddenTabIds(workspaceId, tabIdsToRemove);
  }
  /**
   * 更新工作区类型
   */
  static async updateWorkspaceType(workspaceId, newType) {
    return WorkspaceCore.updateWorkspaceType(workspaceId, newType);
  }
  /**
   * 更新工作区位置
   */
  static async updateWorkspacePosition(workspaceId, newPosition) {
    return WorkspaceStateManager.updateWorkspacePosition(workspaceId, newPosition);
  }
  /**
   * 更新工作区状态
   */
  static async updateWorkspaceState(workspaceId, newState) {
    return WorkspaceStateManager.updateWorkspaceState(workspaceId, newState);
  }
  /**
   * 添加Workona标签页ID
   */
  static async addWorkonaTabId(workspaceId, workonaId) {
    return WorkspaceStateManager.addWorkonaTabId(workspaceId, workonaId);
  }
  /**
   * 移除Workona标签页ID
   */
  static async removeWorkonaTabId(workspaceId, workonaId) {
    return WorkspaceStateManager.removeWorkonaTabId(workspaceId, workonaId);
  }
  // ==================== 工具方法 ====================
  /**
   * 添加当前标签页到工作区（通过Workona ID）
   */
  static async addCurrentTabByWorkonaId(workspaceId, websiteId, options = {}) {
    return WorkspaceUtilities.addCurrentTabByWorkonaId(workspaceId, websiteId, options);
  }
  /**
   * 清理临时工作区
   */
  static async cleanupTemporaryWorkspaces(maxAge = 24 * 60 * 60 * 1e3) {
    return WorkspaceUtilities.cleanupTemporaryWorkspaces(maxAge);
  }
}

class WorkspaceStateSync {
  /**
   * 发送工作区状态更新事件
   */
  static sendWorkspaceStateUpdate(workspaceId, eventType) {
    try {
      if (typeof window !== "undefined") {
        const eventName = eventType === "switch" ? "workspaceSwitchComplete" : "userTabsVisibilityChanged";
        const event = new CustomEvent(eventName, {
          detail: { workspaceId }
        });
        window.dispatchEvent(event);
      }
      if (typeof chrome !== "undefined" && chrome.runtime) {
        const messageType = eventType === "switch" ? "WORKSPACE_SWITCH_COMPLETE" : "USER_TABS_VISIBILITY_CHANGED";
        chrome.runtime.sendMessage({
          type: messageType,
          workspaceId
        }).catch((error) => {
          console.log(`发送${eventType}事件消息失败:`, error);
        });
      }
    } catch (error) {
      console.error(`发送工作区状态更新事件失败:`, error);
    }
  }
  /**
   * 添加工作区状态监听器
   */
  static addStateListener(callback) {
    const handleWorkspaceSwitchComplete = (event) => {
      callback(event.detail.workspaceId, "switch");
    };
    const handleUserTabsVisibilityChanged = (event) => {
      callback(event.detail.workspaceId, "userTabsVisibility");
    };
    const handleChromeMessage = (message) => {
      if (message.type === "WORKSPACE_SWITCH_COMPLETE") {
        callback(message.workspaceId, "switch");
      } else if (message.type === "USER_TABS_VISIBILITY_CHANGED") {
        callback(message.workspaceId, "userTabsVisibility");
      }
    };
    if (typeof window !== "undefined") {
      window.addEventListener("workspaceSwitchComplete", handleWorkspaceSwitchComplete);
      window.addEventListener("userTabsVisibilityChanged", handleUserTabsVisibilityChanged);
    }
    if (typeof chrome !== "undefined" && chrome.runtime) {
      chrome.runtime.onMessage.addListener(handleChromeMessage);
    }
    return () => {
      if (typeof window !== "undefined") {
        window.removeEventListener("workspaceSwitchComplete", handleWorkspaceSwitchComplete);
        window.removeEventListener("userTabsVisibilityChanged", handleUserTabsVisibilityChanged);
      }
      if (typeof chrome !== "undefined" && chrome.runtime) {
        chrome.runtime.onMessage.removeListener(handleChromeMessage);
      }
    };
  }
}

class WorkspaceSessionManager {
  static currentSession = null;
  static isSessionSwitching = false;
  /**
   * 获取当前会话
   */
  static getCurrentSession() {
    return this.currentSession;
  }
  /**
   * 切换工作区会话
   * 复用现有的 WorkspaceStateSync 事件系统
   */
  static async switchSession(workspaceId, options = {}) {
    try {
      if (this.isSessionSwitching) {
        console.log("⏳ 会话切换正在进行中，跳过重复请求");
        return { success: true };
      }
      this.isSessionSwitching = true;
      console.log(`🔄 开始切换到工作区会话: ${workspaceId}`);
      if (this.currentSession && options.preserveCurrentSession !== false) {
        const saveResult = await this.saveSession(this.currentSession);
        if (!saveResult.success) {
          console.warn("保存当前会话失败:", saveResult.error);
        }
      }
      const loadResult = await this.loadSession(workspaceId);
      if (!loadResult.success) {
        this.isSessionSwitching = false;
        return { success: false, error: loadResult.error };
      }
      this.currentSession = loadResult.data;
      WorkspaceStateSync.sendWorkspaceStateUpdate(workspaceId, "switch");
      if (options.restoreTabOrder !== false && this.currentSession.tabOrder.length > 0) {
        await this.restoreTabOrder(this.currentSession);
      }
      if (!this.currentSession.activeTabId && this.currentSession.tabOrder.length > 0) {
        console.log("📋 没有保存的活跃标签页，智能选择合适的标签页激活");
        let targetTabId = null;
        for (const workonaId of this.currentSession.tabOrder) {
          const metadataResult = await WorkonaTabManager.getTabMetadata(workonaId);
          if (metadataResult.success && metadataResult.data?.isWorkspaceCore) {
            targetTabId = workonaId;
            console.log(`🎯 选择工作区核心标签页: ${workonaId}`);
            break;
          }
        }
        if (!targetTabId && this.currentSession.tabOrder.length > 0) {
          targetTabId = this.currentSession.tabOrder[0];
          console.log(`🎯 选择第一个标签页: ${targetTabId}`);
        }
        if (targetTabId) {
          const chromeIdResult = await WorkonaTabManager.getChromeIdByWorkonaId(targetTabId);
          if (chromeIdResult.success && chromeIdResult.data) {
            try {
              await chrome.tabs.update(chromeIdResult.data, { active: true });
              console.log(`✨ 智能激活标签页: ${targetTabId}`);
              this.currentSession.activeTabId = targetTabId;
              await this.saveSession(this.currentSession);
            } catch (error) {
              console.warn("智能激活标签页失败:", error);
            }
          }
        }
      }
      console.log(`✅ 成功切换到工作区会话: ${workspaceId}`);
      return { success: true };
    } catch (error) {
      return {
        success: false,
        error: {
          code: ERROR_CODES.TAB_ERROR,
          message: "Failed to switch workspace session",
          details: error
        }
      };
    } finally {
      this.isSessionSwitching = false;
    }
  }
  /**
   * 获取当前工作区的标签页
   * 确保会话只包含当前工作区的标签页
   */
  static getCurrentWorkspaceTabs() {
    if (!this.currentSession) {
      return [];
    }
    return Object.values(this.currentSession.tabs);
  }
  /**
   * 获取当前工作区的标签页顺序
   */
  static getCurrentTabOrder() {
    if (!this.currentSession) {
      return [];
    }
    return [...this.currentSession.tabOrder];
  }
  /**
   * 更新会话中的标签页
   */
  static async updateSessionTab(workonaId, tab) {
    try {
      if (!this.currentSession) {
        return {
          success: false,
          error: {
            code: ERROR_CODES.TAB_ERROR,
            message: "No active session"
          }
        };
      }
      const existingTab = this.currentSession.tabs[workonaId];
      this.currentSession.tabs[workonaId] = {
        ...existingTab,
        // 保持现有属性
        ...tab
        // 应用更新
      };
      this.currentSession.lastActiveAt = Date.now();
      if (!this.currentSession.tabOrder.includes(workonaId)) {
        this.currentSession.tabOrder.push(workonaId);
        console.log(`📝 添加新标签页到会话顺序: ${workonaId}`);
      } else {
        console.log(`🔄 更新现有标签页会话信息: ${workonaId}`);
      }
      const saveResult = await this.saveSession(this.currentSession);
      if (!saveResult.success) {
        return { success: false, error: saveResult.error };
      }
      return { success: true };
    } catch (error) {
      return {
        success: false,
        error: {
          code: ERROR_CODES.TAB_ERROR,
          message: "Failed to update session tab",
          details: error
        }
      };
    }
  }
  /**
   * 从会话中移除标签页
   */
  static async removeSessionTab(workonaId) {
    try {
      if (!this.currentSession) {
        return { success: true };
      }
      delete this.currentSession.tabs[workonaId];
      this.currentSession.tabOrder = this.currentSession.tabOrder.filter((id) => id !== workonaId);
      if (this.currentSession.activeTabId === workonaId) {
        this.currentSession.activeTabId = void 0;
      }
      this.currentSession.lastActiveAt = Date.now();
      const saveResult = await this.saveSession(this.currentSession);
      if (!saveResult.success) {
        return { success: false, error: saveResult.error };
      }
      console.log(`🗑️ 从会话中移除标签页: ${workonaId}`);
      return { success: true };
    } catch (error) {
      return {
        success: false,
        error: {
          code: ERROR_CODES.TAB_ERROR,
          message: "Failed to remove session tab",
          details: error
        }
      };
    }
  }
  /**
   * 设置当前激活的标签页
   */
  static async setActiveTab(workonaId) {
    try {
      if (!this.currentSession) {
        return {
          success: false,
          error: {
            code: ERROR_CODES.TAB_ERROR,
            message: "No active session"
          }
        };
      }
      this.currentSession.activeTabId = workonaId;
      this.currentSession.lastActiveAt = Date.now();
      if (this.currentSession.tabs[workonaId]) {
        this.currentSession.tabs[workonaId].lastActiveAt = Date.now();
      }
      const saveResult = await this.saveSession(this.currentSession);
      if (!saveResult.success) {
        return { success: false, error: saveResult.error };
      }
      console.log(`📍 记录活跃标签页: ${workonaId} (工作区: ${this.currentSession.workspaceId})`);
      return { success: true };
    } catch (error) {
      return {
        success: false,
        error: {
          code: ERROR_CODES.TAB_ERROR,
          message: "Failed to set active tab",
          details: error
        }
      };
    }
  }
  /**
   * 实时同步当前工作区的标签页状态
   * 记录标签页顺序和活跃状态
   */
  static async syncCurrentWorkspaceState() {
    try {
      if (!this.currentSession) {
        return { success: true };
      }
      console.log(`🔄 同步工作区标签页状态: ${this.currentSession.workspaceId}`);
      const currentWindow = await chrome.windows.getCurrent();
      const allTabs = await chrome.tabs.query({ windowId: currentWindow.id });
      const workonaTabIds = await WorkonaTabManager.getWorkspaceWorkonaTabIds(this.currentSession.workspaceId);
      if (!workonaTabIds.success) {
        return { success: false, error: workonaTabIds.error };
      }
      const currentWorkspaceTabs = [];
      let activeWorkonaId;
      for (const workonaId of workonaTabIds.data) {
        const chromeIdResult = await WorkonaTabManager.getChromeIdByWorkonaId(workonaId);
        if (chromeIdResult.success && chromeIdResult.data) {
          const chromeTab = allTabs.find((tab) => tab.id === chromeIdResult.data);
          if (chromeTab) {
            currentWorkspaceTabs.push({
              workonaId,
              chromeTab,
              index: chromeTab.index
            });
            if (chromeTab.active) {
              activeWorkonaId = workonaId;
            }
          }
        }
      }
      currentWorkspaceTabs.sort((a, b) => a.index - b.index);
      const newTabOrder = currentWorkspaceTabs.map((item) => item.workonaId);
      let hasChanges = false;
      if (JSON.stringify(this.currentSession.tabOrder) !== JSON.stringify(newTabOrder)) {
        this.currentSession.tabOrder = newTabOrder;
        hasChanges = true;
        console.log(`📋 更新标签页顺序: [${newTabOrder.join(", ")}]`);
      }
      if (activeWorkonaId && this.currentSession.activeTabId !== activeWorkonaId) {
        this.currentSession.activeTabId = activeWorkonaId;
        hasChanges = true;
        const metadataResult = await WorkonaTabManager.getTabMetadata(activeWorkonaId);
        const isWorkspaceCore = metadataResult.success && metadataResult.data?.isWorkspaceCore;
        console.log(`📍 更新活跃标签页: ${activeWorkonaId} ${isWorkspaceCore ? "(工作区专用)" : "(用户标签页)"}`);
      }
      if (hasChanges) {
        this.currentSession.lastActiveAt = Date.now();
        const saveResult = await this.saveSession(this.currentSession);
        if (!saveResult.success) {
          return { success: false, error: saveResult.error };
        }
      }
      return { success: true };
    } catch (error) {
      return {
        success: false,
        error: {
          code: ERROR_CODES.TAB_ERROR,
          message: "Failed to sync workspace state",
          details: error
        }
      };
    }
  }
  /**
   * 更新标签页顺序
   */
  static async updateTabOrder(newOrder) {
    try {
      if (!this.currentSession) {
        return {
          success: false,
          error: {
            code: ERROR_CODES.TAB_ERROR,
            message: "No active session"
          }
        };
      }
      const validOrder = newOrder.filter((workonaId) => this.currentSession.tabs[workonaId]);
      this.currentSession.tabOrder = validOrder;
      this.currentSession.lastActiveAt = Date.now();
      const saveResult = await this.saveSession(this.currentSession);
      if (!saveResult.success) {
        return { success: false, error: saveResult.error };
      }
      console.log(`📋 更新标签页顺序: ${validOrder.length} 个标签页`);
      return { success: true };
    } catch (error) {
      return {
        success: false,
        error: {
          code: ERROR_CODES.TAB_ERROR,
          message: "Failed to update tab order",
          details: error
        }
      };
    }
  }
  /**
   * 清除当前会话状态
   * 用于浏览器重启后重置工作区状态
   */
  static async clearCurrentSession() {
    try {
      console.log("🔄 清除当前工作区会话状态");
      this.currentSession = null;
      this.isSessionSwitching = false;
      console.log("✅ 工作区会话状态已清除");
      return { success: true };
    } catch (error) {
      return {
        success: false,
        error: {
          code: ERROR_CODES.TAB_ERROR,
          message: "Failed to clear current session",
          details: error
        }
      };
    }
  }
  /**
   * 将新创建的标签页添加到当前会话
   * 用于后台标签页创建完成后的增量更新
   */
  static async addNewTabsToCurrentSession(newWorkonaIds) {
    try {
      if (!this.currentSession) {
        console.log("⚠️ 没有活跃会话，跳过添加新标签页");
        return { success: true };
      }
      if (newWorkonaIds.length === 0) {
        return { success: true };
      }
      console.log(`📝 添加 ${newWorkonaIds.length} 个新标签页到当前会话`);
      let addedCount = 0;
      for (const workonaId of newWorkonaIds) {
        if (!this.currentSession.tabOrder.includes(workonaId)) {
          this.currentSession.tabOrder.push(workonaId);
          addedCount++;
        }
      }
      if (addedCount > 0) {
        this.currentSession.lastActiveAt = Date.now();
        const saveResult = await this.saveSession(this.currentSession);
        if (!saveResult.success) {
          return { success: false, error: saveResult.error };
        }
        console.log(`✅ 成功添加 ${addedCount} 个新标签页到会话顺序`);
      } else {
        console.log("ℹ️ 所有新标签页都已存在于会话中");
      }
      return { success: true };
    } catch (error) {
      return {
        success: false,
        error: {
          code: ERROR_CODES.TAB_ERROR,
          message: "Failed to add new tabs to current session",
          details: error
        }
      };
    }
  }
  /**
   * 保存会话到存储
   */
  static async saveSession(session) {
    try {
      const sessionsResult = await StorageManager.getWorkspaceSessions();
      if (!sessionsResult.success) {
        return { success: false, error: sessionsResult.error };
      }
      const sessions = sessionsResult.data;
      sessions[session.workspaceId] = session;
      const saveResult = await StorageManager.saveWorkspaceSessions(sessions);
      if (!saveResult.success) {
        return { success: false, error: saveResult.error };
      }
      return { success: true };
    } catch (error) {
      return {
        success: false,
        error: {
          code: ERROR_CODES.STORAGE_ERROR,
          message: "Failed to save session",
          details: error
        }
      };
    }
  }
  /**
   * 从存储加载会话
   */
  static async loadSession(workspaceId) {
    try {
      const sessionsResult = await StorageManager.getWorkspaceSessions();
      if (!sessionsResult.success) {
        return { success: false, error: sessionsResult.error };
      }
      const sessions = sessionsResult.data;
      let session = sessions[workspaceId];
      if (!session) {
        session = {
          workspaceId,
          tabs: {},
          tabOrder: [],
          lastActiveAt: Date.now()
        };
        sessions[workspaceId] = session;
        const saveResult = await StorageManager.saveWorkspaceSessions(sessions);
        if (!saveResult.success) {
          return { success: false, error: saveResult.error };
        }
        console.log(`✨ 创建新的工作区会话: ${workspaceId}`);
      }
      return { success: true, data: session };
    } catch (error) {
      return {
        success: false,
        error: {
          code: ERROR_CODES.STORAGE_ERROR,
          message: "Failed to load session",
          details: error
        }
      };
    }
  }
  /**
   * 恢复标签页顺序和活跃状态
   */
  static async restoreTabOrder(session) {
    try {
      console.log(`🔄 恢复工作区状态: ${session.tabOrder.length} 个标签页`);
      if (session.tabOrder.length === 0) {
        console.log("📋 没有保存的标签页顺序，跳过恢复");
        return;
      }
      const currentWindow = await chrome.windows.getCurrent();
      const allTabs = await chrome.tabs.query({ windowId: currentWindow.id });
      const tabsToReorder = [];
      for (let i = 0; i < session.tabOrder.length; i++) {
        const workonaId = session.tabOrder[i];
        const chromeIdResult = await WorkonaTabManager.getChromeIdByWorkonaId(workonaId);
        if (chromeIdResult.success && chromeIdResult.data) {
          const chromeTab = allTabs.find((tab) => tab.id === chromeIdResult.data);
          if (chromeTab) {
            tabsToReorder.push({
              workonaId,
              chromeId: chromeTab.id,
              targetIndex: i
            });
          }
        }
      }
      let targetActiveTabId = null;
      if (session.activeTabId) {
        const activeTabResult = await WorkonaTabManager.getChromeIdByWorkonaId(session.activeTabId);
        if (activeTabResult.success && activeTabResult.data) {
          const targetTab = allTabs.find((tab) => tab.id === activeTabResult.data);
          if (targetTab) {
            targetActiveTabId = activeTabResult.data;
            console.log(`🎯 确定目标活跃标签页: ${session.activeTabId} (Chrome ID: ${targetActiveTabId})`);
          }
        }
      }
      console.log(`📋 重新排列 ${tabsToReorder.length} 个标签页`);
      const movePromises = tabsToReorder.map(async (tabInfo) => {
        try {
          await chrome.tabs.move(tabInfo.chromeId, { index: tabInfo.targetIndex });
          console.log(`📍 移动标签页 ${tabInfo.workonaId} 到位置 ${tabInfo.targetIndex}`);
        } catch (error) {
          console.warn(`⚠️ 移动标签页 ${tabInfo.workonaId} 失败:`, error);
        }
      });
      await Promise.all(movePromises);
      console.log("✅ 所有标签页移动操作完成");
      if (targetActiveTabId) {
        try {
          const metadataResult = await WorkonaTabManager.getTabMetadata(session.activeTabId);
          const isWorkspaceCore = metadataResult.success && metadataResult.data?.isWorkspaceCore;
          await chrome.tabs.update(targetActiveTabId, { active: true });
          console.log(`✨ 立即激活目标标签页: ${session.activeTabId} ${isWorkspaceCore ? "(工作区专用)" : "(用户标签页)"}`);
          const currentWindow2 = await chrome.windows.getCurrent();
          if (currentWindow2.id) {
            chrome.windows.update(currentWindow2.id, { focused: true });
          }
          console.log(`🎯 工作区标签页状态恢复完成，无闪烁切换`);
        } catch (error) {
          console.warn(`⚠️ 激活目标标签页失败:`, error);
        }
      } else {
        console.log("📋 没有保存的活跃标签页，智能选择合适的标签页");
        if (session.tabOrder.length > 0) {
          let bestTabId = null;
          for (const workonaId of session.tabOrder) {
            const chromeIdResult = await WorkonaTabManager.getChromeIdByWorkonaId(workonaId);
            if (chromeIdResult.success && chromeIdResult.data) {
              const metadataResult = await WorkonaTabManager.getTabMetadata(workonaId);
              if (metadataResult.success && metadataResult.data?.isWorkspaceCore) {
                bestTabId = workonaId;
                break;
              }
              if (!bestTabId) {
                bestTabId = workonaId;
              }
            }
          }
          if (bestTabId) {
            const chromeIdResult = await WorkonaTabManager.getChromeIdByWorkonaId(bestTabId);
            if (chromeIdResult.success && chromeIdResult.data) {
              try {
                await chrome.tabs.update(chromeIdResult.data, { active: true });
                console.log(`✨ 智能激活标签页: ${bestTabId}`);
              } catch (error) {
                console.warn("智能激活标签页失败:", error);
              }
            }
          }
        }
      }
      console.log("✅ 工作区状态恢复完成");
    } catch (error) {
      console.warn("恢复工作区状态失败:", error);
    }
  }
  /**
   * 清理工作区会话
   */
  static async clearWorkspaceSession(workspaceId) {
    try {
      const sessionsResult = await StorageManager.getWorkspaceSessions();
      if (!sessionsResult.success) {
        return { success: false, error: sessionsResult.error };
      }
      const sessions = sessionsResult.data;
      delete sessions[workspaceId];
      const saveResult = await StorageManager.saveWorkspaceSessions(sessions);
      if (!saveResult.success) {
        return { success: false, error: saveResult.error };
      }
      if (this.currentSession && this.currentSession.workspaceId === workspaceId) {
        this.currentSession = null;
      }
      console.log(`🗑️ 清理工作区会话: ${workspaceId}`);
      return { success: true };
    } catch (error) {
      return {
        success: false,
        error: {
          code: ERROR_CODES.STORAGE_ERROR,
          message: "Failed to clear workspace session",
          details: error
        }
      };
    }
  }
}

class WorkonaTabManager {
  /**
   * 生成 Workona 风格标签页ID
   * 格式：t-{workspaceId}-{uuid}
   */
  static generateWorkonaTabId(workspaceId) {
    const uuid = "xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx".replace(/[xy]/g, function(c) {
      const r = Math.random() * 16 | 0;
      const v = c === "x" ? r : r & 3 | 8;
      return v.toString(16);
    });
    return `t-${workspaceId}-${uuid}`;
  }
  /**
   * 创建标签页ID映射关系（增强版：支持元数据分类）
   *
   * 🎯 核心功能：
   * 1. 建立Chrome标签页ID与Workona ID的双向映射关系
   * 2. 支持标签页分类（核心标签页 vs 会话标签页）
   * 3. 记录标签页来源和元数据信息
   * 4. 提供标签页生命周期管理基础
   *
   * 📋 映射类型：
   * - 核心标签页：工作区配置中的网站，持久化存储
   * - 会话标签页：用户临时打开的标签页，会话级存储
   *
   * 🏷️ 标签页来源：
   * - workspace_website：来自工作区网站配置
   * - user_opened：用户主动打开的标签页
   * - session_restored：会话恢复的标签页
   *
   * 💾 存储策略：
   * - 核心标签页：存储到持久化映射表
   * - 会话标签页：存储到会话映射表
   * - 元数据：记录创建时间、来源、关联信息
   *
   * @param workonaId Workona格式的标签页ID (t-{workspaceId}-{uuid})
   * @param chromeId Chrome浏览器的标签页ID
   * @param workspaceId 所属工作区ID
   * @param websiteId 关联的网站配置ID（可选）
   * @param options 额外选项和元数据
   * @returns 创建的映射关系对象
   */
  static async createTabIdMapping(workonaId, chromeId, workspaceId, websiteId, options) {
    try {
      const isWorkspaceCore = options?.isWorkspaceCore ?? !!websiteId;
      const tabType = isWorkspaceCore ? "core" : "session";
      const source = options?.source ?? (websiteId ? "workspace_website" : "user_opened");
      console.log(`🆔 创建标签页映射: ${workonaId} -> Chrome ID ${chromeId} (类型: ${tabType}, 来源: ${source})`);
      const mapping = {
        workonaId,
        chromeId,
        workspaceId,
        websiteId,
        createdAt: Date.now(),
        lastSyncAt: Date.now(),
        // 标签页分类元数据
        isWorkspaceCore,
        tabType,
        metadata: {
          source,
          // 标签页来源
          sessionId: options?.sessionId,
          // 会话ID（如果适用）
          originalUrl: options?.originalUrl,
          // 原始URL（用于恢复）
          addedToWorkspaceAt: isWorkspaceCore ? Date.now() : void 0,
          // 添加到工作区的时间
          isPinned: false,
          // 初始化为未固定状态
          pinnedAt: void 0,
          // 固定时间
          unpinnedAt: void 0
          // 取消固定时间
        }
      };
      const mappingsResult = await StorageManager.getTabIdMappings();
      if (!mappingsResult.success) {
        return { success: false, error: mappingsResult.error };
      }
      const mappings = mappingsResult.data;
      const existingIndex = mappings.findIndex(
        (m) => m.workonaId === workonaId || m.chromeId === chromeId
      );
      if (existingIndex >= 0) {
        const existing = mappings[existingIndex];
        console.log(`🔄 更新现有映射: ${existing.workonaId} -> ${chromeId}`);
        mappings[existingIndex] = {
          ...mapping,
          createdAt: existing.createdAt,
          // 保留原始创建时间
          metadata: {
            ...existing.metadata,
            ...mapping.metadata,
            source: mapping.metadata?.source || existing.metadata?.source || "user_opened",
            addedToWorkspaceAt: existing.metadata?.addedToWorkspaceAt || mapping.metadata?.addedToWorkspaceAt
          }
        };
      } else {
        console.log(`➕ 添加新映射: ${workonaId} -> ${chromeId}`);
        mappings.push(mapping);
      }
      const saveResult = await StorageManager.saveTabIdMappings(mappings);
      if (!saveResult.success) {
        return { success: false, error: saveResult.error };
      }
      try {
        const tab = await chrome.tabs.get(chromeId);
        const isPinned = tab.pinned;
        await chrome.scripting.executeScript({
          target: { tabId: chromeId },
          func: (workonaId2, workspaceId2, websiteId2, isWorkspaceCore2, isPinned2) => {
            const workonaData = {
              workonaId: workonaId2,
              workspaceId: workspaceId2,
              websiteId: websiteId2,
              isWorkspaceCore: isWorkspaceCore2,
              isPinned: isPinned2,
              timestamp: Date.now()
            };
            sessionStorage.setItem("workonaData", JSON.stringify(workonaData));
            console.log(`📝 标签页会话存储 Workona 数据:`, workonaData);
          },
          args: [workonaId, workspaceId, websiteId || "", isWorkspaceCore, isPinned]
        });
      } catch (error) {
        console.warn(`⚠️ 无法为标签页 ${chromeId} 设置会话存储:`, error);
      }
      console.log(`✅ 创建标签页ID映射: ${workonaId} <-> ${chromeId} (类型: ${tabType}, 核心: ${isWorkspaceCore})`);
      return { success: true, data: mapping };
    } catch (error) {
      return {
        success: false,
        error: {
          code: ERROR_CODES.TAB_ERROR,
          message: "Failed to create tab ID mapping",
          details: error
        }
      };
    }
  }
  /**
   * 根据 Chrome ID 获取 Workona ID
   */
  static async getWorkonaIdByChromeId(chromeId) {
    try {
      const mappingsResult = await StorageManager.getTabIdMappings();
      if (!mappingsResult.success) {
        return { success: false, error: mappingsResult.error };
      }
      const mappings = mappingsResult.data;
      const mapping = mappings.find((m) => m.chromeId === chromeId);
      return { success: true, data: mapping?.workonaId || null };
    } catch (error) {
      return {
        success: false,
        error: {
          code: ERROR_CODES.TAB_ERROR,
          message: "Failed to get Workona ID by Chrome ID",
          details: error
        }
      };
    }
  }
  /**
   * 根据 Workona ID 获取 Chrome ID
   */
  static async getChromeIdByWorkonaId(workonaId) {
    try {
      const mappingsResult = await StorageManager.getTabIdMappings();
      if (!mappingsResult.success) {
        return { success: false, error: mappingsResult.error };
      }
      const mappings = mappingsResult.data;
      const mapping = mappings.find((m) => m.workonaId === workonaId);
      return { success: true, data: mapping?.chromeId || null };
    } catch (error) {
      return {
        success: false,
        error: {
          code: ERROR_CODES.TAB_ERROR,
          message: "Failed to get Chrome ID by Workona ID",
          details: error
        }
      };
    }
  }
  /**
   * 同步标签页映射关系
   * 清理无效的映射，更新现有映射的同步时间
   */
  static async syncTabMappings() {
    try {
      console.log("🔄 开始同步标签页映射关系...");
      const mappingsResult = await StorageManager.getTabIdMappings();
      if (!mappingsResult.success) {
        return { success: false, error: mappingsResult.error };
      }
      const mappings = mappingsResult.data;
      const validMappings = [];
      let cleanedCount = 0;
      const allTabs = await chrome.tabs.query({});
      const existingChromeIds = new Set(allTabs.map((tab) => tab.id));
      for (const mapping of mappings) {
        if (existingChromeIds.has(mapping.chromeId)) {
          mapping.lastSyncAt = Date.now();
          validMappings.push(mapping);
        } else {
          console.log(`🗑️ 清理无效映射: ${mapping.workonaId} <-> ${mapping.chromeId}`);
          cleanedCount++;
        }
      }
      const saveResult = await StorageManager.saveTabIdMappings(validMappings);
      if (!saveResult.success) {
        return { success: false, error: saveResult.error };
      }
      console.log(`✅ 标签页映射同步完成，清理了 ${cleanedCount} 个无效映射`);
      return { success: true, data: cleanedCount };
    } catch (error) {
      return {
        success: false,
        error: {
          code: ERROR_CODES.TAB_ERROR,
          message: "Failed to sync tab mappings",
          details: error
        }
      };
    }
  }
  /**
   * 删除标签页映射
   */
  static async removeTabMapping(workonaId) {
    try {
      const mappingsResult = await StorageManager.getTabIdMappings();
      if (!mappingsResult.success) {
        return { success: false, error: mappingsResult.error };
      }
      const mappings = mappingsResult.data;
      const filteredMappings = mappings.filter((m) => m.workonaId !== workonaId);
      const saveResult = await StorageManager.saveTabIdMappings(filteredMappings);
      if (!saveResult.success) {
        return { success: false, error: saveResult.error };
      }
      console.log(`🗑️ 删除标签页映射: ${workonaId}`);
      return { success: true };
    } catch (error) {
      return {
        success: false,
        error: {
          code: ERROR_CODES.TAB_ERROR,
          message: "Failed to remove tab mapping",
          details: error
        }
      };
    }
  }
  /**
   * 获取工作区的所有 Workona 标签页ID
   */
  static async getWorkspaceWorkonaTabIds(workspaceId) {
    try {
      const mappingsResult = await StorageManager.getTabIdMappings();
      if (!mappingsResult.success) {
        return { success: false, error: mappingsResult.error };
      }
      const mappings = mappingsResult.data;
      const workonaIds = mappings.filter((m) => m.workspaceId === workspaceId).map((m) => m.workonaId);
      return { success: true, data: workonaIds };
    } catch (error) {
      return {
        success: false,
        error: {
          code: ERROR_CODES.TAB_ERROR,
          message: "Failed to get workspace Workona tab IDs",
          details: error
        }
      };
    }
  }
  /**
   * 批量清理工作区的标签页映射
   */
  static async clearWorkspaceTabMappings(workspaceId) {
    try {
      const mappingsResult = await StorageManager.getTabIdMappings();
      if (!mappingsResult.success) {
        return { success: false, error: mappingsResult.error };
      }
      const mappings = mappingsResult.data;
      const remainingMappings = mappings.filter((m) => m.workspaceId !== workspaceId);
      const clearedCount = mappings.length - remainingMappings.length;
      const saveResult = await StorageManager.saveTabIdMappings(remainingMappings);
      if (!saveResult.success) {
        return { success: false, error: saveResult.error };
      }
      console.log(`🗑️ 清理工作区 ${workspaceId} 的 ${clearedCount} 个标签页映射`);
      return { success: true, data: clearedCount };
    } catch (error) {
      return {
        success: false,
        error: {
          code: ERROR_CODES.TAB_ERROR,
          message: "Failed to clear workspace tab mappings",
          details: error
        }
      };
    }
  }
  // === 概念性重构：标签页元数据管理方法 ===
  /**
   * 获取标签页元数据
   */
  static async getTabMetadata(workonaId) {
    try {
      const mappingsResult = await StorageManager.getTabIdMappings();
      if (!mappingsResult.success) {
        return { success: false, error: mappingsResult.error };
      }
      const mappings = mappingsResult.data;
      const mapping = mappings.find((m) => m.workonaId === workonaId);
      return { success: true, data: mapping || null };
    } catch (error) {
      return {
        success: false,
        error: {
          code: ERROR_CODES.TAB_ERROR,
          message: "Failed to get tab metadata",
          details: error
        }
      };
    }
  }
  /**
   * 更新标签页元数据
   */
  static async updateTabMetadata(workonaId, updates) {
    try {
      const mappingsResult = await StorageManager.getTabIdMappings();
      if (!mappingsResult.success) {
        return { success: false, error: mappingsResult.error };
      }
      const mappings = mappingsResult.data;
      const mappingIndex = mappings.findIndex((m) => m.workonaId === workonaId);
      if (mappingIndex === -1) {
        return {
          success: false,
          error: {
            code: ERROR_CODES.TAB_ERROR,
            message: "Tab mapping not found"
          }
        };
      }
      const existingMapping = mappings[mappingIndex];
      const updatedMapping = {
        ...existingMapping,
        ...updates,
        lastSyncAt: Date.now(),
        metadata: {
          ...existingMapping.metadata,
          ...updates.metadata,
          // 确保 source 字段始终有值
          source: updates.metadata?.source || existingMapping.metadata?.source || "user_opened"
        }
      };
      mappings[mappingIndex] = updatedMapping;
      const saveResult = await StorageManager.saveTabIdMappings(mappings);
      if (!saveResult.success) {
        return { success: false, error: saveResult.error };
      }
      console.log(`📝 更新标签页元数据: ${workonaId} (核心: ${updatedMapping.isWorkspaceCore})`);
      return { success: true, data: updatedMapping };
    } catch (error) {
      return {
        success: false,
        error: {
          code: ERROR_CODES.TAB_ERROR,
          message: "Failed to update tab metadata",
          details: error
        }
      };
    }
  }
  /**
   * 将会话临时标签页转换为工作区核心标签页
   */
  static async promoteToWorkspaceCore(workonaId, websiteId) {
    try {
      const updates = {
        isWorkspaceCore: true,
        tabType: "core",
        websiteId,
        metadata: {
          source: "workspace_website",
          addedToWorkspaceAt: Date.now()
        }
      };
      const result = await this.updateTabMetadata(workonaId, updates);
      if (result.success) {
        console.log(`⬆️ 标签页提升为工作区核心: ${workonaId}`);
        try {
          const workspaceId = workonaId.split("-")[1];
          await UserTabsRealTimeMonitor.triggerImmediateStateCheck();
          await UserTabsRealTimeMonitor.forceRefreshWorkspaceState(workspaceId);
          console.log(`🔄 已触发工作区 ${workspaceId} 用户标签页状态更新 (标签页提升)`);
        } catch (updateError) {
          console.warn("触发用户标签页状态更新失败:", updateError);
        }
      }
      return result;
    } catch (error) {
      return {
        success: false,
        error: {
          code: ERROR_CODES.TAB_ERROR,
          message: "Failed to promote tab to workspace core",
          details: error
        }
      };
    }
  }
  /**
   * 将工作区核心标签页降级为会话临时标签页
   * 当用户从工作区中移除某个工作区专属标签页时调用
   */
  static async demoteToSessionTab(workonaId) {
    try {
      const metadataResult = await this.getTabMetadata(workonaId);
      if (!metadataResult.success || !metadataResult.data) {
        return {
          success: false,
          error: {
            code: ERROR_CODES.TAB_ERROR,
            message: "Tab metadata not found for demotion"
          }
        };
      }
      const currentMetadata = metadataResult.data;
      if (!currentMetadata.isWorkspaceCore) {
        console.log(`⚠️ 标签页 ${workonaId} 已经是会话临时标签页，无需降级`);
        return { success: true, data: currentMetadata };
      }
      const updates = {
        isWorkspaceCore: false,
        tabType: "session",
        websiteId: void 0,
        // 移除与工作区网站的关联
        metadata: {
          ...currentMetadata.metadata,
          source: "user_opened",
          addedToWorkspaceAt: void 0,
          // 移除工作区添加时间
          demotedAt: Date.now(),
          // 记录降级时间
          originalWebsiteId: currentMetadata.websiteId
          // 保留原始网站ID用于追踪
        }
      };
      const result = await this.updateTabMetadata(workonaId, updates);
      if (result.success) {
        console.log(`⬇️ 标签页降级为会话临时标签页: ${workonaId} (原网站ID: ${currentMetadata.websiteId})`);
        try {
          const workspaceId = workonaId.split("-")[1];
          await UserTabsRealTimeMonitor.triggerImmediateStateCheck();
          await UserTabsRealTimeMonitor.forceRefreshWorkspaceState(workspaceId);
          console.log(`🔄 已触发工作区 ${workspaceId} 用户标签页状态更新 (标签页降级)`);
        } catch (updateError) {
          console.warn("触发用户标签页状态更新失败:", updateError);
        }
      }
      return result;
    } catch (error) {
      return {
        success: false,
        error: {
          code: ERROR_CODES.TAB_ERROR,
          message: "Failed to demote tab to session tab",
          details: error
        }
      };
    }
  }
  /**
   * 同步标签页编辑后的状态
   * 确保编辑标签页后不会破坏 Workona ID 映射关系
   */
  static async syncTabAfterEdit(chromeId, newUrl, newTitle) {
    try {
      const workonaIdResult = await this.getWorkonaIdByChromeId(chromeId);
      if (!workonaIdResult.success || !workonaIdResult.data) {
        return { success: true };
      }
      const workonaId = workonaIdResult.data;
      const metadataResult = await this.getTabMetadata(workonaId);
      if (!metadataResult.success || !metadataResult.data) {
        console.warn(`无法获取标签页元数据: ${workonaId}`);
        return { success: true };
      }
      const currentMetadata = metadataResult.data;
      if (newUrl && newUrl !== currentMetadata.metadata?.originalUrl) {
        const updates = {
          metadata: {
            ...currentMetadata.metadata,
            source: currentMetadata.metadata?.source || "user_opened",
            originalUrl: newUrl,
            // 记录编辑时间
            lastEditedAt: Date.now()
          }
        };
        const updateResult = await this.updateTabMetadata(workonaId, updates);
        if (updateResult.success) {
          console.log(`🔄 同步标签页编辑: ${workonaId} (新URL: ${newUrl})`);
        }
      }
      const currentSession = WorkspaceSessionManager.getCurrentSession();
      if (currentSession && currentSession.tabs[workonaId]) {
        const updatedTab = {
          ...currentSession.tabs[workonaId],
          url: newUrl || currentSession.tabs[workonaId].url,
          title: newTitle || currentSession.tabs[workonaId].title,
          lastUpdated: Date.now()
        };
        await WorkspaceSessionManager.updateSessionTab(workonaId, updatedTab);
        console.log(`📝 同步标签页到会话: ${workonaId}`);
      }
      return { success: true };
    } catch (error) {
      return {
        success: false,
        error: {
          code: ERROR_CODES.TAB_ERROR,
          message: "Failed to sync tab after edit",
          details: error
        }
      };
    }
  }
  /**
   * 检查标签页是否为工作区核心标签页
   */
  static async isWorkspaceCore(chromeId) {
    try {
      const workonaIdResult = await this.getWorkonaIdByChromeId(chromeId);
      if (!workonaIdResult.success || !workonaIdResult.data) {
        return { success: true, data: false };
      }
      const metadataResult = await this.getTabMetadata(workonaIdResult.data);
      if (!metadataResult.success || !metadataResult.data) {
        return { success: true, data: false };
      }
      return { success: true, data: metadataResult.data.isWorkspaceCore };
    } catch (error) {
      return {
        success: false,
        error: {
          code: ERROR_CODES.TAB_ERROR,
          message: "Failed to check if tab is workspace core",
          details: error
        }
      };
    }
  }
  /**
   * 获取工作区的所有核心标签页 Workona ID
   */
  static async getWorkspaceCoreTabIds(workspaceId) {
    try {
      const mappingsResult = await StorageManager.getTabIdMappings();
      if (!mappingsResult.success) {
        return { success: false, error: mappingsResult.error };
      }
      const mappings = mappingsResult.data;
      const coreTabIds = mappings.filter((m) => m.workspaceId === workspaceId && m.isWorkspaceCore).map((m) => m.workonaId);
      return { success: true, data: coreTabIds };
    } catch (error) {
      return {
        success: false,
        error: {
          code: ERROR_CODES.TAB_ERROR,
          message: "Failed to get workspace core tab IDs",
          details: error
        }
      };
    }
  }
  /**
   * 获取工作区的所有会话临时标签页 Workona ID
   */
  static async getWorkspaceSessionTabIds(workspaceId) {
    try {
      const mappingsResult = await StorageManager.getTabIdMappings();
      if (!mappingsResult.success) {
        return { success: false, error: mappingsResult.error };
      }
      const mappings = mappingsResult.data;
      const sessionTabIds = mappings.filter((m) => m.workspaceId === workspaceId && !m.isWorkspaceCore).map((m) => m.workonaId);
      return { success: true, data: sessionTabIds };
    } catch (error) {
      return {
        success: false,
        error: {
          code: ERROR_CODES.TAB_ERROR,
          message: "Failed to get workspace session tab IDs",
          details: error
        }
      };
    }
  }
  /**
   * 检查工作区网站是否有对应的打开标签页
   */
  static async isWebsiteTabOpen(workspaceId, websiteId) {
    try {
      const mappingsResult = await StorageManager.getTabIdMappings();
      if (!mappingsResult.success) {
        return { success: false, error: mappingsResult.error };
      }
      const mappings = mappingsResult.data;
      const mapping = mappings.find(
        (m) => m.workspaceId === workspaceId && m.websiteId === websiteId && m.isWorkspaceCore
        // 只检查工作区核心标签页
      );
      if (!mapping) {
        return {
          success: true,
          data: { isOpen: false }
        };
      }
      try {
        const tab = await chrome.tabs.get(mapping.chromeId);
        if (tab) {
          return {
            success: true,
            data: {
              isOpen: true,
              chromeId: mapping.chromeId,
              workonaId: mapping.workonaId
            }
          };
        }
      } catch {
        await this.removeTabMapping(mapping.workonaId);
        console.log(`🗑️ 清理无效的标签页映射: ${mapping.workonaId}`);
      }
      return {
        success: true,
        data: { isOpen: false }
      };
    } catch (error) {
      return {
        success: false,
        error: {
          code: ERROR_CODES.TAB_ERROR,
          message: "Failed to check if website tab is open",
          details: error
        }
      };
    }
  }
  /**
   * 获取工作区所有网站的标签页状态
   */
  static async getWorkspaceWebsiteTabStates(workspaceId, websiteIds) {
    try {
      const result = {};
      for (const websiteId of websiteIds) {
        const statusResult = await this.isWebsiteTabOpen(workspaceId, websiteId);
        if (statusResult.success) {
          result[websiteId] = statusResult.data;
        } else {
          result[websiteId] = { isOpen: false };
        }
      }
      return { success: true, data: result };
    } catch (error) {
      return {
        success: false,
        error: {
          code: ERROR_CODES.TAB_ERROR,
          message: "Failed to get workspace website tab states",
          details: error
        }
      };
    }
  }
}

const workonaTabManager = /*#__PURE__*/Object.freeze(/*#__PURE__*/Object.defineProperty({
  __proto__: null,
  WorkonaTabManager
}, Symbol.toStringTag, { value: 'Module' }));

class TabManager {
  /**
   * 获取所有标签页信息
   */
  static async getAllTabs() {
    try {
      const tabs = await chrome.tabs.query({});
      const tabInfos = tabs.map((tab) => ({
        id: tab.id,
        url: tab.url || "",
        title: tab.title || "",
        favicon: tab.favIconUrl || "",
        isPinned: tab.pinned,
        isActive: tab.active,
        windowId: tab.windowId,
        index: tab.index
      }));
      return { success: true, data: tabInfos };
    } catch (error) {
      return {
        success: false,
        error: {
          code: ERROR_CODES.TAB_ERROR,
          message: "Failed to get tabs",
          details: error
        }
      };
    }
  }
  /**
   * 获取当前活跃标签页
   */
  static async getActiveTab() {
    try {
      const tabs = await chrome.tabs.query({ active: true, currentWindow: true });
      if (tabs.length === 0) {
        return {
          success: false,
          error: {
            code: ERROR_CODES.TAB_ERROR,
            message: "No active tab found"
          }
        };
      }
      const tab = tabs[0];
      const tabInfo = {
        id: tab.id,
        url: tab.url || "",
        title: tab.title || "",
        favicon: tab.favIconUrl || "",
        isPinned: tab.pinned,
        isActive: tab.active,
        windowId: tab.windowId,
        index: tab.index
      };
      return { success: true, data: tabInfo };
    } catch (error) {
      return {
        success: false,
        error: {
          code: ERROR_CODES.TAB_ERROR,
          message: "Failed to get active tab",
          details: error
        }
      };
    }
  }
  /**
   * 通过Workona ID查找标签页
   */
  static async findTabByWorkonaId(workonaId) {
    try {
      const chromeIdResult = await WorkonaTabManager.getChromeIdByWorkonaId(workonaId);
      if (!chromeIdResult.success || !chromeIdResult.data) {
        return { success: true, data: null };
      }
      const chromeId = chromeIdResult.data;
      try {
        const tab = await chrome.tabs.get(chromeId);
        const tabInfo = {
          id: tab.id,
          url: tab.url || "",
          title: tab.title || "",
          favicon: tab.favIconUrl || "",
          isPinned: tab.pinned,
          isActive: tab.active,
          windowId: tab.windowId,
          index: tab.index
        };
        return { success: true, data: tabInfo };
      } catch (tabError) {
        await WorkonaTabManager.removeTabMapping(workonaId);
        return { success: true, data: null };
      }
    } catch (error) {
      return {
        success: false,
        error: {
          code: ERROR_CODES.TAB_ERROR,
          message: "Failed to find tab by Workona ID",
          details: error
        }
      };
    }
  }
  /**
   * 创建新标签页
   */
  static async createTab(url, pinned = false, active = true) {
    try {
      const tab = await chrome.tabs.create({
        url,
        pinned,
        active
      });
      const tabInfo = {
        id: tab.id,
        url: tab.url || url,
        title: tab.title || "",
        favicon: tab.favIconUrl || "",
        isPinned: tab.pinned || pinned,
        isActive: tab.active || active,
        windowId: tab.windowId,
        index: tab.index
      };
      return { success: true, data: tabInfo };
    } catch (error) {
      return {
        success: false,
        error: {
          code: ERROR_CODES.TAB_ERROR,
          message: "Failed to create tab",
          details: error
        }
      };
    }
  }
  /**
   * 激活指定标签页
   */
  static async activateTab(tabId) {
    try {
      await chrome.tabs.update(tabId, { active: true });
      return { success: true };
    } catch (error) {
      return {
        success: false,
        error: {
          code: ERROR_CODES.TAB_ERROR,
          message: "Failed to activate tab",
          details: error
        }
      };
    }
  }
  /**
   * 关闭指定标签页
   */
  static async closeTab(tabId) {
    try {
      await chrome.tabs.remove(tabId);
      return { success: true };
    } catch (error) {
      return {
        success: false,
        error: {
          code: ERROR_CODES.TAB_ERROR,
          message: "Failed to close tab",
          details: error
        }
      };
    }
  }
  /**
   * 批量关闭标签页
   */
  static async closeTabs(tabIds) {
    try {
      if (tabIds.length === 0) {
        return { success: true };
      }
      const validTabIds = tabIds.filter((id) => typeof id === "number" && id > 0);
      if (validTabIds.length === 0) {
        return { success: true };
      }
      await chrome.tabs.remove(validTabIds);
      return { success: true };
    } catch (error) {
      return {
        success: false,
        error: {
          code: ERROR_CODES.TAB_ERROR,
          message: "Failed to close tabs",
          details: error
        }
      };
    }
  }
  /**
   * 获取工作区真正管理的标签页（Workona 风格：完全基于 ID 映射）
   */
  static async getWorkspaceRelatedTabs(workspace) {
    try {
      const workonaTabIds = await WorkonaTabManager.getWorkspaceWorkonaTabIds(workspace.id);
      if (!workonaTabIds.success) {
        console.log(`❌ 获取工作区 Workona 标签页ID失败:`, workonaTabIds.error);
        return { success: true, data: [] };
      }
      const relatedTabs = [];
      const validWorkonaIds = [];
      for (const workonaId of workonaTabIds.data) {
        const chromeIdResult = await WorkonaTabManager.getChromeIdByWorkonaId(workonaId);
        if (chromeIdResult.success && chromeIdResult.data) {
          try {
            const tab = await chrome.tabs.get(chromeIdResult.data);
            if (tab) {
              relatedTabs.push({
                id: tab.id,
                url: tab.url,
                title: tab.title,
                favicon: tab.favIconUrl || "",
                isPinned: tab.pinned,
                isActive: tab.active,
                windowId: tab.windowId,
                index: tab.index
              });
              validWorkonaIds.push(workonaId);
              console.log(`✅ 找到 Workona 管理的标签页: ${tab.title} (${workonaId})`);
            }
          } catch (tabError) {
            await WorkonaTabManager.removeTabMapping(workonaId);
            console.log(`🗑️ 清理无效的 Workona ID 映射: ${workonaId}`);
          }
        } else {
          await WorkonaTabManager.removeTabMapping(workonaId);
          console.log(`🗑️ 清理无效的 Workona ID 映射: ${workonaId}`);
        }
      }
      return { success: true, data: relatedTabs };
    } catch (error) {
      console.error("获取工作区相关标签页失败:", error);
      return {
        success: false,
        error: {
          code: ERROR_CODES.TAB_ERROR,
          message: "Failed to get workspace related tabs",
          details: error
        }
      };
    }
  }
  /**
   * 获取非工作区相关的标签页（仅在当前窗口中查找）
   */
  static async getNonWorkspaceRelatedTabs(workspace) {
    try {
      console.log(`查找非工作区 "${workspace.name}" 相关的标签页`);
      const tabs = await chrome.tabs.query({ currentWindow: true });
      const currentTabs = tabs.map((tab) => ({
        id: tab.id,
        url: tab.url || "",
        title: tab.title || "",
        favicon: tab.favIconUrl || "",
        isPinned: tab.pinned,
        isActive: tab.active,
        windowId: tab.windowId,
        index: tab.index
      }));
      const workspaceUrls = workspace.websites.map((w) => w.url);
      const nonRelatedTabs = currentTabs.filter(
        (tab) => !workspaceUrls.some((url) => tab.url.startsWith(url))
      );
      return { success: true, data: nonRelatedTabs };
    } catch (error) {
      return {
        success: false,
        error: {
          code: ERROR_CODES.TAB_ERROR,
          message: "Failed to get non-workspace related tabs",
          details: error
        }
      };
    }
  }
  /**
   * 检查标签页是否为用户手动打开的（非工作区配置的标签页）
   */
  static async isUserOpenedTab(tabId) {
    try {
      const workonaIdResult = await WorkonaTabManager.getWorkonaIdByChromeId(tabId);
      if (!workonaIdResult.success || !workonaIdResult.data) {
        return true;
      }
      const metadataResult = await WorkonaTabManager.getTabMetadata(workonaIdResult.data);
      if (!metadataResult.success || !metadataResult.data) {
        return true;
      }
      const { metadata } = metadataResult.data;
      return metadata?.source === "user_opened";
    } catch {
      return true;
    }
  }
}

class TabClassificationService {
  /**
   * 获取当前应该用于新标签页的工作区
   */
  static async getCurrentWorkspaceForNewTab() {
    try {
      const activeIdResult = await StorageManager.getActiveWorkspaceId();
      if (activeIdResult.success && activeIdResult.data) {
        const workspacesResult = await StorageManager.getWorkspaces();
        if (workspacesResult.success && workspacesResult.data) {
          const workspace = workspacesResult.data.find((w) => w.id === activeIdResult.data);
          if (workspace) {
            console.log(`🎯 [getCurrentWorkspaceForNewTab] 通过存储的活跃工作区ID检测到当前工作区: ${workspace.name}`);
            return workspace;
          }
        }
      }
      const currentTabsResult = await this.getCurrentWindowTabs();
      if (!currentTabsResult.success) {
        console.log(`❌ [getCurrentWorkspaceForNewTab] 获取当前窗口标签页失败:`, currentTabsResult.error);
        return null;
      }
      const currentTabs = currentTabsResult.data;
      for (const tab of currentTabs) {
        const workonaIdResult = await WorkonaTabManager.getWorkonaIdByChromeId(tab.id);
        if (workonaIdResult.success && workonaIdResult.data) {
          const workspaceId = workonaIdResult.data.split("-")[1];
          const workspacesResult = await StorageManager.getWorkspaces();
          if (workspacesResult.success && workspacesResult.data) {
            const workspace = workspacesResult.data.find((w) => w.id === workspaceId);
            if (workspace) {
              console.log(`🎯 [getCurrentWorkspaceForNewTab] 通过窗口中其他标签页检测到当前工作区: ${workspace.name}`);
              return workspace;
            }
          }
        }
      }
      try {
        const { WorkspaceSwitcher } = await __vitePreload(async () => { const { WorkspaceSwitcher } = await Promise.resolve().then(() => workspaceSwitcher);return { WorkspaceSwitcher }},true?void 0:void 0);
        const activeWorkspaceResult = await WorkspaceSwitcher.detectActiveWorkspace();
        if (activeWorkspaceResult.success && activeWorkspaceResult.data) {
          const workspace = activeWorkspaceResult.data;
          console.log(`🎯 [getCurrentWorkspaceForNewTab] 通过 WorkspaceSwitcher 检测到当前工作区: ${workspace.name}`);
          return workspace;
        }
      } catch (error) {
        console.warn(`⚠️ [getCurrentWorkspaceForNewTab] 策略3失败:`, error);
      }
      try {
        const workspacesResult = await StorageManager.getWorkspaces();
        if (workspacesResult.success && workspacesResult.data && workspacesResult.data.length > 0) {
          const firstWorkspace = workspacesResult.data[0];
          console.log(`🎯 [getCurrentWorkspaceForNewTab] 使用默认工作区: ${firstWorkspace.name}`);
          return firstWorkspace;
        }
      } catch (error) {
        console.warn(`⚠️ [getCurrentWorkspaceForNewTab] 策略4失败:`, error);
      }
      return null;
    } catch (error) {
      console.error("❌ [getCurrentWorkspaceForNewTab] 获取当前工作区失败:", error);
      return null;
    }
  }
  /**
   * 获取当前窗口的标签页
   */
  static async getCurrentWindowTabs() {
    try {
      const tabs = await chrome.tabs.query({ currentWindow: true });
      const tabInfos = tabs.map((tab) => ({
        id: tab.id,
        url: tab.url || "",
        title: tab.title || "",
        favicon: tab.favIconUrl || "",
        isPinned: tab.pinned,
        isActive: tab.active,
        windowId: tab.windowId,
        index: tab.index
      }));
      return { success: true, data: tabInfos };
    } catch (error) {
      return {
        success: false,
        error: {
          code: ERROR_CODES.TAB_ERROR,
          message: "Failed to get current window tabs",
          details: error
        }
      };
    }
  }
  /**
   * 自动为新标签页创建会话临时 Workona ID
   *
   * 🎯 核心功能：
   * 1. 为用户新打开的标签页自动分配 Workona ID
   * 2. 根据当前工作区状态决定标签页归属
   * 3. 支持智能归属机制，处理无工作区状态的情况
   *
   * 📋 处理流程：
   * 1. 过滤系统页面（chrome://、extension://等）
   * 2. 检查标签页是否已有 Workona ID（避免重复处理）
   * 3. 获取当前活跃工作区
   * 4. 根据工作区状态创建相应的 Workona ID 映射
   * 5. 触发用户标签页状态更新
   *
   * @param tabId Chrome 标签页 ID
   * @param url 标签页 URL
   * @returns 操作结果
   */
  static async autoClassifyNewTab(tabId, url) {
    try {
      console.log(`🎯 [autoClassifyNewTab] 开始自动分类标签页: ID=${tabId}, URL=${url}`);
      const { TabClassificationUtils } = await __vitePreload(async () => { const { TabClassificationUtils } = await Promise.resolve().then(() => workspaceSwitcher);return { TabClassificationUtils }},true?void 0:void 0);
      if (TabClassificationUtils.isSystemTab(url)) {
        console.log(`🚫 [autoClassifyNewTab] 跳过系统页面: ${url}`);
        return { success: true };
      }
      const existingResult = await WorkonaTabManager.getWorkonaIdByChromeId(tabId);
      if (existingResult.success && existingResult.data) {
        console.log(`ℹ️ [autoClassifyNewTab] 标签页已有 Workona ID: ${existingResult.data}，跳过分类`);
        return { success: true };
      }
      const activeWorkspace = await this.getCurrentWorkspaceForNewTab();
      if (!activeWorkspace) {
        console.warn(`⚠️ [autoClassifyNewTab] 无当前工作区，为标签页创建智能归属映射: ${url}`);
        const pendingWorkspaceId = "pending-assignment";
        const workonaId2 = WorkonaTabManager.generateWorkonaTabId(pendingWorkspaceId);
        console.log(`🆔 为等待归属的用户标签页生成 Workona ID: ${workonaId2}`);
        const mappingResult2 = await WorkonaTabManager.createTabIdMapping(
          workonaId2,
          tabId,
          pendingWorkspaceId,
          void 0,
          // 无对应的网站配置ID
          {
            isWorkspaceCore: false,
            // 标记为等待归属的标签页（非工作区核心标签页）
            source: "user_opened"
            // 标记为用户主动打开的标签页
          }
        );
        if (mappingResult2.success) {
          console.log(`✅ [autoClassifyNewTab] 成功为等待归属标签页创建映射: ${url} -> ${workonaId2}`);
        } else {
          console.error(`❌ [autoClassifyNewTab] 创建等待归属映射失败:`, mappingResult2.error);
        }
        return { success: mappingResult2.success, error: mappingResult2.error };
      }
      console.log(`✅ 找到活跃工作区: ${activeWorkspace.name} (ID: ${activeWorkspace.id})`);
      const workonaId = WorkonaTabManager.generateWorkonaTabId(activeWorkspace.id);
      console.log(`🆔 生成 Workona ID: ${workonaId}`);
      const mappingResult = await WorkonaTabManager.createTabIdMapping(
        workonaId,
        tabId,
        activeWorkspace.id,
        void 0,
        // 无对应的网站配置ID（用户临时打开的标签页）
        {
          isWorkspaceCore: false,
          // 标记为会话临时标签页（非工作区核心标签页）
          source: "user_opened"
          // 标记为用户主动打开的标签页
        }
      );
      if (mappingResult.success) {
        console.log(`✨ [autoClassifyNewTab] 自动为用户标签页创建会话临时 Workona ID: ${workonaId} (${url})`);
        const verifyResult = await WorkonaTabManager.getWorkonaIdByChromeId(tabId);
        if (verifyResult.success && verifyResult.data) {
          console.log(`✅ [autoClassifyNewTab] 映射验证成功: ${verifyResult.data}`);
          try {
            console.log(`🔄 触发用户标签页状态更新 (工作区: ${activeWorkspace.id}, 新标签页分类)`);
            const { UserTabsRealTimeMonitor } = await __vitePreload(async () => { const { UserTabsRealTimeMonitor } = await Promise.resolve().then(() => UserTabsRealTimeMonitor$1);return { UserTabsRealTimeMonitor }},true?void 0:void 0);
            await UserTabsRealTimeMonitor.triggerImmediateStateCheck();
            await UserTabsRealTimeMonitor.forceRefreshWorkspaceState(activeWorkspace.id);
            console.log(`✅ 用户标签页状态更新完成 (工作区: ${activeWorkspace.id})`);
          } catch (updateError) {
            console.warn("触发用户标签页状态更新失败:", updateError);
          }
        } else {
          console.error(`❌ [autoClassifyNewTab] 映射验证失败:`, verifyResult);
        }
      } else {
        console.error(`❌ [autoClassifyNewTab] 创建 Workona ID 映射失败:`, mappingResult.error);
      }
      return { success: mappingResult.success, error: mappingResult.error };
    } catch (error) {
      return {
        success: false,
        error: {
          code: ERROR_CODES.TAB_ERROR,
          message: "Failed to auto-classify new tab",
          details: error
        }
      };
    }
  }
}

const TabClassificationService$1 = /*#__PURE__*/Object.freeze(/*#__PURE__*/Object.defineProperty({
  __proto__: null,
  TabClassificationService
}, Symbol.toStringTag, { value: 'Module' }));

class UserTabsRealTimeMonitor {
  static isMonitoring = false;
  static monitoringInterval = null;
  static lastStateSnapshot = /* @__PURE__ */ new Map();
  static MONITOR_INTERVAL = 2e3;
  // 优化为2秒检查间隔，减少日志洪水
  static pendingUpdate = false;
  // 防止重复更新
  /**
   * 启动实时监控
   */
  static startMonitoring() {
    if (this.isMonitoring) {
      return;
    }
    console.log("🚀 启动用户标签页实时监控");
    this.isMonitoring = true;
    this.checkUserTabsStateChanges();
    this.monitoringInterval = setInterval(() => {
      this.checkUserTabsStateChanges();
    }, this.MONITOR_INTERVAL);
  }
  /**
   * 停止实时监控
   */
  static stopMonitoring() {
    if (!this.isMonitoring) {
      return;
    }
    console.log("⏹️ 停止用户标签页实时监控");
    this.isMonitoring = false;
    if (this.monitoringInterval) {
      clearInterval(this.monitoringInterval);
      this.monitoringInterval = null;
    }
    this.lastStateSnapshot.clear();
  }
  /**
   * 检查用户标签页状态变化（优化版：防重复更新）
   */
  static async checkUserTabsStateChanges() {
    if (this.pendingUpdate) {
      return;
    }
    try {
      this.pendingUpdate = true;
      const activeWorkspace = await this.getCurrentActiveWorkspace();
      if (!activeWorkspace) {
        return;
      }
      const workspaceId = activeWorkspace.id;
      const { WorkspaceUserTabsVisibilityManager } = await __vitePreload(async () => { const { WorkspaceUserTabsVisibilityManager } = await Promise.resolve().then(() => WorkspaceUserTabsVisibilityManager$1);return { WorkspaceUserTabsVisibilityManager }},true?void 0:void 0);
      const currentState = await WorkspaceUserTabsVisibilityManager.getWorkspaceUserTabsState(workspaceId);
      if (!currentState.success) {
        return;
      }
      const stateData = currentState.data;
      const stateKey = `workspace_${workspaceId}`;
      const lastState = this.lastStateSnapshot.get(stateKey);
      const currentSnapshot = {
        isHidden: stateData.isHidden,
        hiddenTabsCount: stateData.hiddenTabIds.length,
        totalUserTabs: stateData.totalUserTabs,
        visibleUserTabs: stateData.visibleUserTabs,
        actionType: stateData.actionType,
        timestamp: Date.now()
      };
      if (lastState && this.hasStateChanged(lastState, currentSnapshot)) {
        console.log(`📊 检测到工作区 "${activeWorkspace.name}" 用户标签页状态变化:`, {
          前: lastState,
          后: currentSnapshot
        });
        await this.notifyStateChange(workspaceId, currentSnapshot);
      }
      this.lastStateSnapshot.set(stateKey, currentSnapshot);
    } catch (error) {
      console.error("检查用户标签页状态变化失败:", error);
    } finally {
      this.pendingUpdate = false;
    }
  }
  /**
   * 获取当前活跃工作区（多策略检测）
   */
  static async getCurrentActiveWorkspace() {
    try {
      const activeIdResult = await StorageManager.getActiveWorkspaceId();
      if (activeIdResult.success && activeIdResult.data) {
        const workspacesResult = await StorageManager.getWorkspaces();
        if (workspacesResult.success && workspacesResult.data) {
          const workspace = workspacesResult.data.find((w) => w.id === activeIdResult.data);
          if (workspace) {
            return workspace;
          }
        }
      }
      try {
        const { WorkspaceSwitcher } = await __vitePreload(async () => { const { WorkspaceSwitcher } = await Promise.resolve().then(() => workspaceSwitcher);return { WorkspaceSwitcher }},true?void 0:void 0);
        const activeWorkspaceResult = await WorkspaceSwitcher.detectActiveWorkspace();
        if (activeWorkspaceResult.success && activeWorkspaceResult.data) {
          return activeWorkspaceResult.data;
        }
      } catch (error) {
        console.warn("通过WorkspaceSwitcher检测活跃工作区失败:", error);
      }
      return null;
    } catch (error) {
      console.error("获取当前活跃工作区失败:", error);
      return null;
    }
  }
  /**
   * 检查状态是否发生变化
   */
  static hasStateChanged(lastState, currentState) {
    return lastState.isHidden !== currentState.isHidden || lastState.hiddenTabsCount !== currentState.hiddenTabsCount || lastState.totalUserTabs !== currentState.totalUserTabs || lastState.visibleUserTabs !== currentState.visibleUserTabs || lastState.actionType !== currentState.actionType;
  }
  /**
   * 通知状态变化
   */
  static async notifyStateChange(workspaceId, _newState) {
    try {
      chrome.runtime.sendMessage({
        type: "USER_TABS_VISIBILITY_CHANGED",
        workspaceId,
        timestamp: Date.now()
      }).catch(() => {
      });
    } catch (error) {
      console.warn("通知状态变化失败:", error);
    }
  }
  /**
   * 强制刷新指定工作区状态
   */
  static async forceRefreshWorkspaceState(workspaceId) {
    try {
      console.log(`🔄 强制刷新工作区状态: ${workspaceId}`);
      const stateKey = `workspace_${workspaceId}`;
      this.lastStateSnapshot.delete(stateKey);
      if (this.isMonitoring) {
        await this.checkUserTabsStateChanges();
      }
      console.log(`✅ 工作区状态刷新完成: ${workspaceId}`);
    } catch (error) {
      console.error(`❌ 强制刷新工作区状态失败 (${workspaceId}):`, error);
    }
  }
  /**
   * 立即触发状态检查
   */
  static async triggerImmediateStateCheck() {
    if (!this.isMonitoring) {
      console.log("⚠️ 监控未启动，启动监控并执行状态检查");
      this.startMonitoring();
      return;
    }
    console.log("🔄 立即触发用户标签页状态检查");
    await this.checkUserTabsStateChanges();
  }
  /**
   * 获取监控状态
   */
  static getMonitoringStatus() {
    return {
      isMonitoring: this.isMonitoring,
      workspaceCount: this.lastStateSnapshot.size
    };
  }
}

const UserTabsRealTimeMonitor$1 = /*#__PURE__*/Object.freeze(/*#__PURE__*/Object.defineProperty({
  __proto__: null,
  UserTabsRealTimeMonitor
}, Symbol.toStringTag, { value: 'Module' }));

class WorkspaceUserTabsVisibilityManager {
  /**
   * 获取工作区用户标签页状态
   */
  static async getWorkspaceUserTabsState(workspaceId) {
    try {
      const workspaceResult = await StorageManager.getWorkspace(workspaceId);
      if (!workspaceResult.success) {
        return { success: false, error: workspaceResult.error };
      }
      const { TabClassificationService } = await __vitePreload(async () => { const { TabClassificationService } = await Promise.resolve().then(() => TabClassificationService$1);return { TabClassificationService }},true?void 0:void 0);
      const currentTabsResult = await TabClassificationService.getCurrentWindowTabs();
      if (!currentTabsResult.success) {
        return { success: false, error: currentTabsResult.error };
      }
      const allTabs = currentTabsResult.data;
      const userTabs = [];
      const hiddenTabIds = [];
      const pinnedTabIds = [];
      for (const tab of allTabs) {
        if (TabClassificationUtils$1.isSystemTab(tab.url)) {
          continue;
        }
        const workonaIdResult = await WorkonaTabManager.getWorkonaIdByChromeId(tab.id);
        if (workonaIdResult.success && workonaIdResult.data) {
          const tabWorkspaceId = workonaIdResult.data.split("-")[1];
          if (tabWorkspaceId === workspaceId) {
            const metadataResult = await WorkonaTabManager.getTabMetadata(workonaIdResult.data);
            if (metadataResult.success && metadataResult.data) {
              const { isWorkspaceCore } = metadataResult.data;
              if (!isWorkspaceCore) {
                userTabs.push(tab);
                if (!tab.isActive && tab.windowId !== (await chrome.windows.getCurrent()).id) {
                  hiddenTabIds.push(tab.id);
                }
                if (tab.isPinned) {
                  pinnedTabIds.push(tab.id);
                }
              }
            }
          }
        } else {
          userTabs.push(tab);
          if (!tab.isActive && tab.windowId !== (await chrome.windows.getCurrent()).id) {
            hiddenTabIds.push(tab.id);
          }
          if (tab.isPinned) {
            pinnedTabIds.push(tab.id);
          }
        }
      }
      const totalUserTabs = userTabs.length;
      const visibleUserTabs = totalUserTabs - hiddenTabIds.length;
      const isHidden = hiddenTabIds.length > 0;
      const canContinueHiding = visibleUserTabs > 0;
      let actionType;
      if (isHidden && canContinueHiding) {
        actionType = "continue_hide";
      } else if (isHidden) {
        actionType = "show";
      } else {
        actionType = "hide";
      }
      return {
        success: true,
        data: {
          isHidden,
          hiddenTabIds,
          pinnedTabIds,
          totalUserTabs,
          visibleUserTabs,
          canContinueHiding,
          actionType
        }
      };
    } catch (error) {
      return {
        success: false,
        error: {
          code: ERROR_CODES.TAB_ERROR,
          message: "Failed to get workspace user tabs state",
          details: error
        }
      };
    }
  }
  /**
   * 切换工作区用户标签页可见性
   */
  static async toggleWorkspaceUserTabsVisibility(workspaceId) {
    try {
      const stateResult = await this.getWorkspaceUserTabsState(workspaceId);
      if (!stateResult.success) {
        return { success: false, error: stateResult.error };
      }
      const state = stateResult.data;
      if (state.actionType === "show") {
        return await this.showWorkspaceUserTabs(workspaceId);
      } else if (state.actionType === "continue_hide") {
        return await this.continueHideWorkspaceUserTabs(workspaceId);
      } else {
        return await this.hideWorkspaceUserTabs(workspaceId);
      }
    } catch (error) {
      return {
        success: false,
        error: {
          code: ERROR_CODES.TAB_ERROR,
          message: "Failed to toggle workspace user tabs visibility",
          details: error
        }
      };
    }
  }
  /**
   * 隐藏工作区用户标签页
   */
  static async hideWorkspaceUserTabs(workspaceId) {
    try {
      const stateResult = await this.getWorkspaceUserTabsState(workspaceId);
      if (!stateResult.success) {
        return { success: false, error: stateResult.error };
      }
      const state = stateResult.data;
      const { TabClassificationService } = await __vitePreload(async () => { const { TabClassificationService } = await Promise.resolve().then(() => TabClassificationService$1);return { TabClassificationService }},true?void 0:void 0);
      const currentTabsResult = await TabClassificationService.getCurrentWindowTabs();
      if (!currentTabsResult.success) {
        return { success: false, error: currentTabsResult.error };
      }
      const allTabs = currentTabsResult.data;
      const tabsToHide = [];
      for (const tab of allTabs) {
        if (TabClassificationUtils$1.isSystemTab(tab.url)) {
          continue;
        }
        if (tab.isActive) {
          continue;
        }
        const workonaIdResult = await WorkonaTabManager.getWorkonaIdByChromeId(tab.id);
        if (workonaIdResult.success && workonaIdResult.data) {
          const tabWorkspaceId = workonaIdResult.data.split("-")[1];
          if (tabWorkspaceId === workspaceId) {
            const metadataResult = await WorkonaTabManager.getTabMetadata(workonaIdResult.data);
            if (metadataResult.success && metadataResult.data) {
              const { isWorkspaceCore } = metadataResult.data;
              if (!isWorkspaceCore) {
                tabsToHide.push(tab.id);
              }
            }
          }
        } else {
          tabsToHide.push(tab.id);
        }
      }
      if (tabsToHide.length > 0) {
        const backgroundWindow = await chrome.windows.create({
          url: "about:blank",
          focused: false,
          state: "minimized"
        });
        await chrome.tabs.move(tabsToHide, {
          windowId: backgroundWindow.id,
          index: -1
        });
      }
      return {
        success: true,
        data: {
          action: "hidden",
          affectedTabsCount: tabsToHide.length,
          totalUserTabs: state.totalUserTabs
        }
      };
    } catch (error) {
      return {
        success: false,
        error: {
          code: ERROR_CODES.TAB_ERROR,
          message: "Failed to hide workspace user tabs",
          details: error
        }
      };
    }
  }
  /**
   * 继续隐藏工作区用户标签页
   */
  static async continueHideWorkspaceUserTabs(workspaceId) {
    try {
      const hideResult = await this.hideWorkspaceUserTabs(workspaceId);
      if (!hideResult.success) {
        return { success: false, error: hideResult.error };
      }
      return {
        success: true,
        data: {
          action: "continue_hidden",
          affectedTabsCount: hideResult.data?.affectedTabsCount || 0,
          totalUserTabs: hideResult.data?.totalUserTabs || 0
        }
      };
    } catch (error) {
      return {
        success: false,
        error: {
          code: ERROR_CODES.TAB_ERROR,
          message: "Failed to continue hide workspace user tabs",
          details: error
        }
      };
    }
  }
  /**
   * 显示工作区用户标签页
   */
  static async showWorkspaceUserTabs(workspaceId) {
    try {
      const allWindows = await chrome.windows.getAll({ populate: true });
      const currentWindow = await chrome.windows.getCurrent();
      const tabsToShow = [];
      for (const window of allWindows) {
        if (window.id === currentWindow.id || !window.tabs) {
          continue;
        }
        for (const tab of window.tabs) {
          if (!tab.id || !tab.url) continue;
          if (TabClassificationUtils$1.isSystemTab(tab.url)) {
            continue;
          }
          const workonaIdResult = await WorkonaTabManager.getWorkonaIdByChromeId(tab.id);
          if (workonaIdResult.success && workonaIdResult.data) {
            const tabWorkspaceId = workonaIdResult.data.split("-")[1];
            if (tabWorkspaceId === workspaceId) {
              const metadataResult = await WorkonaTabManager.getTabMetadata(workonaIdResult.data);
              if (metadataResult.success && metadataResult.data) {
                const { isWorkspaceCore } = metadataResult.data;
                if (!isWorkspaceCore) {
                  tabsToShow.push(tab.id);
                }
              }
            }
          }
        }
      }
      if (tabsToShow.length > 0) {
        await chrome.tabs.move(tabsToShow, {
          windowId: currentWindow.id,
          index: -1
        });
      }
      const stateResult = await this.getWorkspaceUserTabsState(workspaceId);
      const totalUserTabs = stateResult.success ? stateResult.data.totalUserTabs : 0;
      return {
        success: true,
        data: {
          action: "shown",
          affectedTabsCount: tabsToShow.length,
          totalUserTabs
        }
      };
    } catch (error) {
      return {
        success: false,
        error: {
          code: ERROR_CODES.TAB_ERROR,
          message: "Failed to show workspace user tabs",
          details: error
        }
      };
    }
  }
  /**
   * 设置工作区用户标签页状态（兼容性方法）
   * @deprecated 此方法主要用于向后兼容，建议使用具体的隐藏/显示方法
   */
  static async setWorkspaceUserTabsState(workspaceId, isHidden, _hiddenTabIds, _pinnedTabIds) {
    try {
      if (isHidden) {
        const hideResult = await this.hideWorkspaceUserTabs(workspaceId);
        return { success: hideResult.success, error: hideResult.error };
      } else {
        const showResult = await this.showWorkspaceUserTabs(workspaceId);
        return { success: showResult.success, error: showResult.error };
      }
    } catch (error) {
      return {
        success: false,
        error: {
          code: ERROR_CODES.TAB_ERROR,
          message: "Failed to set workspace user tabs state",
          details: error
        }
      };
    }
  }
}

const WorkspaceUserTabsVisibilityManager$1 = /*#__PURE__*/Object.freeze(/*#__PURE__*/Object.defineProperty({
  __proto__: null,
  WorkspaceUserTabsVisibilityManager
}, Symbol.toStringTag, { value: 'Module' }));

class WorkspaceTabContentMatcher {
  /**
   * 检查标签页是否匹配工作区内容
   */
  static async matchTabToWorkspace(tab, workspace) {
    const urlMatch = workspace.websites.some(
      (website) => tab.url.startsWith(website.url) || website.url.startsWith(tab.url)
    );
    if (urlMatch) {
      return {
        isMatch: true,
        confidence: 0.9,
        matchType: "prefix"
      };
    }
    return {
      isMatch: false,
      confidence: 0,
      matchType: "none"
    };
  }
  /**
   * 检查标签页是否为工作区标签页
   */
  static async isWorkspaceTab(tab) {
    if (TabClassificationUtils$1.isSystemTab(tab.url)) {
      return { isMatch: false };
    }
    return {
      isMatch: true,
      confidence: 0.5
      // 低置信度，因为我们没有具体的工作区匹配逻辑
    };
  }
}
class UserTabsUtils {
  /**
   * 检查标签页是否为系统标签页
   */
  static isSystemTab(url) {
    const systemPrefixes = [
      "chrome://",
      "chrome-extension://",
      "edge://",
      "about:",
      "moz-extension://",
      "safari-extension://"
    ];
    return systemPrefixes.some((prefix) => url.startsWith(prefix));
  }
  /**
   * 检查标签页是否为扩展内部页面
   */
  static isExtensionTab(url) {
    return url.includes("workspace-placeholder.html") || url.startsWith("chrome-extension://") || url === "chrome://newtab/";
  }
}
let TabClassificationUtils$1 = class TabClassificationUtils {
  /**
   * 使用新的3分类系统判断系统页面
   */
  static isSystemTab(url) {
    return UserTabsUtils.isSystemTab(url) || UserTabsUtils.isExtensionTab(url);
  }
  /**
   * 检查标签页是否为用户标签页
   */
  static isUserTab(url) {
    return !this.isSystemTab(url);
  }
  /**
   * 获取标签页分类
   */
  static getTabCategory(url) {
    if (UserTabsUtils.isSystemTab(url)) {
      return "system";
    }
    if (UserTabsUtils.isExtensionTab(url)) {
      return "extension";
    }
    return "user";
  }
};

const tabs = /*#__PURE__*/Object.freeze(/*#__PURE__*/Object.defineProperty({
  __proto__: null,
  TabClassificationService,
  TabClassificationUtils: TabClassificationUtils$1,
  TabManager,
  UserTabsRealTimeMonitor,
  UserTabsUtils,
  WorkspaceTabContentMatcher,
  WorkspaceUserTabsVisibilityManager
}, Symbol.toStringTag, { value: 'Module' }));

class TabClassificationUtils {
  /**
   * 判断是否为系统标签页
   */
  static isSystemTab(url) {
    return url.includes("chrome://") || url.includes("chrome-extension://") || url.includes("about:") || url.includes("edge://") || url.includes("workspace-placeholder.html") || url === "chrome://newtab/" || url === "about:blank" || url === "";
  }
  /**
   * 检查是否为工作区专属标签页（具有Workona ID映射）
   */
  static async isWorkspaceSpecificTab(tabId) {
    try {
      const workonaIdResult = await WorkonaTabManager.getWorkonaIdByChromeId(tabId);
      return workonaIdResult.success && !!workonaIdResult.data;
    } catch {
      return false;
    }
  }
  /**
   * 检查是否为用户标签页（非系统且非工作区专属）
   */
  static async isUserTab(tab) {
    if (!tab.url || this.isSystemTab(tab.url)) {
      return false;
    }
    const isWorkspaceSpecific = await this.isWorkspaceSpecificTab(tab.id);
    return !isWorkspaceSpecific;
  }
  /**
   * 批量分类标签页
   */
  static async classifyTabs(tabs) {
    const systemTabs = [];
    const workspaceTabs = [];
    const userTabs = [];
    for (const tab of tabs) {
      if (!tab.url || !tab.id) continue;
      if (this.isSystemTab(tab.url)) {
        systemTabs.push(tab);
      } else {
        const isWorkspaceSpecific = await this.isWorkspaceSpecificTab(tab.id);
        if (isWorkspaceSpecific) {
          workspaceTabs.push(tab);
        } else {
          userTabs.push(tab);
        }
      }
    }
    return { systemTabs, workspaceTabs, userTabs };
  }
  /**
   * 获取标签页的分类类型
   */
  static async getTabCategory(tab) {
    if (!tab.url || this.isSystemTab(tab.url)) {
      return "system";
    }
    const isWorkspaceSpecific = await this.isWorkspaceSpecificTab(tab.id);
    return isWorkspaceSpecific ? "workspace" : "user";
  }
  /**
   * 检查标签页是否可以被移动
   */
  static canMoveTab(tab) {
    if (!tab.url) return false;
    if (this.isSystemTab(tab.url)) {
      return false;
    }
    const unmovableUrls = [
      "chrome://newtab/",
      "about:blank",
      "chrome://extensions/",
      "chrome://settings/"
    ];
    return !unmovableUrls.some((url) => tab.url.startsWith(url));
  }
  /**
   * 过滤出可移动的标签页
   */
  static filterMovableTabs(tabs) {
    return tabs.filter((tab) => this.canMoveTab(tab));
  }
  /**
   * 检查标签页是否为工作区占位符页面
   */
  static isWorkspacePlaceholder(url) {
    return url.includes("workspace-placeholder.html");
  }
  /**
   * 检查标签页是否为新标签页
   */
  static isNewTab(url) {
    return url === "chrome://newtab/" || url === "about:blank" || url === "";
  }
}

class WorkspaceSwitchCore {
  static isBackgroundSetupInProgress = false;
  // 后台设置进行中标志
  static currentSetupWorkspaceId = null;
  // 当前正在设置的工作区ID
  /**
   * 检查是否有后台设置正在进行
   */
  static isSetupInProgress() {
    return this.isBackgroundSetupInProgress;
  }
  /**
   * 获取当前正在设置的工作区ID
   */
  static getCurrentSetupWorkspaceId() {
    return this.currentSetupWorkspaceId;
  }
  /**
   * 设置后台设置状态
   */
  static setBackgroundSetupStatus(workspaceId, inProgress) {
    this.isBackgroundSetupInProgress = inProgress;
    this.currentSetupWorkspaceId = workspaceId;
    if (inProgress && workspaceId) {
      console.log(`🔧 开始后台设置工作区: ${workspaceId}`);
    } else {
      console.log(`✅ 后台设置完成`);
    }
  }
  /**
   * 获取当前活跃工作区
   */
  static async getCurrentWorkspace() {
    try {
      const activeIdResult = await StorageManager.getActiveWorkspaceId();
      if (activeIdResult.success && activeIdResult.data) {
        const workspaceResult = await StorageManager.getWorkspace(activeIdResult.data);
        if (workspaceResult.success) {
          return { success: true, data: workspaceResult.data };
        }
      }
      return { success: true, data: null };
    } catch (error) {
      return {
        success: false,
        error: {
          code: ERROR_CODES.STORAGE_ERROR,
          message: "Failed to get current workspace",
          details: error
        }
      };
    }
  }
  /**
   * 设置当前活跃工作区
   */
  static async setCurrentWorkspace(workspaceId) {
    try {
      const result = await StorageManager.setActiveWorkspaceId(workspaceId);
      if (result.success) {
        console.log(`📌 设置活跃工作区: ${workspaceId || "none"}`);
      }
      return result;
    } catch (error) {
      return {
        success: false,
        error: {
          code: ERROR_CODES.STORAGE_ERROR,
          message: "Failed to set current workspace",
          details: error
        }
      };
    }
  }
  /**
   * 准备工作区切换选项
   */
  static async prepareSwitchOptions(options = {}) {
    try {
      const settingsResult = await StorageManager.getSettings();
      if (!settingsResult.success) {
        return { success: false, error: settingsResult.error };
      }
      const settings = settingsResult.data;
      const switchOptions = {
        closeOtherTabs: options.closeOtherTabs ?? settings.autoCloseOtherTabs,
        preserveUserOpenedTabs: options.preserveUserOpenedTabs ?? settings.preserveUserOpenedTabs,
        focusFirstTab: options.focusFirstTab ?? false,
        // 默认不自动聚焦到第一个标签页
        openInNewWindow: options.openInNewWindow ?? false,
        skipAnimation: options.skipAnimation ?? false
      };
      console.log(`⚙️ 切换选项:`, switchOptions);
      return { success: true, data: switchOptions };
    } catch (error) {
      return {
        success: false,
        error: {
          code: ERROR_CODES.STORAGE_ERROR,
          message: "Failed to prepare switch options",
          details: error
        }
      };
    }
  }
  /**
   * 验证工作区切换请求
   */
  static async validateSwitchRequest(workspaceId) {
    try {
      if (this.isBackgroundSetupInProgress) {
        const currentSetupId = this.currentSetupWorkspaceId;
        console.warn(`⚠️ 后台工作区设置正在进行中 (${currentSetupId})，拒绝切换到 ${workspaceId}`);
        return {
          success: false,
          error: {
            code: ERROR_CODES.TAB_ERROR,
            message: "Background workspace setup in progress",
            details: `Cannot switch to ${workspaceId} while ${currentSetupId} is being set up`
          }
        };
      }
      const workspaceResult = await StorageManager.getWorkspace(workspaceId);
      if (!workspaceResult.success) {
        return { success: false, error: workspaceResult.error };
      }
      const workspace = workspaceResult.data;
      console.log(`📋 目标工作区: ${workspace.name} (${workspace.websites.length} 个网站)`);
      return { success: true, data: workspace };
    } catch (error) {
      return {
        success: false,
        error: {
          code: ERROR_CODES.WORKSPACE_ERROR,
          message: "Failed to validate switch request",
          details: error
        }
      };
    }
  }
  /**
   * 检查是否需要切换（避免重复切换到同一工作区）
   */
  static async shouldPerformSwitch(targetWorkspaceId) {
    try {
      const currentWorkspaceResult = await this.getCurrentWorkspace();
      if (currentWorkspaceResult.success && currentWorkspaceResult.data) {
        const currentWorkspace = currentWorkspaceResult.data;
        if (currentWorkspace.id === targetWorkspaceId) {
          console.log(`ℹ️ 已在目标工作区 "${currentWorkspace.name}"，跳过切换`);
          return false;
        }
      }
      return true;
    } catch (error) {
      console.warn("检查是否需要切换时出错:", error);
      return true;
    }
  }
  /**
   * 完成工作区切换后的清理工作
   */
  static async finalizeSwitchOperation(workspaceId) {
    try {
      const setActiveResult = await this.setCurrentWorkspace(workspaceId);
      if (!setActiveResult.success) {
        return setActiveResult;
      }
      this.setBackgroundSetupStatus(null, false);
      console.log(`✅ 工作区切换完成: ${workspaceId}`);
      return { success: true };
    } catch (error) {
      return {
        success: false,
        error: {
          code: ERROR_CODES.WORKSPACE_ERROR,
          message: "Failed to finalize switch operation",
          details: error
        }
      };
    }
  }
}

class WorkspaceTabMover {
  /**
   * 将非目标工作区的标签页移动到后台窗口
   */
  static async moveNonTargetWorkspaceTabsToWindow(targetWorkspaceId) {
    try {
      console.log(`🔄 移动非目标工作区标签页到后台窗口 (目标工作区: ${targetWorkspaceId})`);
      const currentTabs = await chrome.tabs.query({ currentWindow: true });
      const tabsToMove = [];
      for (const tab of currentTabs) {
        if (!tab.id || !tab.url) continue;
        if (TabClassificationUtils.isSystemTab(tab.url)) {
          continue;
        }
        if (tab.active) {
          continue;
        }
        const workonaIdResult = await WorkonaTabManager.getWorkonaIdByChromeId(tab.id);
        if (workonaIdResult.success && workonaIdResult.data) {
          const tabWorkspaceId = workonaIdResult.data.split("-")[1];
          if (tabWorkspaceId !== targetWorkspaceId) {
            tabsToMove.push(tab.id);
          }
        } else {
          tabsToMove.push(tab.id);
        }
      }
      if (tabsToMove.length === 0) {
        console.log(`ℹ️ 没有需要移动的标签页`);
        return { success: true };
      }
      const backgroundWindow = await this.getOrCreateBackgroundWindow();
      if (!backgroundWindow) {
        return {
          success: false,
          error: {
            code: ERROR_CODES.WINDOW_ERROR,
            message: "Failed to create background window"
          }
        };
      }
      await chrome.tabs.move(tabsToMove, {
        windowId: backgroundWindow.id,
        index: -1
      });
      console.log(`✅ 成功移动 ${tabsToMove.length} 个标签页到后台窗口`);
      return { success: true };
    } catch (error) {
      return {
        success: false,
        error: {
          code: ERROR_CODES.TAB_ERROR,
          message: "Failed to move non-target workspace tabs",
          details: error
        }
      };
    }
  }
  /**
   * 将当前标签页移动到工作区专属窗口
   */
  static async moveCurrentTabsToWorkspaceWindow(workspace) {
    try {
      console.log(`🔄 移动当前标签页到工作区 "${workspace.name}" 的专属窗口`);
      const currentTabs = await chrome.tabs.query({ currentWindow: true });
      const workspaceTabIds = [];
      for (const tab of currentTabs) {
        if (!tab.id || !tab.url) continue;
        if (TabClassificationUtils.isSystemTab(tab.url)) {
          continue;
        }
        const workonaIdResult = await WorkonaTabManager.getWorkonaIdByChromeId(tab.id);
        if (workonaIdResult.success && workonaIdResult.data) {
          const tabWorkspaceId = workonaIdResult.data.split("-")[1];
          if (tabWorkspaceId === workspace.id) {
            workspaceTabIds.push(tab.id);
          }
        }
      }
      if (workspaceTabIds.length === 0) {
        console.log(`ℹ️ 当前窗口没有属于工作区 "${workspace.name}" 的标签页`);
        return { success: true };
      }
      const workspaceWindow = await this.getOrCreateWorkspaceWindow(workspace);
      if (!workspaceWindow) {
        return {
          success: false,
          error: {
            code: ERROR_CODES.WINDOW_ERROR,
            message: "Failed to create workspace window"
          }
        };
      }
      await chrome.tabs.move(workspaceTabIds, {
        windowId: workspaceWindow.id,
        index: -1
      });
      console.log(`✅ 成功移动 ${workspaceTabIds.length} 个标签页到工作区窗口`);
      return { success: true };
    } catch (error) {
      return {
        success: false,
        error: {
          code: ERROR_CODES.TAB_ERROR,
          message: "Failed to move tabs to workspace window",
          details: error
        }
      };
    }
  }
  /**
   * 将标签页从工作区窗口移动回主窗口
   */
  static async moveTabsFromWorkspaceWindow(workspace) {
    try {
      console.log(`🔄 将标签页从工作区 "${workspace.name}" 窗口移动回主窗口`);
      const allWindows = await chrome.windows.getAll({ populate: true });
      const currentWindow = await chrome.windows.getCurrent();
      const tabsToMove = [];
      for (const window of allWindows) {
        if (window.id === currentWindow.id || !window.tabs) {
          continue;
        }
        for (const tab of window.tabs) {
          if (!tab.id || !tab.url) continue;
          if (TabClassificationUtils.isSystemTab(tab.url)) {
            continue;
          }
          const workonaIdResult = await WorkonaTabManager.getWorkonaIdByChromeId(tab.id);
          if (workonaIdResult.success && workonaIdResult.data) {
            const tabWorkspaceId = workonaIdResult.data.split("-")[1];
            if (tabWorkspaceId === workspace.id) {
              tabsToMove.push(tab.id);
            }
          }
        }
      }
      if (tabsToMove.length === 0) {
        console.log(`ℹ️ 没有需要移动回主窗口的标签页`);
        return { success: true };
      }
      await chrome.tabs.move(tabsToMove, {
        windowId: currentWindow.id,
        index: -1
      });
      console.log(`✅ 成功移动 ${tabsToMove.length} 个标签页回主窗口`);
      return { success: true };
    } catch (error) {
      return {
        success: false,
        error: {
          code: ERROR_CODES.TAB_ERROR,
          message: "Failed to move tabs from workspace window",
          details: error
        }
      };
    }
  }
  /**
   * 获取或创建后台窗口
   */
  static async getOrCreateBackgroundWindow() {
    try {
      const allWindows = await chrome.windows.getAll();
      const backgroundWindow = allWindows.find(
        (window) => window.state === "minimized" && window.type === "normal"
      );
      if (backgroundWindow) {
        return backgroundWindow;
      }
      const newWindow = await chrome.windows.create({
        url: "about:blank",
        focused: false,
        state: "minimized",
        type: "normal"
      });
      return newWindow;
    } catch (error) {
      console.error("创建后台窗口失败:", error);
      return null;
    }
  }
  /**
   * 获取或创建工作区专属窗口
   */
  static async getOrCreateWorkspaceWindow(_workspace) {
    try {
      const currentWindow = await chrome.windows.getCurrent();
      return currentWindow;
    } catch (error) {
      console.error("获取工作区窗口失败:", error);
      return null;
    }
  }
}

class WorkspaceProtectionManager {
  /**
   * 确保系统标签页保护
   * 防止重要的系统标签页被意外关闭或移动
   */
  static async ensureSystemTabProtection() {
    try {
      console.log("🛡️ 执行系统标签页保护检查");
      const currentTabs = await chrome.tabs.query({ currentWindow: true });
      const systemTabs = currentTabs.filter(
        (tab) => tab.url && TabClassificationUtils.isSystemTab(tab.url)
      );
      if (systemTabs.length > 0) {
        console.log(`🛡️ 发现 ${systemTabs.length} 个系统标签页，确保其保护状态`);
        for (const tab of systemTabs) {
          if (tab.id) {
            console.log(`🛡️ 保护系统标签页: ${tab.url}`);
          }
        }
      }
      const nonSystemTabs = currentTabs.filter(
        (tab) => tab.url && !TabClassificationUtils.isSystemTab(tab.url)
      );
      if (nonSystemTabs.length === 0) {
        console.warn("⚠️ 当前窗口没有非系统标签页，可能需要创建占位符标签页");
        await chrome.tabs.create({
          url: "about:blank",
          active: false
        });
        console.log("✅ 已创建占位符标签页");
      }
    } catch (error) {
      console.error("系统标签页保护失败:", error);
    }
  }
  /**
   * 确保工作区切换保护
   * 防止切换过程中的数据丢失和状态混乱
   */
  static async ensureWorkspaceSwitchProtection(targetWorkspaceId) {
    try {
      console.log(`🛡️ 执行工作区切换保护检查 (目标: ${targetWorkspaceId})`);
      const currentWindow = await chrome.windows.getCurrent();
      if (!currentWindow.id) {
        throw new Error("无法获取当前窗口ID");
      }
      const currentTabs = await chrome.tabs.query({ windowId: currentWindow.id });
      console.log(`🛡️ 当前窗口有 ${currentTabs.length} 个标签页`);
      if (currentTabs.length === 0) {
        console.warn("⚠️ 当前窗口没有标签页，创建占位符标签页");
        await chrome.tabs.create({
          url: "about:blank",
          windowId: currentWindow.id,
          active: true
        });
      }
      const activeTabs = currentTabs.filter((tab) => tab.active);
      if (activeTabs.length === 0 && currentTabs.length > 0) {
        console.warn("⚠️ 没有活跃标签页，激活第一个标签页");
        const firstTab = currentTabs[0];
        if (firstTab.id) {
          await chrome.tabs.update(firstTab.id, { active: true });
        }
      }
      console.log("✅ 工作区切换保护检查完成");
    } catch (error) {
      console.error("工作区切换保护失败:", error);
    }
  }
  /**
   * 验证标签页状态完整性
   */
  static async validateTabStateIntegrity() {
    try {
      console.log("🔍 验证标签页状态完整性");
      const allWindows = await chrome.windows.getAll({ populate: true });
      let totalTabs = 0;
      let systemTabs = 0;
      let userTabs = 0;
      for (const window of allWindows) {
        if (!window.tabs) continue;
        for (const tab of window.tabs) {
          if (!tab.url) continue;
          totalTabs++;
          if (TabClassificationUtils.isSystemTab(tab.url)) {
            systemTabs++;
          } else {
            userTabs++;
          }
        }
      }
      console.log(`📊 标签页统计: 总计 ${totalTabs}, 系统 ${systemTabs}, 用户 ${userTabs}`);
      if (totalTabs === 0) {
        return {
          success: false,
          error: {
            code: ERROR_CODES.TAB_ERROR,
            message: "No tabs found in any window"
          }
        };
      }
      if (allWindows.length === 0) {
        return {
          success: false,
          error: {
            code: ERROR_CODES.WINDOW_ERROR,
            message: "No windows found"
          }
        };
      }
      console.log("✅ 标签页状态完整性验证通过");
      return { success: true };
    } catch (error) {
      return {
        success: false,
        error: {
          code: ERROR_CODES.TAB_ERROR,
          message: "Failed to validate tab state integrity",
          details: error
        }
      };
    }
  }
  /**
   * 检测并处理孤立标签页
   * 查找没有正确Workona ID映射的标签页
   */
  static async detectAndHandleOrphanedTabs() {
    try {
      console.log("🔍 检测孤立标签页");
      const allWindows = await chrome.windows.getAll({ populate: true });
      const orphanedTabs = [];
      for (const window of allWindows) {
        if (!window.tabs) continue;
        for (const tab of window.tabs) {
          if (!tab.id || !tab.url) continue;
          if (TabClassificationUtils.isSystemTab(tab.url)) {
            continue;
          }
        }
      }
      console.log(`📊 发现 ${orphanedTabs.length} 个孤立标签页`);
      return { success: true, data: orphanedTabs.length };
    } catch (error) {
      return {
        success: false,
        error: {
          code: ERROR_CODES.TAB_ERROR,
          message: "Failed to detect orphaned tabs",
          details: error
        }
      };
    }
  }
  /**
   * 紧急恢复机制
   * 在检测到严重问题时执行的恢复操作
   */
  static async emergencyRecovery() {
    try {
      console.log("🚨 执行紧急恢复机制");
      const allWindows = await chrome.windows.getAll();
      if (allWindows.length === 0) {
        await chrome.windows.create({
          url: "about:blank",
          focused: true,
          type: "normal"
        });
        console.log("🚨 已创建紧急恢复窗口");
      }
      const currentWindow = await chrome.windows.getCurrent();
      const currentTabs = await chrome.tabs.query({ windowId: currentWindow.id });
      if (currentTabs.length === 0) {
        await chrome.tabs.create({
          url: "about:blank",
          windowId: currentWindow.id,
          active: true
        });
        console.log("🚨 已创建紧急恢复标签页");
      }
      console.log("✅ 紧急恢复完成");
      return { success: true };
    } catch (error) {
      return {
        success: false,
        error: {
          code: ERROR_CODES.SYSTEM_ERROR,
          message: "Emergency recovery failed",
          details: error
        }
      };
    }
  }
}

class WorkspaceNotificationManager {
  /**
   * 通知工作区切换完成
   */
  static async notifyWorkspaceSwitchComplete(workspaceId) {
    try {
      console.log(`📢 通知工作区切换完成: ${workspaceId}`);
      const message = {
        type: "WORKSPACE_SWITCHED",
        workspaceId,
        timestamp: Date.now()
      };
      try {
        await chrome.runtime.sendMessage(message);
      } catch (error) {
        console.log("📢 没有消息监听器，跳过消息发送");
      }
      if (typeof window !== "undefined" && window.dispatchEvent) {
        const customEvent = new CustomEvent("workspaceSwitched", {
          detail: { workspaceId, timestamp: Date.now() }
        });
        window.dispatchEvent(customEvent);
      }
      console.log(`✅ 工作区切换通知发送完成: ${workspaceId}`);
    } catch (error) {
      console.error("发送工作区切换通知失败:", error);
    }
  }
  /**
   * 通知工作区设置状态变更
   */
  static async notifyWorkspaceSetupStatusChange(workspaceId, isSetupInProgress) {
    try {
      console.log(`📢 通知工作区设置状态变更: ${workspaceId}, 进行中: ${isSetupInProgress}`);
      const message = {
        type: "WORKSPACE_SETUP_STATUS_CHANGED",
        workspaceId,
        isSetupInProgress,
        timestamp: Date.now()
      };
      try {
        await chrome.runtime.sendMessage(message);
      } catch (error) {
        console.log("📢 没有消息监听器，跳过消息发送");
      }
      if (typeof window !== "undefined" && window.dispatchEvent) {
        const customEvent = new CustomEvent("workspaceSetupStatusChanged", {
          detail: { workspaceId, isSetupInProgress, timestamp: Date.now() }
        });
        window.dispatchEvent(customEvent);
      }
      console.log(`✅ 工作区设置状态通知发送完成: ${workspaceId}`);
    } catch (error) {
      console.error("发送工作区设置状态通知失败:", error);
    }
  }
  /**
   * 通知工作区标签页状态变更
   */
  static async notifyWorkspaceTabStateChange(workspaceId, changeType, details) {
    try {
      console.log(`📢 通知工作区标签页状态变更: ${workspaceId}, 类型: ${changeType}`);
      const message = {
        type: "WORKSPACE_TAB_STATE_CHANGED",
        workspaceId,
        changeType,
        details,
        timestamp: Date.now()
      };
      try {
        await chrome.runtime.sendMessage(message);
      } catch (error) {
        console.log("📢 没有消息监听器，跳过消息发送");
      }
      console.log(`✅ 工作区标签页状态变更通知发送完成: ${workspaceId}`);
    } catch (error) {
      console.error("发送工作区标签页状态变更通知失败:", error);
    }
  }
  /**
   * 通知用户标签页可见性变更
   */
  static async notifyUserTabsVisibilityChange(workspaceId, isHidden, affectedCount) {
    try {
      console.log(`📢 通知用户标签页可见性变更: ${workspaceId}, 隐藏: ${isHidden}, 影响数量: ${affectedCount}`);
      const message = {
        type: "USER_TABS_VISIBILITY_CHANGED",
        workspaceId,
        isHidden,
        affectedCount,
        timestamp: Date.now()
      };
      try {
        await chrome.runtime.sendMessage(message);
      } catch (error) {
        console.log("📢 没有消息监听器，跳过消息发送");
      }
      console.log(`✅ 用户标签页可见性变更通知发送完成: ${workspaceId}`);
    } catch (error) {
      console.error("发送用户标签页可见性变更通知失败:", error);
    }
  }
  /**
   * 发送通用工作区事件通知
   */
  static async sendWorkspaceEvent(eventType, workspaceId, data) {
    try {
      console.log(`📢 发送工作区事件: ${eventType}, 工作区: ${workspaceId}`);
      const message = {
        type: "WORKSPACE_EVENT",
        eventType,
        workspaceId,
        data,
        timestamp: Date.now()
      };
      try {
        await chrome.runtime.sendMessage(message);
      } catch (error) {
        console.log("📢 没有消息监听器，跳过消息发送");
      }
      if (typeof window !== "undefined" && window.dispatchEvent) {
        const customEvent = new CustomEvent("workspaceEvent", {
          detail: { eventType, workspaceId, data, timestamp: Date.now() }
        });
        window.dispatchEvent(customEvent);
      }
      console.log(`✅ 工作区事件通知发送完成: ${eventType}`);
    } catch (error) {
      console.error("发送工作区事件通知失败:", error);
    }
  }
  /**
   * 批量发送通知
   */
  static async sendBatchNotifications(notifications) {
    try {
      console.log(`📢 批量发送 ${notifications.length} 个通知`);
      const promises = notifications.map(
        (notification) => this.sendWorkspaceEvent(notification.type, notification.workspaceId, notification.data)
      );
      await Promise.allSettled(promises);
      console.log(`✅ 批量通知发送完成`);
    } catch (error) {
      console.error("批量发送通知失败:", error);
    }
  }
  /**
   * 清理过期的通知监听器
   */
  static cleanupExpiredListeners() {
    try {
      console.log("🧹 清理过期的通知监听器");
      console.log("✅ 通知监听器清理完成");
    } catch (error) {
      console.error("清理通知监听器失败:", error);
    }
  }
}

class WorkspaceSwitcher {
  /**
   * 检查是否有后台设置正在进行
   */
  static isSetupInProgress() {
    return WorkspaceSwitchCore.isSetupInProgress();
  }
  /**
   * 获取当前正在设置的工作区ID
   */
  static getCurrentSetupWorkspaceId() {
    return WorkspaceSwitchCore.getCurrentSetupWorkspaceId();
  }
  /**
   * 主要的工作区切换方法
   * 整合了所有拆分后的服务类功能
   */
  static async switchToWorkspace(workspaceId, options = {}) {
    try {
      console.log(`🔄 开始切换到工作区: ${workspaceId}`);
      const validationResult = await WorkspaceSwitchCore.validateSwitchRequest(workspaceId);
      if (!validationResult.success) {
        return { success: false, error: validationResult.error };
      }
      const workspace = validationResult.data;
      const shouldSwitch = await WorkspaceSwitchCore.shouldPerformSwitch(workspaceId);
      if (!shouldSwitch) {
        return { success: true };
      }
      const optionsResult = await WorkspaceSwitchCore.prepareSwitchOptions(options);
      if (!optionsResult.success) {
        return { success: false, error: optionsResult.error };
      }
      const switchOptions = optionsResult.data;
      const currentWorkspaceResult = await WorkspaceSwitchCore.getCurrentWorkspace();
      const currentWorkspace = currentWorkspaceResult.success ? currentWorkspaceResult.data : null;
      WorkspaceSwitchCore.setBackgroundSetupStatus(workspaceId, true);
      await WorkspaceProtectionManager.ensureSystemTabProtection();
      await WorkspaceProtectionManager.ensureWorkspaceSwitchProtection(workspaceId);
      if (currentWorkspace) {
        console.log(`💾 保存当前工作区状态: ${currentWorkspace.name}`);
        await WorkspaceSessionManager.syncCurrentWorkspaceState();
      }
      if (currentWorkspace && currentWorkspace.id !== workspaceId) {
        await WorkspaceTabMover.moveCurrentTabsToWorkspaceWindow(currentWorkspace);
      } else if (!currentWorkspace) {
        await WorkspaceTabMover.moveNonTargetWorkspaceTabsToWindow(workspaceId);
      }
      await this.handlePendingTabAssignment(workspaceId);
      if (switchOptions.autoOpenWebsites) {
        await this.openWorkspaceWebsites(workspace);
      }
      await this.handleUserTabsVisibilityState(workspace);
      const finalizeResult = await WorkspaceSwitchCore.finalizeSwitchOperation(workspaceId);
      if (!finalizeResult.success) {
        return finalizeResult;
      }
      await WorkspaceNotificationManager.notifyWorkspaceSwitchComplete(workspaceId);
      await WorkspaceNotificationManager.notifyWorkspaceSetupStatusChange(workspaceId, false);
      console.log(`✅ 工作区切换完成: ${workspace.name}`);
      return { success: true };
    } catch (error) {
      WorkspaceSwitchCore.setBackgroundSetupStatus(null, false);
      return {
        success: false,
        error: {
          code: ERROR_CODES.WORKSPACE_ERROR,
          message: "Failed to switch workspace",
          details: error
        }
      };
    }
  }
  /**
   * 获取当前工作区
   */
  static async getCurrentWorkspace() {
    return WorkspaceSwitchCore.getCurrentWorkspace();
  }
  /**
   * 检测活跃工作区
   */
  static async detectActiveWorkspace() {
    try {
      const currentResult = await WorkspaceSwitchCore.getCurrentWorkspace();
      if (currentResult.success && currentResult.data) {
        return currentResult;
      }
      const activeTabResult = await TabManager.getActiveTab();
      if (!activeTabResult.success || !activeTabResult.data) {
        return { success: true, data: null };
      }
      const activeTab = activeTabResult.data;
      const workspacesResult = await StorageManager.getWorkspaces();
      if (!workspacesResult.success) {
        return { success: false, error: workspacesResult.error };
      }
      const workspaces = workspacesResult.data;
      const workonaIdResult = await WorkonaTabManager.getWorkonaIdByChromeId(activeTab.id);
      if (workonaIdResult.success && workonaIdResult.data) {
        const workspaceId = workonaIdResult.data.split("-")[1];
        const workspace = workspaces.find((w) => w.id === workspaceId);
        if (workspace) {
          console.log(`🎯 通过Workona ID检测到活跃工作区: ${workspace.name}`);
          return { success: true, data: workspace };
        }
      }
      const matchResult = await WorkspaceTabContentMatcher.isWorkspaceTab(activeTab);
      if (matchResult.isMatch && matchResult.workspaceId) {
        const matchingWorkspace = workspaces.find((w) => w.id === matchResult.workspaceId);
        if (matchingWorkspace) {
          console.log(`🏢 通过智能匹配检测到活跃工作区: ${matchingWorkspace.name} (置信度: ${matchResult.confidence})`);
          return { success: true, data: matchingWorkspace };
        }
      }
      return { success: true, data: null };
    } catch (error) {
      return {
        success: false,
        error: {
          code: ERROR_CODES.WORKSPACE_ERROR,
          message: "Failed to detect active workspace",
          details: error
        }
      };
    }
  }
  /**
   * 处理等待归属的标签页
   */
  static async handlePendingTabAssignment(workspaceId) {
    try {
      console.log(`🔄 处理等待归属的标签页，分配到工作区: ${workspaceId}`);
      const allTabs = await chrome.tabs.query({});
      const pendingTabs = [];
      for (const tab of allTabs) {
        if (!tab.id || !tab.url) continue;
        if (TabClassificationUtils.isSystemTab(tab.url)) {
          continue;
        }
        const workonaIdResult = await WorkonaTabManager.getWorkonaIdByChromeId(tab.id);
        if (workonaIdResult.success && workonaIdResult.data) {
          const tabWorkspaceId = workonaIdResult.data.split("-")[1];
          if (tabWorkspaceId === "pending-assignment") {
            pendingTabs.push(tab);
          }
        }
      }
      if (pendingTabs.length === 0) {
        console.log(`ℹ️ 没有等待归属的标签页`);
        return;
      }
      for (const tab of pendingTabs) {
        if (!tab.id) continue;
        const oldWorkonaIdResult = await WorkonaTabManager.getWorkonaIdByChromeId(tab.id);
        if (oldWorkonaIdResult.success && oldWorkonaIdResult.data) {
          await WorkonaTabManager.removeTabMapping(oldWorkonaIdResult.data);
          const newWorkonaId = WorkonaTabManager.generateWorkonaTabId(workspaceId);
          await WorkonaTabManager.createTabIdMapping(
            newWorkonaId,
            tab.id,
            workspaceId,
            void 0,
            {
              isWorkspaceCore: false,
              source: "user_opened"
            }
          );
          console.log(`✅ 重新分配标签页: ${tab.title} -> ${workspaceId}`);
        }
      }
      console.log(`✅ 完成 ${pendingTabs.length} 个等待归属标签页的重新分配`);
    } catch (error) {
      console.error("处理等待归属标签页失败:", error);
    }
  }
  /**
   * 打开工作区网站
   */
  static async openWorkspaceWebsites(workspace) {
    try {
      console.log(`🌐 打开工作区网站: ${workspace.name}`);
      for (const website of workspace.websites) {
        const existingTabs = await chrome.tabs.query({ url: website.url + "*" });
        if (existingTabs.length > 0) {
          console.log(`ℹ️ 网站已打开，跳过: ${website.title}`);
          continue;
        }
        const createResult = await TabManager.createTab(website.url, false, false);
        if (createResult.success) {
          console.log(`✅ 打开网站: ${website.title}`);
        } else {
          console.error(`❌ 打开网站失败: ${website.title}`, createResult.error);
        }
      }
    } catch (error) {
      console.error("打开工作区网站失败:", error);
    }
  }
  /**
   * 处理用户标签页可见性状态
   */
  static async handleUserTabsVisibilityState(workspace) {
    try {
      console.log(`👁️ 处理工作区用户标签页可见性: ${workspace.name}`);
      const stateResult = await WorkspaceUserTabsVisibilityManager.getWorkspaceUserTabsState(workspace.id);
      if (stateResult.success && stateResult.data) {
        const state = stateResult.data;
        console.log(`📊 用户标签页状态: 总计 ${state.totalUserTabs}, 可见 ${state.visibleUserTabs}, 隐藏 ${state.hiddenTabIds.length}`);
        await UserTabsRealTimeMonitor.forceRefreshWorkspaceState(workspace.id);
      }
    } catch (error) {
      console.error("处理用户标签页可见性状态失败:", error);
    }
  }
  /**
   * 向后兼容的方法 - 委托给新的服务类
   */
  // 系统保护相关
  static async ensureSystemTabProtection() {
    return WorkspaceProtectionManager.ensureSystemTabProtection();
  }
  static async ensureWorkspaceSwitchProtection(workspaceId) {
    return WorkspaceProtectionManager.ensureWorkspaceSwitchProtection(workspaceId);
  }
  // 标签页移动相关
  static async moveNonTargetWorkspaceTabsToWindow(targetWorkspaceId) {
    return WorkspaceTabMover.moveNonTargetWorkspaceTabsToWindow(targetWorkspaceId);
  }
  static async moveCurrentTabsToWorkspaceWindow(workspace) {
    return WorkspaceTabMover.moveCurrentTabsToWorkspaceWindow(workspace);
  }
  // 通知相关
  static async notifyWorkspaceSwitchComplete(workspaceId) {
    return WorkspaceNotificationManager.notifyWorkspaceSwitchComplete(workspaceId);
  }
}

const workspaceSwitcher = /*#__PURE__*/Object.freeze(/*#__PURE__*/Object.defineProperty({
  __proto__: null,
  TabClassificationUtils,
  WorkspaceNotificationManager,
  WorkspaceProtectionManager,
  WorkspaceSwitchCore,
  WorkspaceSwitcher,
  WorkspaceTabMover
}, Symbol.toStringTag, { value: 'Module' }));

class MigrationManager {
  static CURRENT_VERSION = "1.0.0";
  static BACKUP_KEY = "migrationBackup";
  /**
   * 检测当前数据版本
   */
  static async detectDataVersion() {
    try {
      const versionResult = await StorageManager.getDataVersion();
      if (!versionResult.success) {
        return { success: true, data: "0.0.0" };
      }
      return { success: true, data: versionResult.data };
    } catch (error) {
      return {
        success: false,
        error: {
          code: ERROR_CODES.STORAGE_ERROR,
          message: "Failed to detect data version",
          details: error
        }
      };
    }
  }
  /**
   * 执行 Workona 格式迁移
   */
  static async migrateToWorkonaFormat(options = {}) {
    try {
      console.log("🚀 开始 Workona 格式数据迁移...");
      const versionResult = await this.detectDataVersion();
      if (!versionResult.success) {
        return { success: false, error: versionResult.error };
      }
      const currentVersion = versionResult.data;
      if (currentVersion === this.CURRENT_VERSION) {
        console.log("✅ 数据已是最新版本，无需迁移");
        return { success: true, data: false };
      }
      if (options.backupOriginalData !== false) {
        const backupResult = await this.backupOriginalData();
        if (!backupResult.success) {
          console.error("❌ 数据备份失败，中止迁移");
          return { success: false, error: backupResult.error };
        }
        console.log("💾 原始数据备份完成");
      }
      const allDataResult = await StorageManager.getAllData();
      if (!allDataResult.success) {
        return { success: false, error: allDataResult.error };
      }
      const data = allDataResult.data;
      console.log(`📊 检测到 ${data.workspaces.length} 个工作区需要迁移`);
      const migratedWorkspaces = await this.migrateWorkspaces(data.workspaces);
      if (!migratedWorkspaces.success) {
        if (options.rollbackOnError !== false) {
          await this.rollbackMigration();
        }
        return { success: false, error: migratedWorkspaces.error };
      }
      const initResult = await this.initializeWorkonaData();
      if (!initResult.success) {
        if (options.rollbackOnError !== false) {
          await this.rollbackMigration();
        }
        return { success: false, error: initResult.error };
      }
      const versionUpdateResult = await StorageManager.saveDataVersion(this.CURRENT_VERSION);
      if (!versionUpdateResult.success) {
        if (options.rollbackOnError !== false) {
          await this.rollbackMigration();
        }
        return { success: false, error: versionUpdateResult.error };
      }
      if (options.validateAfterMigration !== false) {
        const validationResult = await this.validateMigration();
        if (!validationResult.success) {
          console.error("❌ 迁移验证失败");
          if (options.rollbackOnError !== false) {
            await this.rollbackMigration();
          }
          return { success: false, error: validationResult.error };
        }
        console.log("✅ 迁移验证通过");
      }
      console.log("🎉 Workona 格式迁移完成！");
      return { success: true, data: true };
    } catch (error) {
      console.error("❌ 迁移过程中发生错误:", error);
      if (options.rollbackOnError !== false) {
        await this.rollbackMigration();
      }
      return {
        success: false,
        error: {
          code: ERROR_CODES.STORAGE_ERROR,
          message: "Migration failed",
          details: error
        }
      };
    }
  }
  /**
   * 迁移工作区数据
   */
  static async migrateWorkspaces(workspaces) {
    try {
      const migratedWorkspaces = [];
      for (const workspace of workspaces) {
        const migratedWorkspace = {
          ...workspace,
          // 添加 Workona 风格字段（如果不存在）
          type: workspace.type || "saved",
          pos: workspace.pos || workspace.createdAt,
          state: workspace.state || (workspace.isActive ? "active" : "inactive"),
          workonaTabIds: workspace.workonaTabIds || [],
          sessionId: workspace.sessionId || `session_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
          tabOrder: workspace.tabOrder || []
        };
        migratedWorkspaces.push(migratedWorkspace);
        console.log(`✨ 迁移工作区: ${workspace.name} -> Workona 格式`);
      }
      const saveResult = await StorageManager.saveWorkspaces(migratedWorkspaces);
      if (!saveResult.success) {
        return { success: false, error: saveResult.error };
      }
      return { success: true, data: migratedWorkspaces };
    } catch (error) {
      return {
        success: false,
        error: {
          code: ERROR_CODES.STORAGE_ERROR,
          message: "Failed to migrate workspaces",
          details: error
        }
      };
    }
  }
  /**
   * 初始化 Workona 数据结构
   */
  static async initializeWorkonaData() {
    try {
      await StorageManager.saveTabIdMappings([]);
      await StorageManager.saveLocalOpenWorkspaces({});
      await StorageManager.saveTabGroups({});
      await StorageManager.saveWorkspaceSessions({});
      console.log("🏗️ Workona 数据结构初始化完成");
      return { success: true };
    } catch (error) {
      return {
        success: false,
        error: {
          code: ERROR_CODES.STORAGE_ERROR,
          message: "Failed to initialize Workona data",
          details: error
        }
      };
    }
  }
  /**
   * 备份原始数据
   */
  static async backupOriginalData() {
    try {
      const allDataResult = await StorageManager.getAllData();
      if (!allDataResult.success) {
        return { success: false, error: allDataResult.error };
      }
      const backupData = {
        ...allDataResult.data,
        backupTimestamp: Date.now(),
        backupVersion: await this.detectDataVersion()
      };
      await chrome.storage.local.set({
        [this.BACKUP_KEY]: backupData
      });
      return { success: true };
    } catch (error) {
      return {
        success: false,
        error: {
          code: ERROR_CODES.STORAGE_ERROR,
          message: "Failed to backup original data",
          details: error
        }
      };
    }
  }
  /**
   * 回滚迁移
   */
  static async rollbackMigration() {
    try {
      console.log("🔄 开始回滚迁移...");
      const result = await chrome.storage.local.get([this.BACKUP_KEY]);
      const backupData = result[this.BACKUP_KEY];
      if (!backupData) {
        return {
          success: false,
          error: {
            code: ERROR_CODES.STORAGE_ERROR,
            message: "No backup data found for rollback"
          }
        };
      }
      await StorageManager.saveWorkspaces(backupData.workspaces);
      await StorageManager.saveSettings(backupData.settings);
      await StorageManager.setActiveWorkspaceId(backupData.activeWorkspaceId);
      await chrome.storage.local.remove([
        WORKONA_STORAGE_KEYS.TAB_ID_MAPPINGS,
        WORKONA_STORAGE_KEYS.LOCAL_OPEN_WORKSPACES,
        WORKONA_STORAGE_KEYS.TAB_GROUPS,
        WORKONA_STORAGE_KEYS.WORKSPACE_SESSIONS,
        WORKONA_STORAGE_KEYS.DATA_VERSION
      ]);
      console.log("✅ 迁移回滚完成");
      return { success: true };
    } catch (error) {
      return {
        success: false,
        error: {
          code: ERROR_CODES.STORAGE_ERROR,
          message: "Failed to rollback migration",
          details: error
        }
      };
    }
  }
  /**
   * 验证迁移结果
   */
  static async validateMigration() {
    try {
      const versionResult = await StorageManager.getDataVersion();
      if (!versionResult.success || versionResult.data !== this.CURRENT_VERSION) {
        throw new Error("Data version validation failed");
      }
      const workspacesResult = await StorageManager.getWorkspaces();
      if (!workspacesResult.success) {
        throw new Error("Workspaces validation failed");
      }
      const workspaces = workspacesResult.data;
      for (const workspace of workspaces) {
        if (!workspace.type || !workspace.pos || !workspace.state) {
          throw new Error(`Workspace ${workspace.name} missing Workona fields`);
        }
      }
      const mappingsResult = await StorageManager.getTabIdMappings();
      const sessionsResult = await StorageManager.getWorkspaceSessions();
      if (!mappingsResult.success || !sessionsResult.success) {
        throw new Error("Workona data structures validation failed");
      }
      return { success: true };
    } catch (error) {
      return {
        success: false,
        error: {
          code: ERROR_CODES.STORAGE_ERROR,
          message: "Migration validation failed",
          details: error
        }
      };
    }
  }
  /**
   * 清理备份数据
   */
  static async cleanupBackup() {
    try {
      await chrome.storage.local.remove([this.BACKUP_KEY]);
      console.log("🧹 迁移备份数据已清理");
      return { success: true };
    } catch (error) {
      return {
        success: false,
        error: {
          code: ERROR_CODES.STORAGE_ERROR,
          message: "Failed to cleanup backup",
          details: error
        }
      };
    }
  }
  // === 概念性重构：标签页元数据迁移方法 ===
  /**
   * 迁移现有 Workona ID 映射的元数据（概念性重构）
   */
  static async migrateTabMappingsMetadata() {
    try {
      console.log("🔄 迁移现有 Workona ID 映射的元数据...");
      const mappingsResult = await StorageManager.getTabIdMappings();
      if (!mappingsResult.success) {
        console.log("ℹ️ 没有现有的 Workona ID 映射需要迁移");
        return { success: true, data: 0 };
      }
      const mappings = mappingsResult.data;
      let migratedCount = 0;
      for (const mapping of mappings) {
        if (mapping.hasOwnProperty("isWorkspaceCore") && mapping.hasOwnProperty("tabType")) {
          continue;
        }
        const isWorkspaceCore = !!mapping.websiteId;
        const tabType = isWorkspaceCore ? "core" : "session";
        const source = isWorkspaceCore ? "workspace_website" : "user_opened";
        const updatedMapping = {
          ...mapping,
          isWorkspaceCore,
          tabType,
          metadata: {
            source,
            addedToWorkspaceAt: isWorkspaceCore ? mapping.createdAt : void 0
          }
        };
        const index = mappings.findIndex((m) => m.workonaId === mapping.workonaId);
        if (index >= 0) {
          mappings[index] = updatedMapping;
          migratedCount++;
        }
      }
      if (migratedCount > 0) {
        const saveResult = await StorageManager.saveTabIdMappings(mappings);
        if (saveResult.success) {
          console.log(`✅ 成功迁移 ${migratedCount} 个 Workona ID 映射的元数据`);
          return { success: true, data: migratedCount };
        } else {
          throw new Error("保存迁移后的映射失败");
        }
      } else {
        console.log("ℹ️ 所有 Workona ID 映射已包含最新元数据");
        return { success: true, data: 0 };
      }
    } catch (error) {
      console.error("❌ 迁移 Workona ID 映射元数据失败:", error);
      return {
        success: false,
        error: {
          code: ERROR_CODES.STORAGE_ERROR,
          message: "Failed to migrate tab mappings metadata",
          details: error
        }
      };
    }
  }
}

export { COMMANDS as C, ERROR_CODES as E, MigrationManager as M, StorageManager as S, TabClassificationService as T, UserTabsRealTimeMonitor as U, WorkspaceManager as W, __vitePreload as _, WorkspaceSwitcher as a, WorkonaTabManager as b, WORKSPACE_ICONS as c, WORKSPACE_COLORS as d, WorkspaceUserTabsVisibilityManager as e, WorkspaceStateSync as f, WorkspaceSessionManager as g, tabs as t };
