const STORAGE_KEYS = {
  WORKSPACES: "workspaces",
  SETTINGS: "settings",
  ACTIVE_WORKSPACE_ID: "activeWorkspaceId",
  LAST_ACTIVE_WORKSPACE_IDS: "lastActiveWorkspaceIds"
};
const WORKONA_STORAGE_KEYS = {
  TAB_ID_MAPPINGS: "workonaTabIdMappings",
  LOCAL_OPEN_WORKSPACES: "localOpenWorkspaces",
  TAB_GROUPS: "tabGroups",
  WORKSPACE_SESSIONS: "workspaceSessions",
  GLOBAL_WORKSPACE_WINDOW_ID: "globalWorkspaceWindowId",
  DATA_VERSION: "workonaDataVersion",
  MIGRATION_BACKUP: "migrationBackup"
};
const DEFAULT_SETTINGS = {
  autoCloseOtherTabs: true,
  preserveUserOpenedTabs: false,
  defaultWorkspaceOnStartup: "",
  sidebarWidth: 320,
  theme: "dark",
  showFavicons: true,
  confirmBeforeDelete: true,
  maxRecentWorkspaces: 5
};
const WORKSPACE_COLORS = [
  "#3b82f6",
  // blue
  "#10b981",
  // emerald
  "#f59e0b",
  // amber
  "#ef4444",
  // red
  "#8b5cf6",
  // violet
  "#06b6d4",
  // cyan
  "#84cc16",
  // lime
  "#f97316",
  // orange
  "#ec4899",
  // pink
  "#6366f1"
  // indigo
];
const WORKSPACE_ICONS = [
  "🚀",
  "💼",
  "🔬",
  "🎨",
  "📊",
  "🛠️",
  "📚",
  "💡",
  "🎯",
  "⚡",
  "🌟",
  "🔥",
  "💎",
  "🎪",
  "🎭",
  "🎨",
  "🎵",
  "🎮",
  "🏆",
  "🎊",
  "📱",
  "💻",
  "🖥️",
  "⌨️",
  "🖱️",
  "🖨️",
  "📷",
  "📹",
  "🎥",
  "📺",
  "🔍",
  "🔎",
  "🔬",
  "🔭",
  "📡",
  "🛰️",
  "🚁",
  "✈️",
  "🛸"
];
const ERROR_CODES = {
  WORKSPACE_NOT_FOUND: "WORKSPACE_NOT_FOUND",
  WEBSITE_NOT_FOUND: "WEBSITE_NOT_FOUND",
  STORAGE_ERROR: "STORAGE_ERROR",
  TAB_ERROR: "TAB_ERROR",
  WINDOW_ERROR: "WINDOW_ERROR",
  PERMISSION_DENIED: "PERMISSION_DENIED",
  INVALID_URL: "INVALID_URL",
  DUPLICATE_WORKSPACE: "DUPLICATE_WORKSPACE",
  DUPLICATE_WEBSITE: "DUPLICATE_WEBSITE",
  WORKSPACE_ERROR: "WORKSPACE_ERROR",
  SYSTEM_ERROR: "SYSTEM_ERROR",
  EXPORT_ERROR: "EXPORT_ERROR",
  IMPORT_ERROR: "IMPORT_ERROR",
  INVALID_DATA_FORMAT: "INVALID_DATA_FORMAT"
};
const COMMANDS = {
  SWITCH_WORKSPACE_1: "switch-workspace-1",
  SWITCH_WORKSPACE_2: "switch-workspace-2",
  SWITCH_WORKSPACE_3: "switch-workspace-3",
  TOGGLE_SIDEPANEL: "toggle-sidepanel"
};
const DEFAULT_FAVICON = 'data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><circle cx="12" cy="12" r="10"/><path d="m9 12 2 2 4-4"/></svg>';

class StorageCore {
  static changeListeners = [];
  /**
   * 获取设置
   */
  static async getSettings() {
    try {
      const result = await chrome.storage.local.get(STORAGE_KEYS.SETTINGS);
      const settings = { ...DEFAULT_SETTINGS, ...result[STORAGE_KEYS.SETTINGS] };
      return { success: true, data: settings };
    } catch (error) {
      return {
        success: false,
        error: {
          code: ERROR_CODES.STORAGE_ERROR,
          message: "Failed to get settings",
          details: error
        }
      };
    }
  }
  /**
   * 保存设置
   */
  static async saveSettings(settings) {
    try {
      const currentResult = await StorageCore.getSettings();
      if (!currentResult.success) {
        return { success: false, error: currentResult.error };
      }
      const mergedSettings = { ...currentResult.data, ...settings };
      await chrome.storage.local.set({
        [STORAGE_KEYS.SETTINGS]: mergedSettings
      });
      return { success: true };
    } catch (error) {
      return {
        success: false,
        error: {
          code: ERROR_CODES.STORAGE_ERROR,
          message: "Failed to save settings",
          details: error
        }
      };
    }
  }
  /**
   * 获取活跃工作区ID
   */
  static async getActiveWorkspaceId() {
    try {
      const result = await chrome.storage.local.get(STORAGE_KEYS.ACTIVE_WORKSPACE_ID);
      const activeWorkspaceId = result[STORAGE_KEYS.ACTIVE_WORKSPACE_ID] || null;
      return { success: true, data: activeWorkspaceId };
    } catch (error) {
      return {
        success: false,
        error: {
          code: ERROR_CODES.STORAGE_ERROR,
          message: "Failed to get active workspace ID",
          details: error
        }
      };
    }
  }
  /**
   * 设置活跃工作区ID
   */
  static async setActiveWorkspaceId(id) {
    try {
      await chrome.storage.local.set({
        [STORAGE_KEYS.ACTIVE_WORKSPACE_ID]: id
      });
      return { success: true };
    } catch (error) {
      return {
        success: false,
        error: {
          code: ERROR_CODES.STORAGE_ERROR,
          message: "Failed to set active workspace ID",
          details: error
        }
      };
    }
  }
  /**
   * 更新最近活跃工作区列表
   */
  static async updateLastActiveWorkspaces(workspaceId) {
    try {
      const result = await chrome.storage.local.get(STORAGE_KEYS.LAST_ACTIVE_WORKSPACE_IDS);
      let lastActiveIds = result[STORAGE_KEYS.LAST_ACTIVE_WORKSPACE_IDS] || [];
      lastActiveIds = lastActiveIds.filter((id) => id !== workspaceId);
      lastActiveIds.unshift(workspaceId);
      lastActiveIds = lastActiveIds.slice(0, 10);
      await chrome.storage.local.set({
        [STORAGE_KEYS.LAST_ACTIVE_WORKSPACE_IDS]: lastActiveIds
      });
      return { success: true };
    } catch (error) {
      return {
        success: false,
        error: {
          code: ERROR_CODES.STORAGE_ERROR,
          message: "Failed to update last active workspaces",
          details: error
        }
      };
    }
  }
  /**
   * 获取最近活跃工作区列表
   */
  static async getLastActiveWorkspaces() {
    try {
      const result = await chrome.storage.local.get(STORAGE_KEYS.LAST_ACTIVE_WORKSPACE_IDS);
      const lastActiveIds = result[STORAGE_KEYS.LAST_ACTIVE_WORKSPACE_IDS] || [];
      return { success: true, data: lastActiveIds };
    } catch (error) {
      return {
        success: false,
        error: {
          code: ERROR_CODES.STORAGE_ERROR,
          message: "Failed to get last active workspaces",
          details: error
        }
      };
    }
  }
  /**
   * 清除所有存储数据
   */
  static async clearAll() {
    try {
      await chrome.storage.local.clear();
      return { success: true };
    } catch (error) {
      return {
        success: false,
        error: {
          code: ERROR_CODES.STORAGE_ERROR,
          message: "Failed to clear storage",
          details: error
        }
      };
    }
  }
  /**
   * 获取存储使用情况
   */
  static async getStorageUsage() {
    try {
      const bytesInUse = await chrome.storage.local.getBytesInUse();
      const quota = chrome.storage.local.QUOTA_BYTES;
      const percentUsed = bytesInUse / quota * 100;
      return {
        success: true,
        data: {
          bytesInUse,
          quota,
          percentUsed
        }
      };
    } catch (error) {
      return {
        success: false,
        error: {
          code: ERROR_CODES.STORAGE_ERROR,
          message: "Failed to get storage usage",
          details: error
        }
      };
    }
  }
  /**
   * 添加存储变化监听器
   */
  static onChanged(callback) {
    StorageCore.changeListeners.push(callback);
    if (StorageCore.changeListeners.length === 1) {
      chrome.storage.onChanged.addListener((changes, areaName) => {
        if (areaName === "local") {
          StorageCore.changeListeners.forEach((listener) => {
            try {
              listener(changes);
            } catch (error) {
              console.error("存储变化监听器执行失败:", error);
            }
          });
        }
      });
    }
  }
  /**
   * 移除存储变化监听器
   */
  static removeChangeListener(callback) {
    const index = StorageCore.changeListeners.indexOf(callback);
    if (index > -1) {
      StorageCore.changeListeners.splice(index, 1);
    }
  }
  /**
   * 检查存储键是否存在
   */
  static async hasKey(key) {
    try {
      const result = await chrome.storage.local.get(key);
      return { success: true, data: key in result };
    } catch (error) {
      return {
        success: false,
        error: {
          code: ERROR_CODES.STORAGE_ERROR,
          message: "Failed to check storage key",
          details: error
        }
      };
    }
  }
  /**
   * 删除指定的存储键
   */
  static async removeKey(key) {
    try {
      await chrome.storage.local.remove(key);
      return { success: true };
    } catch (error) {
      return {
        success: false,
        error: {
          code: ERROR_CODES.STORAGE_ERROR,
          message: "Failed to remove storage key",
          details: error
        }
      };
    }
  }
  /**
   * 批量删除存储键
   */
  static async removeKeys(keys) {
    try {
      await chrome.storage.local.remove(keys);
      return { success: true };
    } catch (error) {
      return {
        success: false,
        error: {
          code: ERROR_CODES.STORAGE_ERROR,
          message: "Failed to remove storage keys",
          details: error
        }
      };
    }
  }
}

class WorkspaceStorage {
  /**
   * 获取所有工作区
   */
  static async getWorkspaces() {
    try {
      const result = await chrome.storage.local.get(STORAGE_KEYS.WORKSPACES);
      const workspaces = result[STORAGE_KEYS.WORKSPACES] || [];
      const validatedWorkspaces = WorkspaceStorage.validateWorkspaces(workspaces);
      return { success: true, data: validatedWorkspaces };
    } catch (error) {
      return {
        success: false,
        error: {
          code: ERROR_CODES.STORAGE_ERROR,
          message: "Failed to get workspaces",
          details: error
        }
      };
    }
  }
  /**
   * 保存工作区列表
   */
  static async saveWorkspaces(workspaces) {
    try {
      const validatedWorkspaces = WorkspaceStorage.validateWorkspaces(workspaces);
      await chrome.storage.local.set({
        [STORAGE_KEYS.WORKSPACES]: validatedWorkspaces
      });
      return { success: true };
    } catch (error) {
      return {
        success: false,
        error: {
          code: ERROR_CODES.STORAGE_ERROR,
          message: "Failed to save workspaces",
          details: error
        }
      };
    }
  }
  /**
   * 获取单个工作区
   */
  static async getWorkspace(id) {
    const result = await WorkspaceStorage.getWorkspaces();
    if (!result.success) {
      return { success: false, error: result.error };
    }
    const workspaces = result.data;
    const workspace = workspaces.find((w) => w.id === id);
    if (!workspace) {
      return {
        success: false,
        error: {
          code: ERROR_CODES.WORKSPACE_NOT_FOUND,
          message: `Workspace with id ${id} not found`
        }
      };
    }
    return { success: true, data: workspace };
  }
  /**
   * 更新单个工作区
   */
  static async updateWorkspace(updatedWorkspace) {
    try {
      const workspacesResult = await WorkspaceStorage.getWorkspaces();
      if (!workspacesResult.success) {
        return { success: false, error: workspacesResult.error };
      }
      const workspaces = workspacesResult.data;
      const workspaceIndex = workspaces.findIndex((w) => w.id === updatedWorkspace.id);
      if (workspaceIndex === -1) {
        return {
          success: false,
          error: {
            code: ERROR_CODES.WORKSPACE_NOT_FOUND,
            message: `Workspace with id ${updatedWorkspace.id} not found`
          }
        };
      }
      workspaces[workspaceIndex] = updatedWorkspace;
      return await WorkspaceStorage.saveWorkspaces(workspaces);
    } catch (error) {
      return {
        success: false,
        error: {
          code: ERROR_CODES.STORAGE_ERROR,
          message: "Failed to update workspace",
          details: error
        }
      };
    }
  }
  /**
   * 删除工作区
   */
  static async deleteWorkspace(id) {
    try {
      const workspacesResult = await WorkspaceStorage.getWorkspaces();
      if (!workspacesResult.success) {
        return { success: false, error: workspacesResult.error };
      }
      const workspaces = workspacesResult.data;
      const workspaceIndex = workspaces.findIndex((w) => w.id === id);
      if (workspaceIndex === -1) {
        return {
          success: false,
          error: {
            code: ERROR_CODES.WORKSPACE_NOT_FOUND,
            message: `Workspace with id ${id} not found`
          }
        };
      }
      workspaces.splice(workspaceIndex, 1);
      return await WorkspaceStorage.saveWorkspaces(workspaces);
    } catch (error) {
      return {
        success: false,
        error: {
          code: ERROR_CODES.STORAGE_ERROR,
          message: "Failed to delete workspace",
          details: error
        }
      };
    }
  }
  /**
   * 查找工作区
   */
  static async findWorkspaces(predicate) {
    try {
      const workspacesResult = await WorkspaceStorage.getWorkspaces();
      if (!workspacesResult.success) {
        return { success: false, error: workspacesResult.error };
      }
      const workspaces = workspacesResult.data;
      const matchedWorkspaces = workspaces.filter(predicate);
      return { success: true, data: matchedWorkspaces };
    } catch (error) {
      return {
        success: false,
        error: {
          code: ERROR_CODES.STORAGE_ERROR,
          message: "Failed to find workspaces",
          details: error
        }
      };
    }
  }
  /**
   * 获取工作区统计信息
   */
  static async getWorkspaceStats() {
    try {
      const workspacesResult = await WorkspaceStorage.getWorkspaces();
      if (!workspacesResult.success) {
        return { success: false, error: workspacesResult.error };
      }
      const workspaces = workspacesResult.data;
      const totalWorkspaces = workspaces.length;
      const totalWebsites = workspaces.reduce((sum, ws) => sum + ws.websites.length, 0);
      const activeWorkspaces = workspaces.filter((ws) => ws.isActive).length;
      const averageWebsitesPerWorkspace = totalWorkspaces > 0 ? totalWebsites / totalWorkspaces : 0;
      return {
        success: true,
        data: {
          totalWorkspaces,
          totalWebsites,
          activeWorkspaces,
          averageWebsitesPerWorkspace
        }
      };
    } catch (error) {
      return {
        success: false,
        error: {
          code: ERROR_CODES.STORAGE_ERROR,
          message: "Failed to get workspace stats",
          details: error
        }
      };
    }
  }
  /**
   * 验证工作区数据完整性
   */
  static validateWorkspaces(workspaces) {
    return workspaces.map((workspace) => {
      const validatedWorkspace = {
        id: workspace.id || `ws_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
        name: workspace.name || "Untitled Workspace",
        icon: workspace.icon || "📁",
        color: workspace.color || "#3B82F6",
        websites: workspace.websites || [],
        createdAt: workspace.createdAt || Date.now(),
        updatedAt: workspace.updatedAt || Date.now(),
        isActive: workspace.isActive || false,
        order: workspace.order || 0,
        // 可选字段
        description: workspace.description,
        position: workspace.position,
        windowId: workspace.windowId,
        userTabsHidden: workspace.userTabsHidden,
        hiddenUserTabIds: workspace.hiddenUserTabIds,
        hiddenTabIds: workspace.hiddenTabIds,
        pinnedTabIds: workspace.pinnedTabIds,
        type: workspace.type,
        pos: workspace.pos,
        state: workspace.state,
        workonaTabIds: workspace.workonaTabIds,
        sessionId: workspace.sessionId,
        tabOrder: workspace.tabOrder
      };
      validatedWorkspace.websites = workspace.websites?.map((website) => ({
        id: website.id || `site_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
        url: website.url || "",
        title: website.title || "Untitled Website",
        favicon: website.favicon || "/icons/default-favicon.png",
        isPinned: website.isPinned || false,
        addedAt: website.addedAt || Date.now(),
        order: website.order || 0
      })) || [];
      return validatedWorkspace;
    });
  }
  /**
   * 压缩工作区数据（移除不必要的字段）
   */
  static compressWorkspaceData(workspaces) {
    return workspaces.map((workspace) => {
      const compressed = {
        id: workspace.id,
        name: workspace.name,
        icon: workspace.icon,
        color: workspace.color,
        websites: workspace.websites,
        createdAt: workspace.createdAt,
        updatedAt: workspace.updatedAt,
        isActive: workspace.isActive,
        order: workspace.order
      };
      if (workspace.description) compressed.description = workspace.description;
      if (workspace.position !== void 0) compressed.position = workspace.position;
      if (workspace.windowId) compressed.windowId = workspace.windowId;
      if (workspace.userTabsHidden) compressed.userTabsHidden = workspace.userTabsHidden;
      if (workspace.hiddenUserTabIds?.length) compressed.hiddenUserTabIds = workspace.hiddenUserTabIds;
      if (workspace.hiddenTabIds?.length) compressed.hiddenTabIds = workspace.hiddenTabIds;
      if (workspace.pinnedTabIds?.length) compressed.pinnedTabIds = workspace.pinnedTabIds;
      if (workspace.type) compressed.type = workspace.type;
      if (workspace.pos !== void 0) compressed.pos = workspace.pos;
      if (workspace.state) compressed.state = workspace.state;
      if (workspace.workonaTabIds?.length) compressed.workonaTabIds = workspace.workonaTabIds;
      if (workspace.sessionId) compressed.sessionId = workspace.sessionId;
      if (workspace.tabOrder?.length) compressed.tabOrder = workspace.tabOrder;
      return compressed;
    });
  }
}

class WorkonaStorage {
  /**
   * 保存标签页ID映射
   */
  static async saveTabIdMappings(mappings) {
    try {
      await chrome.storage.local.set({
        [WORKONA_STORAGE_KEYS.TAB_ID_MAPPINGS]: mappings
      });
      return { success: true };
    } catch (error) {
      return {
        success: false,
        error: {
          code: ERROR_CODES.STORAGE_ERROR,
          message: "Failed to save tab ID mappings",
          details: error
        }
      };
    }
  }
  /**
   * 获取标签页ID映射
   */
  static async getTabIdMappings() {
    try {
      const result = await chrome.storage.local.get(WORKONA_STORAGE_KEYS.TAB_ID_MAPPINGS);
      const mappings = result[WORKONA_STORAGE_KEYS.TAB_ID_MAPPINGS] || [];
      return { success: true, data: mappings };
    } catch (error) {
      return {
        success: false,
        error: {
          code: ERROR_CODES.STORAGE_ERROR,
          message: "Failed to get tab ID mappings",
          details: error
        }
      };
    }
  }
  /**
   * 保存本地打开工作区
   */
  static async saveLocalOpenWorkspaces(workspaces) {
    try {
      await chrome.storage.local.set({
        [WORKONA_STORAGE_KEYS.LOCAL_OPEN_WORKSPACES]: workspaces
      });
      return { success: true };
    } catch (error) {
      return {
        success: false,
        error: {
          code: ERROR_CODES.STORAGE_ERROR,
          message: "Failed to save local open workspaces",
          details: error
        }
      };
    }
  }
  /**
   * 获取本地打开工作区
   */
  static async getLocalOpenWorkspaces() {
    try {
      const result = await chrome.storage.local.get(WORKONA_STORAGE_KEYS.LOCAL_OPEN_WORKSPACES);
      const workspaces = result[WORKONA_STORAGE_KEYS.LOCAL_OPEN_WORKSPACES] || {};
      return { success: true, data: workspaces };
    } catch (error) {
      return {
        success: false,
        error: {
          code: ERROR_CODES.STORAGE_ERROR,
          message: "Failed to get local open workspaces",
          details: error
        }
      };
    }
  }
  /**
   * 保存标签页组
   */
  static async saveTabGroups(tabGroups) {
    try {
      await chrome.storage.local.set({
        [WORKONA_STORAGE_KEYS.TAB_GROUPS]: tabGroups
      });
      return { success: true };
    } catch (error) {
      return {
        success: false,
        error: {
          code: ERROR_CODES.STORAGE_ERROR,
          message: "Failed to save tab groups",
          details: error
        }
      };
    }
  }
  /**
   * 获取标签页组
   */
  static async getTabGroups() {
    try {
      const result = await chrome.storage.local.get(WORKONA_STORAGE_KEYS.TAB_GROUPS);
      const tabGroups = result[WORKONA_STORAGE_KEYS.TAB_GROUPS] || {};
      return { success: true, data: tabGroups };
    } catch (error) {
      return {
        success: false,
        error: {
          code: ERROR_CODES.STORAGE_ERROR,
          message: "Failed to get tab groups",
          details: error
        }
      };
    }
  }
  /**
   * 保存工作区会话
   */
  static async saveWorkspaceSessions(sessions) {
    try {
      await chrome.storage.local.set({
        [WORKONA_STORAGE_KEYS.WORKSPACE_SESSIONS]: sessions
      });
      return { success: true };
    } catch (error) {
      return {
        success: false,
        error: {
          code: ERROR_CODES.STORAGE_ERROR,
          message: "Failed to save workspace sessions",
          details: error
        }
      };
    }
  }
  /**
   * 获取工作区会话
   */
  static async getWorkspaceSessions() {
    try {
      const result = await chrome.storage.local.get(WORKONA_STORAGE_KEYS.WORKSPACE_SESSIONS);
      const sessions = result[WORKONA_STORAGE_KEYS.WORKSPACE_SESSIONS] || {};
      return { success: true, data: sessions };
    } catch (error) {
      return {
        success: false,
        error: {
          code: ERROR_CODES.STORAGE_ERROR,
          message: "Failed to get workspace sessions",
          details: error
        }
      };
    }
  }
  /**
   * 保存全局工作区窗口ID
   */
  static async saveGlobalWorkspaceWindowId(windowId) {
    try {
      await chrome.storage.local.set({
        [WORKONA_STORAGE_KEYS.GLOBAL_WORKSPACE_WINDOW_ID]: windowId
      });
      return { success: true };
    } catch (error) {
      return {
        success: false,
        error: {
          code: ERROR_CODES.STORAGE_ERROR,
          message: "Failed to save global workspace window ID",
          details: error
        }
      };
    }
  }
  /**
   * 获取全局工作区窗口ID
   */
  static async getGlobalWorkspaceWindowId() {
    try {
      const result = await chrome.storage.local.get(WORKONA_STORAGE_KEYS.GLOBAL_WORKSPACE_WINDOW_ID);
      const windowId = result[WORKONA_STORAGE_KEYS.GLOBAL_WORKSPACE_WINDOW_ID] || null;
      return { success: true, data: windowId };
    } catch (error) {
      return {
        success: false,
        error: {
          code: ERROR_CODES.STORAGE_ERROR,
          message: "Failed to get global workspace window ID",
          details: error
        }
      };
    }
  }
  /**
   * 清除全局工作区窗口ID
   */
  static async clearGlobalWorkspaceWindowId() {
    try {
      await chrome.storage.local.remove(WORKONA_STORAGE_KEYS.GLOBAL_WORKSPACE_WINDOW_ID);
      return { success: true };
    } catch (error) {
      return {
        success: false,
        error: {
          code: ERROR_CODES.STORAGE_ERROR,
          message: "Failed to clear global workspace window ID",
          details: error
        }
      };
    }
  }
  /**
   * 保存数据版本
   */
  static async saveDataVersion(version) {
    try {
      await chrome.storage.local.set({
        [WORKONA_STORAGE_KEYS.DATA_VERSION]: version
      });
      return { success: true };
    } catch (error) {
      return {
        success: false,
        error: {
          code: ERROR_CODES.STORAGE_ERROR,
          message: "Failed to save data version",
          details: error
        }
      };
    }
  }
  /**
   * 获取数据版本
   */
  static async getDataVersion() {
    try {
      const result = await chrome.storage.local.get(WORKONA_STORAGE_KEYS.DATA_VERSION);
      const version = result[WORKONA_STORAGE_KEYS.DATA_VERSION] || "0.0.0";
      return { success: true, data: version };
    } catch (error) {
      return {
        success: false,
        error: {
          code: ERROR_CODES.STORAGE_ERROR,
          message: "Failed to get data version",
          details: error
        }
      };
    }
  }
  /**
   * 清理过期的标签页映射
   */
  static async cleanupExpiredTabMappings(maxAge = 7 * 24 * 60 * 60 * 1e3) {
    try {
      const mappingsResult = await WorkonaStorage.getTabIdMappings();
      if (!mappingsResult.success) {
        return { success: false, error: mappingsResult.error };
      }
      const mappings = mappingsResult.data;
      const now = Date.now();
      const validMappings = mappings.filter((mapping) => {
        const age = now - mapping.createdAt;
        return age <= maxAge;
      });
      const removedCount = mappings.length - validMappings.length;
      if (removedCount > 0) {
        const saveResult = await WorkonaStorage.saveTabIdMappings(validMappings);
        if (!saveResult.success) {
          return { success: false, error: saveResult.error };
        }
      }
      return { success: true, data: removedCount };
    } catch (error) {
      return {
        success: false,
        error: {
          code: ERROR_CODES.STORAGE_ERROR,
          message: "Failed to cleanup expired tab mappings",
          details: error
        }
      };
    }
  }
  /**
   * 获取Workona存储统计信息
   */
  static async getWorkonaStorageStats() {
    try {
      const [
        tabMappingsResult,
        localWorkspacesResult,
        tabGroupsResult,
        sessionsResult,
        globalWindowResult,
        versionResult
      ] = await Promise.all([
        WorkonaStorage.getTabIdMappings(),
        WorkonaStorage.getLocalOpenWorkspaces(),
        WorkonaStorage.getTabGroups(),
        WorkonaStorage.getWorkspaceSessions(),
        WorkonaStorage.getGlobalWorkspaceWindowId(),
        WorkonaStorage.getDataVersion()
      ]);
      return {
        success: true,
        data: {
          tabMappingsCount: tabMappingsResult.success ? tabMappingsResult.data.length : 0,
          localWorkspacesCount: localWorkspacesResult.success ? Object.keys(localWorkspacesResult.data).length : 0,
          tabGroupsCount: tabGroupsResult.success ? Object.keys(tabGroupsResult.data).length : 0,
          sessionsCount: sessionsResult.success ? Object.keys(sessionsResult.data).length : 0,
          hasGlobalWindowId: globalWindowResult.success && globalWindowResult.data !== null,
          dataVersion: versionResult.success ? versionResult.data : "0.0.0"
        }
      };
    } catch (error) {
      return {
        success: false,
        error: {
          code: ERROR_CODES.STORAGE_ERROR,
          message: "Failed to get Workona storage stats",
          details: error
        }
      };
    }
  }
}

class ImportDataProcessor {
  /**
   * 处理导入数据后的自动映射补全
   */
  static async processImportedData(importData) {
    try {
      console.log("🔄 开始处理导入数据的系统映射补全...");
      await this.completeWorkspaceWorkonaFields(importData.workspaces);
      await this.generateTabIdMappings(importData.workspaces);
      await this.generateWorkspaceSessions(importData.workspaces);
      await this.initializeSystemMappings();
      console.log("✅ 导入数据系统映射补全完成");
      return { success: true };
    } catch (error) {
      console.error("❌ 导入数据处理失败:", error);
      return {
        success: false,
        error: {
          code: ERROR_CODES.STORAGE_ERROR,
          message: "Failed to process imported data",
          details: error
        }
      };
    }
  }
  /**
   * 补全工作区的 Workona 风格字段
   */
  static async completeWorkspaceWorkonaFields(workspaces) {
    console.log("📝 补全工作区 Workona 风格字段...");
    const updatedWorkspaces = workspaces.map((workspace, index) => {
      const updatedWorkspace = {
        ...workspace,
        // 如果缺少 Workona 字段，自动补全
        type: workspace.type || "saved",
        pos: workspace.pos || Date.now() + index,
        state: workspace.state || "inactive",
        workonaTabIds: workspace.workonaTabIds || [],
        sessionId: workspace.sessionId || `session_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
        tabOrder: workspace.tabOrder || [],
        isActive: false
        // 导入后所有工作区都不激活
      };
      if (!updatedWorkspace.workonaTabIds || updatedWorkspace.workonaTabIds.length === 0 && updatedWorkspace.websites.length > 0) {
        updatedWorkspace.workonaTabIds = updatedWorkspace.websites.map(
          (website) => `t-${updatedWorkspace.id}-${website.id}`
        );
      }
      if (!updatedWorkspace.tabOrder || updatedWorkspace.tabOrder.length === 0 && updatedWorkspace.websites.length > 0) {
        updatedWorkspace.tabOrder = updatedWorkspace.websites.map((website) => website.id);
      }
      return updatedWorkspace;
    });
    await StorageManager.saveWorkspaces(updatedWorkspaces);
    console.log(`✅ 已补全 ${updatedWorkspaces.length} 个工作区的 Workona 字段`);
  }
  /**
   * 生成标签页ID映射
   */
  static async generateTabIdMappings(workspaces) {
    console.log("🔗 生成标签页ID映射...");
    const tabIdMappings = [];
    for (const workspace of workspaces) {
      for (const website of workspace.websites) {
        const workonaId = `t-${workspace.id}-${website.id}`;
        const mapping = {
          workonaId,
          chromeId: -1,
          // 导入时没有实际的 Chrome 标签页ID，使用-1表示无效
          workspaceId: workspace.id,
          websiteId: website.id,
          isWorkspaceCore: true,
          // 工作区网站默认为核心标签页
          tabType: "core",
          // 添加必需的tabType字段
          createdAt: Date.now(),
          lastSyncAt: Date.now(),
          // 添加必需的lastSyncAt字段
          metadata: {
            source: "workspace_website",
            addedToWorkspaceAt: Date.now(),
            isPinned: website.isPinned || false,
            pinnedAt: website.isPinned ? Date.now() : void 0
          }
        };
        tabIdMappings.push(mapping);
      }
    }
    await StorageManager.saveTabIdMappings(tabIdMappings);
    console.log(`✅ 已生成 ${tabIdMappings.length} 个标签页ID映射`);
  }
  /**
   * 生成工作区会话数据
   */
  static async generateWorkspaceSessions(workspaces) {
    console.log("📊 生成工作区会话数据...");
    const workspaceSessions = {};
    for (const workspace of workspaces) {
      const session = {
        workspaceId: workspace.id,
        tabs: {},
        // 空的标签页映射，导入时没有实际标签页
        tabOrder: workspace.tabOrder || workspace.websites.map((w) => w.id),
        activeTabId: workspace.websites.length > 0 ? workspace.websites[0].id : void 0,
        lastActiveAt: Date.now(),
        windowId: void 0
        // 导入时没有实际的窗口ID
      };
      workspaceSessions[workspace.id] = session;
    }
    await StorageManager.saveWorkspaceSessions(workspaceSessions);
    console.log(`✅ 已生成 ${Object.keys(workspaceSessions).length} 个工作区会话`);
  }
  /**
   * 初始化其他系统映射
   */
  static async initializeSystemMappings() {
    console.log("🔧 初始化其他系统映射...");
    await StorageManager.saveLocalOpenWorkspaces({});
    await StorageManager.saveTabGroups({});
    await StorageManager.clearGlobalWorkspaceWindowId();
    await StorageManager.saveDataVersion("2.0.0");
    console.log("✅ 系统映射初始化完成");
  }
  /**
   * 验证导入数据的完整性
   */
  static validateImportedData(importData) {
    const errors = [];
    if (!importData.workspaces || !Array.isArray(importData.workspaces)) {
      errors.push("缺少工作区数据或格式错误");
    }
    if (importData.workspaces) {
      for (let i = 0; i < importData.workspaces.length; i++) {
        const workspace = importData.workspaces[i];
        if (!workspace.id) {
          errors.push(`工作区 ${i + 1} 缺少ID`);
        }
        if (!workspace.name) {
          errors.push(`工作区 ${i + 1} 缺少名称`);
        }
        if (!workspace.websites || !Array.isArray(workspace.websites)) {
          errors.push(`工作区 ${i + 1} 缺少网站数据或格式错误`);
        } else {
          for (let j = 0; j < workspace.websites.length; j++) {
            const website = workspace.websites[j];
            if (!website.id) {
              errors.push(`工作区 ${i + 1} 的网站 ${j + 1} 缺少ID`);
            }
            if (!website.url) {
              errors.push(`工作区 ${i + 1} 的网站 ${j + 1} 缺少URL`);
            }
          }
        }
      }
    }
    return {
      isValid: errors.length === 0,
      errors
    };
  }
  /**
   * 清理导入数据中的无效字段
   */
  static cleanImportData(importData) {
    const cleanedData = { ...importData };
    if (cleanedData.workspaces) {
      cleanedData.workspaces = cleanedData.workspaces.map((workspace) => ({
        ...workspace,
        isActive: false,
        // 确保导入后所有工作区都不激活
        // 移除可能导致冲突的字段
        windowId: void 0,
        lastActiveAt: void 0
      }));
    }
    cleanedData.activeWorkspaceId = null;
    cleanedData.globalWorkspaceWindowId = null;
    return cleanedData;
  }
}

class StorageImportExport {
  /**
   * 获取所有存储数据
   */
  static async getAllData() {
    try {
      const result = await chrome.storage.local.get([
        STORAGE_KEYS.WORKSPACES,
        STORAGE_KEYS.SETTINGS,
        STORAGE_KEYS.ACTIVE_WORKSPACE_ID,
        STORAGE_KEYS.LAST_ACTIVE_WORKSPACE_IDS,
        // Workona 风格数据
        WORKONA_STORAGE_KEYS.TAB_ID_MAPPINGS,
        WORKONA_STORAGE_KEYS.LOCAL_OPEN_WORKSPACES,
        WORKONA_STORAGE_KEYS.TAB_GROUPS,
        WORKONA_STORAGE_KEYS.WORKSPACE_SESSIONS,
        WORKONA_STORAGE_KEYS.GLOBAL_WORKSPACE_WINDOW_ID,
        WORKONA_STORAGE_KEYS.DATA_VERSION
      ]);
      const data = {
        workspaces: result[STORAGE_KEYS.WORKSPACES] || [],
        settings: { ...DEFAULT_SETTINGS, ...result[STORAGE_KEYS.SETTINGS] },
        activeWorkspaceId: result[STORAGE_KEYS.ACTIVE_WORKSPACE_ID] || null,
        lastActiveWorkspaceIds: result[STORAGE_KEYS.LAST_ACTIVE_WORKSPACE_IDS] || [],
        // Workona 风格扩展数据（恢复原始默认值处理）
        tabIdMappings: result[WORKONA_STORAGE_KEYS.TAB_ID_MAPPINGS] || [],
        localOpenWorkspaces: result[WORKONA_STORAGE_KEYS.LOCAL_OPEN_WORKSPACES] || {},
        tabGroups: result[WORKONA_STORAGE_KEYS.TAB_GROUPS] || {},
        workspaceSessions: result[WORKONA_STORAGE_KEYS.WORKSPACE_SESSIONS] || {},
        globalWorkspaceWindowId: result[WORKONA_STORAGE_KEYS.GLOBAL_WORKSPACE_WINDOW_ID] || void 0,
        dataVersion: result[WORKONA_STORAGE_KEYS.DATA_VERSION] || "1.0.0"
      };
      return { success: true, data };
    } catch (error) {
      return {
        success: false,
        error: {
          code: ERROR_CODES.STORAGE_ERROR,
          message: "Failed to get all data",
          details: error
        }
      };
    }
  }
  /**
   * 导出数据为JSON字符串
   */
  static async exportData() {
    try {
      const dataResult = await StorageImportExport.getAllData();
      if (!dataResult.success) {
        return { success: false, error: dataResult.error };
      }
      const data = dataResult.data;
      const exportData = {
        version: "1.0.0",
        exportedAt: Date.now(),
        data: {
          workspaces: data.workspaces,
          settings: data.settings,
          activeWorkspaceId: data.activeWorkspaceId,
          lastActiveWorkspaceIds: data.lastActiveWorkspaceIds,
          // 可选的Workona数据
          ...data.tabIdMappings && { tabIdMappings: data.tabIdMappings },
          ...data.localOpenWorkspaces && { localOpenWorkspaces: data.localOpenWorkspaces },
          ...data.tabGroups && { tabGroups: data.tabGroups },
          ...data.workspaceSessions && { workspaceSessions: data.workspaceSessions },
          ...data.globalWorkspaceWindowId && { globalWorkspaceWindowId: data.globalWorkspaceWindowId },
          ...data.dataVersion && { dataVersion: data.dataVersion }
        }
      };
      const jsonString = JSON.stringify(exportData, null, 2);
      return { success: true, data: jsonString };
    } catch (error) {
      return {
        success: false,
        error: {
          code: ERROR_CODES.EXPORT_ERROR,
          message: "Failed to export data",
          details: error
        }
      };
    }
  }
  /**
   * 从JSON字符串导入数据
   */
  static async importData(jsonData) {
    try {
      const importData = JSON.parse(jsonData);
      if (!importData.data || !importData.data.workspaces) {
        return {
          success: false,
          error: {
            code: ERROR_CODES.INVALID_DATA_FORMAT,
            message: "Invalid import data format"
          }
        };
      }
      const processResult = await ImportDataProcessor.processImportedData(importData.data);
      if (!processResult.success) {
        return { success: false, error: processResult.error };
      }
      const existingWorkspacesResult = await WorkspaceStorage.getWorkspaces();
      if (!existingWorkspacesResult.success) {
        return { success: false, error: existingWorkspacesResult.error };
      }
      const existingWorkspaces = existingWorkspacesResult.data;
      const mergeResult = await StorageImportExport.performIncrementalImport(
        existingWorkspaces,
        importData.data.workspaces || []
      );
      if (!mergeResult.success) {
        return { success: false, error: mergeResult.error };
      }
      const saveResult = await WorkspaceStorage.saveWorkspaces(mergeResult.data);
      if (!saveResult.success) {
        return { success: false, error: saveResult.error };
      }
      if (importData.data.settings) {
        const settingsResult = await StorageCore.saveSettings(importData.data.settings);
        if (!settingsResult.success) {
          console.warn("导入设置失败:", settingsResult.error);
        }
      }
      if (importData.data.tabIdMappings) {
        await WorkonaStorage.saveTabIdMappings(importData.data.tabIdMappings);
      }
      if (importData.data.localOpenWorkspaces) {
        await WorkonaStorage.saveLocalOpenWorkspaces(importData.data.localOpenWorkspaces);
      }
      if (importData.data.tabGroups) {
        await WorkonaStorage.saveTabGroups(importData.data.tabGroups);
      }
      if (importData.data.workspaceSessions) {
        await WorkonaStorage.saveWorkspaceSessions(importData.data.workspaceSessions);
      }
      if (importData.data.dataVersion) {
        await WorkonaStorage.saveDataVersion(importData.data.dataVersion);
      }
      return { success: true };
    } catch (error) {
      return {
        success: false,
        error: {
          code: ERROR_CODES.IMPORT_ERROR,
          message: "Failed to import data",
          details: error
        }
      };
    }
  }
  /**
   * 执行增量导入（合并现有数据和导入数据）
   */
  static async performIncrementalImport(existingWorkspaces, importedWorkspaces) {
    try {
      const mergedWorkspaces = [...existingWorkspaces];
      for (const importedWorkspace of importedWorkspaces) {
        const existingIndex = mergedWorkspaces.findIndex(
          (ws) => ws.name === importedWorkspace.name
        );
        if (existingIndex >= 0) {
          const existingWorkspace = mergedWorkspaces[existingIndex];
          const mergedWebsites = [...existingWorkspace.websites];
          for (const importedWebsite of importedWorkspace.websites) {
            const websiteExists = mergedWebsites.some(
              (ws) => ws.url === importedWebsite.url
            );
            if (!websiteExists) {
              mergedWebsites.push({
                ...importedWebsite,
                id: `site_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
                addedAt: Date.now(),
                order: mergedWebsites.length
              });
            }
          }
          mergedWorkspaces[existingIndex] = {
            ...existingWorkspace,
            websites: mergedWebsites,
            updatedAt: Date.now()
          };
        } else {
          const newWorkspace = {
            ...importedWorkspace,
            id: `ws_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
            isActive: false,
            createdAt: Date.now(),
            updatedAt: Date.now(),
            order: mergedWorkspaces.length,
            websites: importedWorkspace.websites.map((website, index) => ({
              ...website,
              id: `site_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
              addedAt: Date.now(),
              order: index
            }))
          };
          mergedWorkspaces.push(newWorkspace);
        }
      }
      return { success: true, data: mergedWorkspaces };
    } catch (error) {
      return {
        success: false,
        error: {
          code: ERROR_CODES.IMPORT_ERROR,
          message: "Failed to perform incremental import",
          details: error
        }
      };
    }
  }
  /**
   * 创建数据备份
   */
  static async createBackup() {
    try {
      const exportResult = await StorageImportExport.exportData();
      if (!exportResult.success) {
        return { success: false, error: exportResult.error };
      }
      const backupData = {
        type: "backup",
        createdAt: Date.now(),
        data: exportResult.data
      };
      return { success: true, data: JSON.stringify(backupData, null, 2) };
    } catch (error) {
      return {
        success: false,
        error: {
          code: ERROR_CODES.EXPORT_ERROR,
          message: "Failed to create backup",
          details: error
        }
      };
    }
  }
  /**
   * 从备份恢复数据
   */
  static async restoreFromBackup(backupData) {
    try {
      const backup = JSON.parse(backupData);
      if (backup.type !== "backup" || !backup.data) {
        return {
          success: false,
          error: {
            code: ERROR_CODES.INVALID_DATA_FORMAT,
            message: "Invalid backup data format"
          }
        };
      }
      return await StorageImportExport.importData(backup.data);
    } catch (error) {
      return {
        success: false,
        error: {
          code: ERROR_CODES.IMPORT_ERROR,
          message: "Failed to restore from backup",
          details: error
        }
      };
    }
  }
  /**
   * 渐进式数据迁移检查（恢复原始功能）
   * 检测现有数据格式，逐步迁移到 Workona 格式
   */
  static async migrateToWorkonaFormat() {
    try {
      console.log("🔄 开始检查 Workona 数据迁移需求...");
      const versionResult = await WorkonaStorage.getDataVersion();
      if (!versionResult.success) {
        return { success: false, error: versionResult.error };
      }
      const currentVersion = versionResult.data;
      const targetVersion = "1.0.0";
      if (currentVersion === targetVersion) {
        console.log("✅ 数据版本已是最新，无需迁移");
        return { success: true, data: false };
      }
      console.log(`📦 检测到数据版本 ${currentVersion}，开始迁移到 ${targetVersion}...`);
      const allDataResult = await this.getAllData();
      if (!allDataResult.success) {
        return { success: false, error: allDataResult.error };
      }
      const data = allDataResult.data;
      if (!data.tabIdMappings || data.tabIdMappings.length === 0) {
        await WorkonaStorage.saveTabIdMappings([]);
      }
      if (!data.localOpenWorkspaces || Object.keys(data.localOpenWorkspaces).length === 0) {
        await WorkonaStorage.saveLocalOpenWorkspaces({});
      }
      if (!data.tabGroups || Object.keys(data.tabGroups).length === 0) {
        await WorkonaStorage.saveTabGroups({});
      }
      if (!data.workspaceSessions || Object.keys(data.workspaceSessions).length === 0) {
        await WorkonaStorage.saveWorkspaceSessions({});
      }
      await WorkonaStorage.saveDataVersion(targetVersion);
      console.log("✅ Workona 数据迁移完成");
      return { success: true, data: true };
    } catch (error) {
      console.error("❌ Workona 数据迁移失败:", error);
      return {
        success: false,
        error: {
          code: ERROR_CODES.STORAGE_ERROR,
          message: "Failed to migrate to Workona format",
          details: error
        }
      };
    }
  }
}

class StorageManager {
  // ==================== 基础存储操作 ====================
  /**
   * 获取所有存储数据
   */
  static async getAllData() {
    return StorageImportExport.getAllData();
  }
  /**
   * 获取设置
   */
  static async getSettings() {
    return StorageCore.getSettings();
  }
  /**
   * 保存设置
   */
  static async saveSettings(settings) {
    return StorageCore.saveSettings(settings);
  }
  /**
   * 获取活跃工作区ID
   */
  static async getActiveWorkspaceId() {
    return StorageCore.getActiveWorkspaceId();
  }
  /**
   * 设置活跃工作区ID
   */
  static async setActiveWorkspaceId(id) {
    return StorageCore.setActiveWorkspaceId(id);
  }
  /**
   * 更新最近活跃工作区列表
   */
  static async updateLastActiveWorkspaces(workspaceId) {
    return StorageCore.updateLastActiveWorkspaces(workspaceId);
  }
  /**
   * 清除所有存储数据
   */
  static async clearAll() {
    return StorageCore.clearAll();
  }
  // ==================== 工作区存储操作 ====================
  /**
   * 获取所有工作区
   */
  static async getWorkspaces() {
    return WorkspaceStorage.getWorkspaces();
  }
  /**
   * 保存工作区列表
   */
  static async saveWorkspaces(workspaces) {
    return WorkspaceStorage.saveWorkspaces(workspaces);
  }
  /**
   * 获取单个工作区
   */
  static async getWorkspace(id) {
    return WorkspaceStorage.getWorkspace(id);
  }
  /**
   * 更新单个工作区
   */
  static async updateWorkspace(updatedWorkspace) {
    return WorkspaceStorage.updateWorkspace(updatedWorkspace);
  }
  // ==================== Workona存储操作 ====================
  /**
   * 保存标签页ID映射
   */
  static async saveTabIdMappings(mappings) {
    return WorkonaStorage.saveTabIdMappings(mappings);
  }
  /**
   * 获取标签页ID映射
   */
  static async getTabIdMappings() {
    return WorkonaStorage.getTabIdMappings();
  }
  /**
   * 保存本地打开工作区
   */
  static async saveLocalOpenWorkspaces(workspaces) {
    return WorkonaStorage.saveLocalOpenWorkspaces(workspaces);
  }
  /**
   * 获取本地打开工作区
   */
  static async getLocalOpenWorkspaces() {
    return WorkonaStorage.getLocalOpenWorkspaces();
  }
  /**
   * 保存标签页组
   */
  static async saveTabGroups(tabGroups) {
    return WorkonaStorage.saveTabGroups(tabGroups);
  }
  /**
   * 获取标签页组
   */
  static async getTabGroups() {
    return WorkonaStorage.getTabGroups();
  }
  /**
   * 保存工作区会话
   */
  static async saveWorkspaceSessions(sessions) {
    return WorkonaStorage.saveWorkspaceSessions(sessions);
  }
  /**
   * 获取工作区会话
   */
  static async getWorkspaceSessions() {
    return WorkonaStorage.getWorkspaceSessions();
  }
  /**
   * 保存全局工作区窗口ID
   */
  static async saveGlobalWorkspaceWindowId(windowId) {
    return WorkonaStorage.saveGlobalWorkspaceWindowId(windowId);
  }
  /**
   * 获取全局工作区窗口ID
   */
  static async getGlobalWorkspaceWindowId() {
    return WorkonaStorage.getGlobalWorkspaceWindowId();
  }
  /**
   * 清除全局工作区窗口ID
   */
  static async clearGlobalWorkspaceWindowId() {
    return WorkonaStorage.clearGlobalWorkspaceWindowId();
  }
  /**
   * 保存数据版本
   */
  static async saveDataVersion(version) {
    return WorkonaStorage.saveDataVersion(version);
  }
  /**
   * 获取数据版本
   */
  static async getDataVersion() {
    return WorkonaStorage.getDataVersion();
  }
  // ==================== 导入导出操作 ====================
  /**
   * 导出数据为JSON字符串
   */
  static async exportData() {
    return StorageImportExport.exportData();
  }
  /**
   * 从JSON字符串导入数据
   */
  static async importData(jsonData) {
    return StorageImportExport.importData(jsonData);
  }
  // ==================== 工具方法 ====================
  /**
   * 添加存储变化监听器
   */
  static onChanged(callback) {
    return StorageCore.onChanged(callback);
  }
  /**
   * 获取存储使用情况
   */
  static async getStorageUsage() {
    return StorageCore.getStorageUsage();
  }
  /**
   * 检查存储键是否存在
   */
  static async hasKey(key) {
    return StorageCore.hasKey(key);
  }
  /**
   * 删除指定的存储键
   */
  static async removeKey(key) {
    return StorageCore.removeKey(key);
  }
  // ==================== 迁移相关（向后兼容） ====================
  /**
   * 迁移到Workona格式（恢复原始功能）
   */
  static async migrateToWorkonaFormat() {
    return StorageImportExport.migrateToWorkonaFormat();
  }
}

const scriptRel = 'modulepreload';const assetsURL = function(dep) { return "/"+dep };const seen = {};const __vitePreload = function preload(baseModule, deps, importerUrl) {
  let promise = Promise.resolve();
  if (true && deps && deps.length > 0) {
    document.getElementsByTagName("link");
    const cspNonceMeta = document.querySelector(
      "meta[property=csp-nonce]"
    );
    const cspNonce = cspNonceMeta?.nonce || cspNonceMeta?.getAttribute("nonce");
    promise = Promise.allSettled(
      deps.map((dep) => {
        dep = assetsURL(dep);
        if (dep in seen) return;
        seen[dep] = true;
        const isCss = dep.endsWith(".css");
        const cssSelector = isCss ? '[rel="stylesheet"]' : "";
        if (document.querySelector(`link[href="${dep}"]${cssSelector}`)) {
          return;
        }
        const link = document.createElement("link");
        link.rel = isCss ? "stylesheet" : scriptRel;
        if (!isCss) {
          link.as = "script";
        }
        link.crossOrigin = "";
        link.href = dep;
        if (cspNonce) {
          link.setAttribute("nonce", cspNonce);
        }
        document.head.appendChild(link);
        if (isCss) {
          return new Promise((res, rej) => {
            link.addEventListener("load", res);
            link.addEventListener(
              "error",
              () => rej(new Error(`Unable to preload CSS for ${dep}`))
            );
          });
        }
      })
    );
  }
  function handlePreloadError(err) {
    const e = new Event("vite:preloadError", {
      cancelable: true
    });
    e.payload = err;
    window.dispatchEvent(e);
    if (!e.defaultPrevented) {
      throw err;
    }
  }
  return promise.then((res) => {
    for (const item of res || []) {
      if (item.status !== "rejected") continue;
      handlePreloadError(item.reason);
    }
    return baseModule().catch(handlePreloadError);
  });
};

class WorkspaceStateSync {
  /**
   * 发送工作区状态更新事件
   */
  static sendWorkspaceStateUpdate(workspaceId, eventType) {
    try {
      if (typeof window !== "undefined") {
        const eventName = eventType === "switch" ? "workspaceSwitchComplete" : "userTabsVisibilityChanged";
        const event = new CustomEvent(eventName, {
          detail: { workspaceId }
        });
        window.dispatchEvent(event);
      }
      if (typeof chrome !== "undefined" && chrome.runtime) {
        const messageType = eventType === "switch" ? "WORKSPACE_SWITCH_COMPLETE" : "USER_TABS_VISIBILITY_CHANGED";
        chrome.runtime.sendMessage({
          type: messageType,
          workspaceId
        }).catch((error) => {
          console.log(`发送${eventType}事件消息失败:`, error);
        });
      }
    } catch (error) {
      console.error(`发送工作区状态更新事件失败:`, error);
    }
  }
  /**
   * 添加工作区状态监听器
   */
  static addStateListener(callback) {
    const handleWorkspaceSwitchComplete = (event) => {
      callback(event.detail.workspaceId, "switch");
    };
    const handleUserTabsVisibilityChanged = (event) => {
      callback(event.detail.workspaceId, "userTabsVisibility");
    };
    const handleChromeMessage = (message) => {
      if (message.type === "WORKSPACE_SWITCH_COMPLETE") {
        callback(message.workspaceId, "switch");
      } else if (message.type === "USER_TABS_VISIBILITY_CHANGED") {
        callback(message.workspaceId, "userTabsVisibility");
      }
    };
    if (typeof window !== "undefined") {
      window.addEventListener("workspaceSwitchComplete", handleWorkspaceSwitchComplete);
      window.addEventListener("userTabsVisibilityChanged", handleUserTabsVisibilityChanged);
    }
    if (typeof chrome !== "undefined" && chrome.runtime) {
      chrome.runtime.onMessage.addListener(handleChromeMessage);
    }
    return () => {
      if (typeof window !== "undefined") {
        window.removeEventListener("workspaceSwitchComplete", handleWorkspaceSwitchComplete);
        window.removeEventListener("userTabsVisibilityChanged", handleUserTabsVisibilityChanged);
      }
      if (typeof chrome !== "undefined" && chrome.runtime) {
        chrome.runtime.onMessage.removeListener(handleChromeMessage);
      }
    };
  }
}

class WorkspaceSessionManager {
  static currentSession = null;
  static isSessionSwitching = false;
  /**
   * 获取当前会话
   */
  static getCurrentSession() {
    return this.currentSession;
  }
  /**
   * 切换工作区会话
   * 复用现有的 WorkspaceStateSync 事件系统
   */
  static async switchSession(workspaceId, options = {}) {
    try {
      if (this.isSessionSwitching) {
        console.log("⏳ 会话切换正在进行中，跳过重复请求");
        return { success: true };
      }
      this.isSessionSwitching = true;
      console.log(`🔄 开始切换到工作区会话: ${workspaceId}`);
      if (this.currentSession && options.preserveCurrentSession !== false) {
        const saveResult = await this.saveSession(this.currentSession);
        if (!saveResult.success) {
          console.warn("保存当前会话失败:", saveResult.error);
        }
      }
      const loadResult = await this.loadSession(workspaceId);
      if (!loadResult.success) {
        this.isSessionSwitching = false;
        return { success: false, error: loadResult.error };
      }
      this.currentSession = loadResult.data;
      WorkspaceStateSync.sendWorkspaceStateUpdate(workspaceId, "switch");
      if (options.restoreTabOrder !== false && this.currentSession.tabOrder.length > 0) {
        await this.restoreTabOrder(this.currentSession);
      }
      if (!this.currentSession.activeTabId && this.currentSession.tabOrder.length > 0) {
        console.log("📋 没有保存的活跃标签页，智能选择合适的标签页激活");
        let targetTabId = null;
        for (const workonaId of this.currentSession.tabOrder) {
          const metadataResult = await WorkonaTabManager.getTabMetadata(workonaId);
          if (metadataResult.success && metadataResult.data?.isWorkspaceCore) {
            targetTabId = workonaId;
            console.log(`🎯 选择工作区核心标签页: ${workonaId}`);
            break;
          }
        }
        if (!targetTabId && this.currentSession.tabOrder.length > 0) {
          targetTabId = this.currentSession.tabOrder[0];
          console.log(`🎯 选择第一个标签页: ${targetTabId}`);
        }
        if (targetTabId) {
          const chromeIdResult = await WorkonaTabManager.getChromeIdByWorkonaId(targetTabId);
          if (chromeIdResult.success && chromeIdResult.data) {
            try {
              await chrome.tabs.update(chromeIdResult.data, { active: true });
              console.log(`✨ 智能激活标签页: ${targetTabId}`);
              this.currentSession.activeTabId = targetTabId;
              await this.saveSession(this.currentSession);
            } catch (error) {
              console.warn("智能激活标签页失败:", error);
            }
          }
        }
      }
      console.log(`✅ 成功切换到工作区会话: ${workspaceId}`);
      return { success: true };
    } catch (error) {
      return {
        success: false,
        error: {
          code: ERROR_CODES.TAB_ERROR,
          message: "Failed to switch workspace session",
          details: error
        }
      };
    } finally {
      this.isSessionSwitching = false;
    }
  }
  /**
   * 获取当前工作区的标签页
   * 确保会话只包含当前工作区的标签页
   */
  static getCurrentWorkspaceTabs() {
    if (!this.currentSession) {
      return [];
    }
    return Object.values(this.currentSession.tabs);
  }
  /**
   * 获取当前工作区的标签页顺序
   */
  static getCurrentTabOrder() {
    if (!this.currentSession) {
      return [];
    }
    return [...this.currentSession.tabOrder];
  }
  /**
   * 更新会话中的标签页
   */
  static async updateSessionTab(workonaId, tab) {
    try {
      if (!this.currentSession) {
        return {
          success: false,
          error: {
            code: ERROR_CODES.TAB_ERROR,
            message: "No active session"
          }
        };
      }
      const existingTab = this.currentSession.tabs[workonaId];
      this.currentSession.tabs[workonaId] = {
        ...existingTab,
        // 保持现有属性
        ...tab
        // 应用更新
      };
      this.currentSession.lastActiveAt = Date.now();
      if (!this.currentSession.tabOrder.includes(workonaId)) {
        this.currentSession.tabOrder.push(workonaId);
        console.log(`📝 添加新标签页到会话顺序: ${workonaId}`);
      } else {
        console.log(`🔄 更新现有标签页会话信息: ${workonaId}`);
      }
      const saveResult = await this.saveSession(this.currentSession);
      if (!saveResult.success) {
        return { success: false, error: saveResult.error };
      }
      return { success: true };
    } catch (error) {
      return {
        success: false,
        error: {
          code: ERROR_CODES.TAB_ERROR,
          message: "Failed to update session tab",
          details: error
        }
      };
    }
  }
  /**
   * 从会话中移除标签页
   */
  static async removeSessionTab(workonaId) {
    try {
      if (!this.currentSession) {
        return { success: true };
      }
      delete this.currentSession.tabs[workonaId];
      this.currentSession.tabOrder = this.currentSession.tabOrder.filter((id) => id !== workonaId);
      if (this.currentSession.activeTabId === workonaId) {
        this.currentSession.activeTabId = void 0;
      }
      this.currentSession.lastActiveAt = Date.now();
      const saveResult = await this.saveSession(this.currentSession);
      if (!saveResult.success) {
        return { success: false, error: saveResult.error };
      }
      console.log(`🗑️ 从会话中移除标签页: ${workonaId}`);
      return { success: true };
    } catch (error) {
      return {
        success: false,
        error: {
          code: ERROR_CODES.TAB_ERROR,
          message: "Failed to remove session tab",
          details: error
        }
      };
    }
  }
  /**
   * 设置当前激活的标签页
   */
  static async setActiveTab(workonaId) {
    try {
      if (!this.currentSession) {
        return {
          success: false,
          error: {
            code: ERROR_CODES.TAB_ERROR,
            message: "No active session"
          }
        };
      }
      this.currentSession.activeTabId = workonaId;
      this.currentSession.lastActiveAt = Date.now();
      if (this.currentSession.tabs[workonaId]) {
        this.currentSession.tabs[workonaId].lastActiveAt = Date.now();
      }
      const saveResult = await this.saveSession(this.currentSession);
      if (!saveResult.success) {
        return { success: false, error: saveResult.error };
      }
      console.log(`📍 记录活跃标签页: ${workonaId} (工作区: ${this.currentSession.workspaceId})`);
      return { success: true };
    } catch (error) {
      return {
        success: false,
        error: {
          code: ERROR_CODES.TAB_ERROR,
          message: "Failed to set active tab",
          details: error
        }
      };
    }
  }
  /**
   * 实时同步当前工作区的标签页状态
   * 记录标签页顺序和活跃状态
   */
  static async syncCurrentWorkspaceState() {
    try {
      if (!this.currentSession) {
        return { success: true };
      }
      console.log(`🔄 同步工作区标签页状态: ${this.currentSession.workspaceId}`);
      const currentWindow = await chrome.windows.getCurrent();
      const allTabs = await chrome.tabs.query({ windowId: currentWindow.id });
      const workonaTabIds = await WorkonaTabManager.getWorkspaceWorkonaTabIds(this.currentSession.workspaceId);
      if (!workonaTabIds.success) {
        return { success: false, error: workonaTabIds.error };
      }
      const currentWorkspaceTabs = [];
      let activeWorkonaId;
      for (const workonaId of workonaTabIds.data) {
        const chromeIdResult = await WorkonaTabManager.getChromeIdByWorkonaId(workonaId);
        if (chromeIdResult.success && chromeIdResult.data) {
          const chromeTab = allTabs.find((tab) => tab.id === chromeIdResult.data);
          if (chromeTab) {
            currentWorkspaceTabs.push({
              workonaId,
              chromeTab,
              index: chromeTab.index
            });
            if (chromeTab.active) {
              activeWorkonaId = workonaId;
            }
          }
        }
      }
      currentWorkspaceTabs.sort((a, b) => a.index - b.index);
      const newTabOrder = currentWorkspaceTabs.map((item) => item.workonaId);
      let hasChanges = false;
      if (JSON.stringify(this.currentSession.tabOrder) !== JSON.stringify(newTabOrder)) {
        this.currentSession.tabOrder = newTabOrder;
        hasChanges = true;
        console.log(`📋 更新标签页顺序: [${newTabOrder.join(", ")}]`);
      }
      if (activeWorkonaId && this.currentSession.activeTabId !== activeWorkonaId) {
        this.currentSession.activeTabId = activeWorkonaId;
        hasChanges = true;
        const metadataResult = await WorkonaTabManager.getTabMetadata(activeWorkonaId);
        const isWorkspaceCore = metadataResult.success && metadataResult.data?.isWorkspaceCore;
        console.log(`📍 更新活跃标签页: ${activeWorkonaId} ${isWorkspaceCore ? "(工作区专用)" : "(用户标签页)"}`);
      }
      if (hasChanges) {
        this.currentSession.lastActiveAt = Date.now();
        const saveResult = await this.saveSession(this.currentSession);
        if (!saveResult.success) {
          return { success: false, error: saveResult.error };
        }
      }
      return { success: true };
    } catch (error) {
      return {
        success: false,
        error: {
          code: ERROR_CODES.TAB_ERROR,
          message: "Failed to sync workspace state",
          details: error
        }
      };
    }
  }
  /**
   * 更新标签页顺序
   */
  static async updateTabOrder(newOrder) {
    try {
      if (!this.currentSession) {
        return {
          success: false,
          error: {
            code: ERROR_CODES.TAB_ERROR,
            message: "No active session"
          }
        };
      }
      const validOrder = newOrder.filter((workonaId) => this.currentSession.tabs[workonaId]);
      this.currentSession.tabOrder = validOrder;
      this.currentSession.lastActiveAt = Date.now();
      const saveResult = await this.saveSession(this.currentSession);
      if (!saveResult.success) {
        return { success: false, error: saveResult.error };
      }
      console.log(`📋 更新标签页顺序: ${validOrder.length} 个标签页`);
      return { success: true };
    } catch (error) {
      return {
        success: false,
        error: {
          code: ERROR_CODES.TAB_ERROR,
          message: "Failed to update tab order",
          details: error
        }
      };
    }
  }
  /**
   * 清除当前会话状态
   * 用于浏览器重启后重置工作区状态
   */
  static async clearCurrentSession() {
    try {
      console.log("🔄 清除当前工作区会话状态");
      this.currentSession = null;
      this.isSessionSwitching = false;
      console.log("✅ 工作区会话状态已清除");
      return { success: true };
    } catch (error) {
      return {
        success: false,
        error: {
          code: ERROR_CODES.TAB_ERROR,
          message: "Failed to clear current session",
          details: error
        }
      };
    }
  }
  /**
   * 将新创建的标签页添加到当前会话
   * 用于后台标签页创建完成后的增量更新
   */
  static async addNewTabsToCurrentSession(newWorkonaIds) {
    try {
      if (!this.currentSession) {
        console.log("⚠️ 没有活跃会话，跳过添加新标签页");
        return { success: true };
      }
      if (newWorkonaIds.length === 0) {
        return { success: true };
      }
      console.log(`📝 添加 ${newWorkonaIds.length} 个新标签页到当前会话`);
      let addedCount = 0;
      for (const workonaId of newWorkonaIds) {
        if (!this.currentSession.tabOrder.includes(workonaId)) {
          this.currentSession.tabOrder.push(workonaId);
          addedCount++;
        }
      }
      if (addedCount > 0) {
        this.currentSession.lastActiveAt = Date.now();
        const saveResult = await this.saveSession(this.currentSession);
        if (!saveResult.success) {
          return { success: false, error: saveResult.error };
        }
        console.log(`✅ 成功添加 ${addedCount} 个新标签页到会话顺序`);
      } else {
        console.log("ℹ️ 所有新标签页都已存在于会话中");
      }
      return { success: true };
    } catch (error) {
      return {
        success: false,
        error: {
          code: ERROR_CODES.TAB_ERROR,
          message: "Failed to add new tabs to current session",
          details: error
        }
      };
    }
  }
  /**
   * 保存会话到存储
   */
  static async saveSession(session) {
    try {
      const sessionsResult = await StorageManager.getWorkspaceSessions();
      if (!sessionsResult.success) {
        return { success: false, error: sessionsResult.error };
      }
      const sessions = sessionsResult.data;
      sessions[session.workspaceId] = session;
      const saveResult = await StorageManager.saveWorkspaceSessions(sessions);
      if (!saveResult.success) {
        return { success: false, error: saveResult.error };
      }
      return { success: true };
    } catch (error) {
      return {
        success: false,
        error: {
          code: ERROR_CODES.STORAGE_ERROR,
          message: "Failed to save session",
          details: error
        }
      };
    }
  }
  /**
   * 从存储加载会话
   */
  static async loadSession(workspaceId) {
    try {
      const sessionsResult = await StorageManager.getWorkspaceSessions();
      if (!sessionsResult.success) {
        return { success: false, error: sessionsResult.error };
      }
      const sessions = sessionsResult.data;
      let session = sessions[workspaceId];
      if (!session) {
        session = {
          workspaceId,
          tabs: {},
          tabOrder: [],
          lastActiveAt: Date.now()
        };
        sessions[workspaceId] = session;
        const saveResult = await StorageManager.saveWorkspaceSessions(sessions);
        if (!saveResult.success) {
          return { success: false, error: saveResult.error };
        }
        console.log(`✨ 创建新的工作区会话: ${workspaceId}`);
      }
      return { success: true, data: session };
    } catch (error) {
      return {
        success: false,
        error: {
          code: ERROR_CODES.STORAGE_ERROR,
          message: "Failed to load session",
          details: error
        }
      };
    }
  }
  /**
   * 恢复标签页顺序和活跃状态
   */
  static async restoreTabOrder(session) {
    try {
      console.log(`🔄 恢复工作区状态: ${session.tabOrder.length} 个标签页`);
      if (session.tabOrder.length === 0) {
        console.log("📋 没有保存的标签页顺序，跳过恢复");
        return;
      }
      const currentWindow = await chrome.windows.getCurrent();
      const allTabs = await chrome.tabs.query({ windowId: currentWindow.id });
      const tabsToReorder = [];
      for (let i = 0; i < session.tabOrder.length; i++) {
        const workonaId = session.tabOrder[i];
        const chromeIdResult = await WorkonaTabManager.getChromeIdByWorkonaId(workonaId);
        if (chromeIdResult.success && chromeIdResult.data) {
          const chromeTab = allTabs.find((tab) => tab.id === chromeIdResult.data);
          if (chromeTab) {
            tabsToReorder.push({
              workonaId,
              chromeId: chromeTab.id,
              targetIndex: i
            });
          }
        }
      }
      let targetActiveTabId = null;
      if (session.activeTabId) {
        const activeTabResult = await WorkonaTabManager.getChromeIdByWorkonaId(session.activeTabId);
        if (activeTabResult.success && activeTabResult.data) {
          const targetTab = allTabs.find((tab) => tab.id === activeTabResult.data);
          if (targetTab) {
            targetActiveTabId = activeTabResult.data;
            console.log(`🎯 确定目标活跃标签页: ${session.activeTabId} (Chrome ID: ${targetActiveTabId})`);
          }
        }
      }
      console.log(`📋 重新排列 ${tabsToReorder.length} 个标签页`);
      const movePromises = tabsToReorder.map(async (tabInfo) => {
        try {
          await chrome.tabs.move(tabInfo.chromeId, { index: tabInfo.targetIndex });
          console.log(`📍 移动标签页 ${tabInfo.workonaId} 到位置 ${tabInfo.targetIndex}`);
        } catch (error) {
          console.warn(`⚠️ 移动标签页 ${tabInfo.workonaId} 失败:`, error);
        }
      });
      await Promise.all(movePromises);
      console.log("✅ 所有标签页移动操作完成");
      if (targetActiveTabId) {
        try {
          const metadataResult = await WorkonaTabManager.getTabMetadata(session.activeTabId);
          const isWorkspaceCore = metadataResult.success && metadataResult.data?.isWorkspaceCore;
          await chrome.tabs.update(targetActiveTabId, { active: true });
          console.log(`✨ 立即激活目标标签页: ${session.activeTabId} ${isWorkspaceCore ? "(工作区专用)" : "(用户标签页)"}`);
          const currentWindow2 = await chrome.windows.getCurrent();
          if (currentWindow2.id) {
            chrome.windows.update(currentWindow2.id, { focused: true });
          }
          console.log(`🎯 工作区标签页状态恢复完成，无闪烁切换`);
        } catch (error) {
          console.warn(`⚠️ 激活目标标签页失败:`, error);
        }
      } else {
        console.log("📋 没有保存的活跃标签页，智能选择合适的标签页");
        if (session.tabOrder.length > 0) {
          let bestTabId = null;
          for (const workonaId of session.tabOrder) {
            const chromeIdResult = await WorkonaTabManager.getChromeIdByWorkonaId(workonaId);
            if (chromeIdResult.success && chromeIdResult.data) {
              const metadataResult = await WorkonaTabManager.getTabMetadata(workonaId);
              if (metadataResult.success && metadataResult.data?.isWorkspaceCore) {
                bestTabId = workonaId;
                break;
              }
              if (!bestTabId) {
                bestTabId = workonaId;
              }
            }
          }
          if (bestTabId) {
            const chromeIdResult = await WorkonaTabManager.getChromeIdByWorkonaId(bestTabId);
            if (chromeIdResult.success && chromeIdResult.data) {
              try {
                await chrome.tabs.update(chromeIdResult.data, { active: true });
                console.log(`✨ 智能激活标签页: ${bestTabId}`);
              } catch (error) {
                console.warn("智能激活标签页失败:", error);
              }
            }
          }
        }
      }
      console.log("✅ 工作区状态恢复完成");
    } catch (error) {
      console.warn("恢复工作区状态失败:", error);
    }
  }
  /**
   * 清理工作区会话
   */
  static async clearWorkspaceSession(workspaceId) {
    try {
      const sessionsResult = await StorageManager.getWorkspaceSessions();
      if (!sessionsResult.success) {
        return { success: false, error: sessionsResult.error };
      }
      const sessions = sessionsResult.data;
      delete sessions[workspaceId];
      const saveResult = await StorageManager.saveWorkspaceSessions(sessions);
      if (!saveResult.success) {
        return { success: false, error: saveResult.error };
      }
      if (this.currentSession && this.currentSession.workspaceId === workspaceId) {
        this.currentSession = null;
      }
      console.log(`🗑️ 清理工作区会话: ${workspaceId}`);
      return { success: true };
    } catch (error) {
      return {
        success: false,
        error: {
          code: ERROR_CODES.STORAGE_ERROR,
          message: "Failed to clear workspace session",
          details: error
        }
      };
    }
  }
}

class WorkonaTabManager {
  /**
   * 生成 Workona 风格标签页ID
   * 格式：t-{workspaceId}-{uuid}
   */
  static generateWorkonaTabId(workspaceId) {
    const uuid = "xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx".replace(/[xy]/g, function(c) {
      const r = Math.random() * 16 | 0;
      const v = c === "x" ? r : r & 3 | 8;
      return v.toString(16);
    });
    return `t-${workspaceId}-${uuid}`;
  }
  /**
   * 创建标签页ID映射关系（增强版：支持元数据分类）
   *
   * 🎯 核心功能：
   * 1. 建立Chrome标签页ID与Workona ID的双向映射关系
   * 2. 支持标签页分类（核心标签页 vs 会话标签页）
   * 3. 记录标签页来源和元数据信息
   * 4. 提供标签页生命周期管理基础
   *
   * 📋 映射类型：
   * - 核心标签页：工作区配置中的网站，持久化存储
   * - 会话标签页：用户临时打开的标签页，会话级存储
   *
   * 🏷️ 标签页来源：
   * - workspace_website：来自工作区网站配置
   * - user_opened：用户主动打开的标签页
   * - session_restored：会话恢复的标签页
   *
   * 💾 存储策略：
   * - 核心标签页：存储到持久化映射表
   * - 会话标签页：存储到会话映射表
   * - 元数据：记录创建时间、来源、关联信息
   *
   * @param workonaId Workona格式的标签页ID (t-{workspaceId}-{uuid})
   * @param chromeId Chrome浏览器的标签页ID
   * @param workspaceId 所属工作区ID
   * @param websiteId 关联的网站配置ID（可选）
   * @param options 额外选项和元数据
   * @returns 创建的映射关系对象
   */
  static async createTabIdMapping(workonaId, chromeId, workspaceId, websiteId, options) {
    try {
      const isWorkspaceCore = options?.isWorkspaceCore ?? !!websiteId;
      const tabType = isWorkspaceCore ? "core" : "session";
      const source = options?.source ?? (websiteId ? "workspace_website" : "user_opened");
      console.log(`🆔 创建标签页映射: ${workonaId} -> Chrome ID ${chromeId} (类型: ${tabType}, 来源: ${source})`);
      const mapping = {
        workonaId,
        chromeId,
        workspaceId,
        websiteId,
        createdAt: Date.now(),
        lastSyncAt: Date.now(),
        // 标签页分类元数据
        isWorkspaceCore,
        tabType,
        metadata: {
          source,
          // 标签页来源
          sessionId: options?.sessionId,
          // 会话ID（如果适用）
          originalUrl: options?.originalUrl,
          // 原始URL（用于恢复）
          addedToWorkspaceAt: isWorkspaceCore ? Date.now() : void 0,
          // 添加到工作区的时间
          isPinned: false,
          // 初始化为未固定状态
          pinnedAt: void 0,
          // 固定时间
          unpinnedAt: void 0
          // 取消固定时间
        }
      };
      const mappingsResult = await StorageManager.getTabIdMappings();
      if (!mappingsResult.success) {
        return { success: false, error: mappingsResult.error };
      }
      const mappings = mappingsResult.data;
      const existingIndex = mappings.findIndex(
        (m) => m.workonaId === workonaId || m.chromeId === chromeId
      );
      if (existingIndex >= 0) {
        const existing = mappings[existingIndex];
        console.log(`🔄 更新现有映射: ${existing.workonaId} -> ${chromeId}`);
        mappings[existingIndex] = {
          ...mapping,
          createdAt: existing.createdAt,
          // 保留原始创建时间
          metadata: {
            ...existing.metadata,
            ...mapping.metadata,
            source: mapping.metadata?.source || existing.metadata?.source || "user_opened",
            addedToWorkspaceAt: existing.metadata?.addedToWorkspaceAt || mapping.metadata?.addedToWorkspaceAt
          }
        };
      } else {
        console.log(`➕ 添加新映射: ${workonaId} -> ${chromeId}`);
        mappings.push(mapping);
      }
      const saveResult = await StorageManager.saveTabIdMappings(mappings);
      if (!saveResult.success) {
        return { success: false, error: saveResult.error };
      }
      try {
        const tab = await chrome.tabs.get(chromeId);
        const isPinned = tab.pinned;
        await chrome.scripting.executeScript({
          target: { tabId: chromeId },
          func: (workonaId2, workspaceId2, websiteId2, isWorkspaceCore2, isPinned2) => {
            const workonaData = {
              workonaId: workonaId2,
              workspaceId: workspaceId2,
              websiteId: websiteId2,
              isWorkspaceCore: isWorkspaceCore2,
              isPinned: isPinned2,
              timestamp: Date.now()
            };
            sessionStorage.setItem("workonaData", JSON.stringify(workonaData));
            console.log(`📝 标签页会话存储 Workona 数据:`, workonaData);
          },
          args: [workonaId, workspaceId, websiteId || "", isWorkspaceCore, isPinned]
        });
      } catch (error) {
        console.warn(`⚠️ 无法为标签页 ${chromeId} 设置会话存储:`, error);
      }
      console.log(`✅ 创建标签页ID映射: ${workonaId} <-> ${chromeId} (类型: ${tabType}, 核心: ${isWorkspaceCore})`);
      return { success: true, data: mapping };
    } catch (error) {
      return {
        success: false,
        error: {
          code: ERROR_CODES.TAB_ERROR,
          message: "Failed to create tab ID mapping",
          details: error
        }
      };
    }
  }
  /**
   * 根据 Chrome ID 获取 Workona ID
   */
  static async getWorkonaIdByChromeId(chromeId) {
    try {
      const mappingsResult = await StorageManager.getTabIdMappings();
      if (!mappingsResult.success) {
        return { success: false, error: mappingsResult.error };
      }
      const mappings = mappingsResult.data;
      const mapping = mappings.find((m) => m.chromeId === chromeId);
      return { success: true, data: mapping?.workonaId || null };
    } catch (error) {
      return {
        success: false,
        error: {
          code: ERROR_CODES.TAB_ERROR,
          message: "Failed to get Workona ID by Chrome ID",
          details: error
        }
      };
    }
  }
  /**
   * 根据 Workona ID 获取 Chrome ID
   */
  static async getChromeIdByWorkonaId(workonaId) {
    try {
      const mappingsResult = await StorageManager.getTabIdMappings();
      if (!mappingsResult.success) {
        return { success: false, error: mappingsResult.error };
      }
      const mappings = mappingsResult.data;
      const mapping = mappings.find((m) => m.workonaId === workonaId);
      return { success: true, data: mapping?.chromeId || null };
    } catch (error) {
      return {
        success: false,
        error: {
          code: ERROR_CODES.TAB_ERROR,
          message: "Failed to get Chrome ID by Workona ID",
          details: error
        }
      };
    }
  }
  /**
   * 同步标签页映射关系
   * 清理无效的映射，更新现有映射的同步时间
   */
  static async syncTabMappings() {
    try {
      console.log("🔄 开始同步标签页映射关系...");
      const mappingsResult = await StorageManager.getTabIdMappings();
      if (!mappingsResult.success) {
        return { success: false, error: mappingsResult.error };
      }
      const mappings = mappingsResult.data;
      const validMappings = [];
      let cleanedCount = 0;
      const allTabs = await chrome.tabs.query({});
      const existingChromeIds = new Set(allTabs.map((tab) => tab.id));
      for (const mapping of mappings) {
        if (existingChromeIds.has(mapping.chromeId)) {
          mapping.lastSyncAt = Date.now();
          validMappings.push(mapping);
        } else {
          console.log(`🗑️ 清理无效映射: ${mapping.workonaId} <-> ${mapping.chromeId}`);
          cleanedCount++;
        }
      }
      const saveResult = await StorageManager.saveTabIdMappings(validMappings);
      if (!saveResult.success) {
        return { success: false, error: saveResult.error };
      }
      console.log(`✅ 标签页映射同步完成，清理了 ${cleanedCount} 个无效映射`);
      return { success: true, data: cleanedCount };
    } catch (error) {
      return {
        success: false,
        error: {
          code: ERROR_CODES.TAB_ERROR,
          message: "Failed to sync tab mappings",
          details: error
        }
      };
    }
  }
  /**
   * 删除标签页映射
   */
  static async removeTabMapping(workonaId) {
    try {
      const mappingsResult = await StorageManager.getTabIdMappings();
      if (!mappingsResult.success) {
        return { success: false, error: mappingsResult.error };
      }
      const mappings = mappingsResult.data;
      const filteredMappings = mappings.filter((m) => m.workonaId !== workonaId);
      const saveResult = await StorageManager.saveTabIdMappings(filteredMappings);
      if (!saveResult.success) {
        return { success: false, error: saveResult.error };
      }
      console.log(`🗑️ 删除标签页映射: ${workonaId}`);
      return { success: true };
    } catch (error) {
      return {
        success: false,
        error: {
          code: ERROR_CODES.TAB_ERROR,
          message: "Failed to remove tab mapping",
          details: error
        }
      };
    }
  }
  /**
   * 获取工作区的所有 Workona 标签页ID
   */
  static async getWorkspaceWorkonaTabIds(workspaceId) {
    try {
      const mappingsResult = await StorageManager.getTabIdMappings();
      if (!mappingsResult.success) {
        return { success: false, error: mappingsResult.error };
      }
      const mappings = mappingsResult.data;
      const workonaIds = mappings.filter((m) => m.workspaceId === workspaceId).map((m) => m.workonaId);
      return { success: true, data: workonaIds };
    } catch (error) {
      return {
        success: false,
        error: {
          code: ERROR_CODES.TAB_ERROR,
          message: "Failed to get workspace Workona tab IDs",
          details: error
        }
      };
    }
  }
  /**
   * 批量清理工作区的标签页映射
   */
  static async clearWorkspaceTabMappings(workspaceId) {
    try {
      const mappingsResult = await StorageManager.getTabIdMappings();
      if (!mappingsResult.success) {
        return { success: false, error: mappingsResult.error };
      }
      const mappings = mappingsResult.data;
      const remainingMappings = mappings.filter((m) => m.workspaceId !== workspaceId);
      const clearedCount = mappings.length - remainingMappings.length;
      const saveResult = await StorageManager.saveTabIdMappings(remainingMappings);
      if (!saveResult.success) {
        return { success: false, error: saveResult.error };
      }
      console.log(`🗑️ 清理工作区 ${workspaceId} 的 ${clearedCount} 个标签页映射`);
      return { success: true, data: clearedCount };
    } catch (error) {
      return {
        success: false,
        error: {
          code: ERROR_CODES.TAB_ERROR,
          message: "Failed to clear workspace tab mappings",
          details: error
        }
      };
    }
  }
  // === 概念性重构：标签页元数据管理方法 ===
  /**
   * 获取标签页元数据
   */
  static async getTabMetadata(workonaId) {
    try {
      const mappingsResult = await StorageManager.getTabIdMappings();
      if (!mappingsResult.success) {
        return { success: false, error: mappingsResult.error };
      }
      const mappings = mappingsResult.data;
      const mapping = mappings.find((m) => m.workonaId === workonaId);
      return { success: true, data: mapping || null };
    } catch (error) {
      return {
        success: false,
        error: {
          code: ERROR_CODES.TAB_ERROR,
          message: "Failed to get tab metadata",
          details: error
        }
      };
    }
  }
  /**
   * 更新标签页元数据
   */
  static async updateTabMetadata(workonaId, updates) {
    try {
      const mappingsResult = await StorageManager.getTabIdMappings();
      if (!mappingsResult.success) {
        return { success: false, error: mappingsResult.error };
      }
      const mappings = mappingsResult.data;
      const mappingIndex = mappings.findIndex((m) => m.workonaId === workonaId);
      if (mappingIndex === -1) {
        return {
          success: false,
          error: {
            code: ERROR_CODES.TAB_ERROR,
            message: "Tab mapping not found"
          }
        };
      }
      const existingMapping = mappings[mappingIndex];
      const updatedMapping = {
        ...existingMapping,
        ...updates,
        lastSyncAt: Date.now(),
        metadata: {
          ...existingMapping.metadata,
          ...updates.metadata,
          // 确保 source 字段始终有值
          source: updates.metadata?.source || existingMapping.metadata?.source || "user_opened"
        }
      };
      mappings[mappingIndex] = updatedMapping;
      const saveResult = await StorageManager.saveTabIdMappings(mappings);
      if (!saveResult.success) {
        return { success: false, error: saveResult.error };
      }
      console.log(`📝 更新标签页元数据: ${workonaId} (核心: ${updatedMapping.isWorkspaceCore})`);
      return { success: true, data: updatedMapping };
    } catch (error) {
      return {
        success: false,
        error: {
          code: ERROR_CODES.TAB_ERROR,
          message: "Failed to update tab metadata",
          details: error
        }
      };
    }
  }
  /**
   * 将会话临时标签页转换为工作区核心标签页
   */
  static async promoteToWorkspaceCore(workonaId, websiteId) {
    try {
      const updates = {
        isWorkspaceCore: true,
        tabType: "core",
        websiteId,
        metadata: {
          source: "workspace_website",
          addedToWorkspaceAt: Date.now()
        }
      };
      const result = await this.updateTabMetadata(workonaId, updates);
      if (result.success) {
        console.log(`⬆️ 标签页提升为工作区核心: ${workonaId}`);
        try {
          const workspaceId = workonaId.split("-")[1];
          await UserTabsRealTimeMonitor.triggerImmediateStateCheck();
          await UserTabsRealTimeMonitor.forceRefreshWorkspaceState(workspaceId);
          console.log(`🔄 已触发工作区 ${workspaceId} 用户标签页状态更新 (标签页提升)`);
        } catch (updateError) {
          console.warn("触发用户标签页状态更新失败:", updateError);
        }
      }
      return result;
    } catch (error) {
      return {
        success: false,
        error: {
          code: ERROR_CODES.TAB_ERROR,
          message: "Failed to promote tab to workspace core",
          details: error
        }
      };
    }
  }
  /**
   * 将工作区核心标签页降级为会话临时标签页
   * 当用户从工作区中移除某个工作区专属标签页时调用
   */
  static async demoteToSessionTab(workonaId) {
    try {
      const metadataResult = await this.getTabMetadata(workonaId);
      if (!metadataResult.success || !metadataResult.data) {
        return {
          success: false,
          error: {
            code: ERROR_CODES.TAB_ERROR,
            message: "Tab metadata not found for demotion"
          }
        };
      }
      const currentMetadata = metadataResult.data;
      if (!currentMetadata.isWorkspaceCore) {
        console.log(`⚠️ 标签页 ${workonaId} 已经是会话临时标签页，无需降级`);
        return { success: true, data: currentMetadata };
      }
      const updates = {
        isWorkspaceCore: false,
        tabType: "session",
        websiteId: void 0,
        // 移除与工作区网站的关联
        metadata: {
          ...currentMetadata.metadata,
          source: "user_opened",
          addedToWorkspaceAt: void 0,
          // 移除工作区添加时间
          demotedAt: Date.now(),
          // 记录降级时间
          originalWebsiteId: currentMetadata.websiteId
          // 保留原始网站ID用于追踪
        }
      };
      const result = await this.updateTabMetadata(workonaId, updates);
      if (result.success) {
        console.log(`⬇️ 标签页降级为会话临时标签页: ${workonaId} (原网站ID: ${currentMetadata.websiteId})`);
        try {
          const workspaceId = workonaId.split("-")[1];
          await UserTabsRealTimeMonitor.triggerImmediateStateCheck();
          await UserTabsRealTimeMonitor.forceRefreshWorkspaceState(workspaceId);
          console.log(`🔄 已触发工作区 ${workspaceId} 用户标签页状态更新 (标签页降级)`);
        } catch (updateError) {
          console.warn("触发用户标签页状态更新失败:", updateError);
        }
      }
      return result;
    } catch (error) {
      return {
        success: false,
        error: {
          code: ERROR_CODES.TAB_ERROR,
          message: "Failed to demote tab to session tab",
          details: error
        }
      };
    }
  }
  /**
   * 同步标签页编辑后的状态
   * 确保编辑标签页后不会破坏 Workona ID 映射关系
   */
  static async syncTabAfterEdit(chromeId, newUrl, newTitle) {
    try {
      const workonaIdResult = await this.getWorkonaIdByChromeId(chromeId);
      if (!workonaIdResult.success || !workonaIdResult.data) {
        return { success: true };
      }
      const workonaId = workonaIdResult.data;
      const metadataResult = await this.getTabMetadata(workonaId);
      if (!metadataResult.success || !metadataResult.data) {
        console.warn(`无法获取标签页元数据: ${workonaId}`);
        return { success: true };
      }
      const currentMetadata = metadataResult.data;
      if (newUrl && newUrl !== currentMetadata.metadata?.originalUrl) {
        const updates = {
          metadata: {
            ...currentMetadata.metadata,
            source: currentMetadata.metadata?.source || "user_opened",
            originalUrl: newUrl,
            // 记录编辑时间
            lastEditedAt: Date.now()
          }
        };
        const updateResult = await this.updateTabMetadata(workonaId, updates);
        if (updateResult.success) {
          console.log(`🔄 同步标签页编辑: ${workonaId} (新URL: ${newUrl})`);
        }
      }
      const currentSession = WorkspaceSessionManager.getCurrentSession();
      if (currentSession && currentSession.tabs[workonaId]) {
        const updatedTab = {
          ...currentSession.tabs[workonaId],
          url: newUrl || currentSession.tabs[workonaId].url,
          title: newTitle || currentSession.tabs[workonaId].title,
          lastUpdated: Date.now()
        };
        await WorkspaceSessionManager.updateSessionTab(workonaId, updatedTab);
        console.log(`📝 同步标签页到会话: ${workonaId}`);
      }
      return { success: true };
    } catch (error) {
      return {
        success: false,
        error: {
          code: ERROR_CODES.TAB_ERROR,
          message: "Failed to sync tab after edit",
          details: error
        }
      };
    }
  }
  /**
   * 检查标签页是否为工作区核心标签页
   */
  static async isWorkspaceCore(chromeId) {
    try {
      const workonaIdResult = await this.getWorkonaIdByChromeId(chromeId);
      if (!workonaIdResult.success || !workonaIdResult.data) {
        return { success: true, data: false };
      }
      const metadataResult = await this.getTabMetadata(workonaIdResult.data);
      if (!metadataResult.success || !metadataResult.data) {
        return { success: true, data: false };
      }
      return { success: true, data: metadataResult.data.isWorkspaceCore };
    } catch (error) {
      return {
        success: false,
        error: {
          code: ERROR_CODES.TAB_ERROR,
          message: "Failed to check if tab is workspace core",
          details: error
        }
      };
    }
  }
  /**
   * 获取工作区的所有核心标签页 Workona ID
   */
  static async getWorkspaceCoreTabIds(workspaceId) {
    try {
      const mappingsResult = await StorageManager.getTabIdMappings();
      if (!mappingsResult.success) {
        return { success: false, error: mappingsResult.error };
      }
      const mappings = mappingsResult.data;
      const coreTabIds = mappings.filter((m) => m.workspaceId === workspaceId && m.isWorkspaceCore).map((m) => m.workonaId);
      return { success: true, data: coreTabIds };
    } catch (error) {
      return {
        success: false,
        error: {
          code: ERROR_CODES.TAB_ERROR,
          message: "Failed to get workspace core tab IDs",
          details: error
        }
      };
    }
  }
  /**
   * 获取工作区的所有会话临时标签页 Workona ID
   */
  static async getWorkspaceSessionTabIds(workspaceId) {
    try {
      const mappingsResult = await StorageManager.getTabIdMappings();
      if (!mappingsResult.success) {
        return { success: false, error: mappingsResult.error };
      }
      const mappings = mappingsResult.data;
      const sessionTabIds = mappings.filter((m) => m.workspaceId === workspaceId && !m.isWorkspaceCore).map((m) => m.workonaId);
      return { success: true, data: sessionTabIds };
    } catch (error) {
      return {
        success: false,
        error: {
          code: ERROR_CODES.TAB_ERROR,
          message: "Failed to get workspace session tab IDs",
          details: error
        }
      };
    }
  }
  /**
   * 检查工作区网站是否有对应的打开标签页
   */
  static async isWebsiteTabOpen(workspaceId, websiteId) {
    try {
      const mappingsResult = await StorageManager.getTabIdMappings();
      if (!mappingsResult.success) {
        return { success: false, error: mappingsResult.error };
      }
      const mappings = mappingsResult.data;
      const mapping = mappings.find(
        (m) => m.workspaceId === workspaceId && m.websiteId === websiteId && m.isWorkspaceCore
        // 只检查工作区核心标签页
      );
      if (!mapping) {
        return {
          success: true,
          data: { isOpen: false }
        };
      }
      try {
        const tab = await chrome.tabs.get(mapping.chromeId);
        if (tab) {
          return {
            success: true,
            data: {
              isOpen: true,
              chromeId: mapping.chromeId,
              workonaId: mapping.workonaId
            }
          };
        }
      } catch {
        await this.removeTabMapping(mapping.workonaId);
        console.log(`🗑️ 清理无效的标签页映射: ${mapping.workonaId}`);
      }
      return {
        success: true,
        data: { isOpen: false }
      };
    } catch (error) {
      return {
        success: false,
        error: {
          code: ERROR_CODES.TAB_ERROR,
          message: "Failed to check if website tab is open",
          details: error
        }
      };
    }
  }
  /**
   * 获取工作区所有网站的标签页状态
   */
  static async getWorkspaceWebsiteTabStates(workspaceId, websiteIds) {
    try {
      const result = {};
      for (const websiteId of websiteIds) {
        const statusResult = await this.isWebsiteTabOpen(workspaceId, websiteId);
        if (statusResult.success) {
          result[websiteId] = statusResult.data;
        } else {
          result[websiteId] = { isOpen: false };
        }
      }
      return { success: true, data: result };
    } catch (error) {
      return {
        success: false,
        error: {
          code: ERROR_CODES.TAB_ERROR,
          message: "Failed to get workspace website tab states",
          details: error
        }
      };
    }
  }
}

const workonaTabManager = /*#__PURE__*/Object.freeze(/*#__PURE__*/Object.defineProperty({
  __proto__: null,
  WorkonaTabManager
}, Symbol.toStringTag, { value: 'Module' }));

class TabManager {
  /**
   * 获取所有标签页信息
   */
  static async getAllTabs() {
    try {
      const tabs = await chrome.tabs.query({});
      const tabInfos = tabs.map((tab) => ({
        id: tab.id,
        url: tab.url || "",
        title: tab.title || "",
        favicon: tab.favIconUrl || "",
        isPinned: tab.pinned,
        isActive: tab.active,
        windowId: tab.windowId,
        index: tab.index
      }));
      return { success: true, data: tabInfos };
    } catch (error) {
      return {
        success: false,
        error: {
          code: ERROR_CODES.TAB_ERROR,
          message: "Failed to get tabs",
          details: error
        }
      };
    }
  }
  /**
   * 获取当前窗口的所有标签页
   */
  static async getCurrentWindowTabs() {
    try {
      const tabs = await chrome.tabs.query({ currentWindow: true });
      const tabInfos = tabs.map((tab) => ({
        id: tab.id,
        url: tab.url || "",
        title: tab.title || "",
        favicon: tab.favIconUrl || "",
        isPinned: tab.pinned,
        isActive: tab.active,
        windowId: tab.windowId,
        index: tab.index
      }));
      return { success: true, data: tabInfos };
    } catch (error) {
      return {
        success: false,
        error: {
          code: ERROR_CODES.TAB_ERROR,
          message: "Failed to get current window tabs",
          details: error
        }
      };
    }
  }
  /**
   * 获取当前活跃标签页
   */
  static async getActiveTab() {
    try {
      const tabs = await chrome.tabs.query({ active: true, currentWindow: true });
      if (tabs.length === 0) {
        return {
          success: false,
          error: {
            code: ERROR_CODES.TAB_ERROR,
            message: "No active tab found"
          }
        };
      }
      const tab = tabs[0];
      const tabInfo = {
        id: tab.id,
        url: tab.url || "",
        title: tab.title || "",
        favicon: tab.favIconUrl || "",
        isPinned: tab.pinned,
        isActive: tab.active,
        windowId: tab.windowId,
        index: tab.index
      };
      return { success: true, data: tabInfo };
    } catch (error) {
      return {
        success: false,
        error: {
          code: ERROR_CODES.TAB_ERROR,
          message: "Failed to get active tab",
          details: error
        }
      };
    }
  }
  /**
   * 通过Workona ID查找标签页
   */
  static async findTabByWorkonaId(workonaId) {
    try {
      const chromeIdResult = await WorkonaTabManager.getChromeIdByWorkonaId(workonaId);
      if (!chromeIdResult.success || !chromeIdResult.data) {
        return { success: true, data: null };
      }
      const chromeId = chromeIdResult.data;
      try {
        const tab = await chrome.tabs.get(chromeId);
        const tabInfo = {
          id: tab.id,
          url: tab.url || "",
          title: tab.title || "",
          favicon: tab.favIconUrl || "",
          isPinned: tab.pinned,
          isActive: tab.active,
          windowId: tab.windowId,
          index: tab.index
        };
        return { success: true, data: tabInfo };
      } catch (tabError) {
        await WorkonaTabManager.removeTabMapping(workonaId);
        return { success: true, data: null };
      }
    } catch (error) {
      return {
        success: false,
        error: {
          code: ERROR_CODES.TAB_ERROR,
          message: "Failed to find tab by Workona ID",
          details: error
        }
      };
    }
  }
  /**
   * 创建新标签页
   */
  static async createTab(url, pinned = false, active = true) {
    try {
      const tab = await chrome.tabs.create({
        url,
        pinned,
        active
      });
      const tabInfo = {
        id: tab.id,
        url: tab.url || url,
        title: tab.title || "",
        favicon: tab.favIconUrl || "",
        isPinned: tab.pinned || pinned,
        isActive: tab.active || active,
        windowId: tab.windowId,
        index: tab.index
      };
      return { success: true, data: tabInfo };
    } catch (error) {
      return {
        success: false,
        error: {
          code: ERROR_CODES.TAB_ERROR,
          message: "Failed to create tab",
          details: error
        }
      };
    }
  }
  /**
   * 激活指定标签页
   */
  static async activateTab(tabId) {
    try {
      await chrome.tabs.update(tabId, { active: true });
      return { success: true };
    } catch (error) {
      return {
        success: false,
        error: {
          code: ERROR_CODES.TAB_ERROR,
          message: "Failed to activate tab",
          details: error
        }
      };
    }
  }
  /**
   * 关闭指定标签页
   */
  static async closeTab(tabId) {
    try {
      await chrome.tabs.remove(tabId);
      return { success: true };
    } catch (error) {
      return {
        success: false,
        error: {
          code: ERROR_CODES.TAB_ERROR,
          message: "Failed to close tab",
          details: error
        }
      };
    }
  }
  /**
   * 批量关闭标签页
   */
  static async closeTabs(tabIds) {
    try {
      if (tabIds.length === 0) {
        return { success: true };
      }
      const validTabIds = tabIds.filter((id) => typeof id === "number" && id > 0);
      if (validTabIds.length === 0) {
        return { success: true };
      }
      await chrome.tabs.remove(validTabIds);
      return { success: true };
    } catch (error) {
      return {
        success: false,
        error: {
          code: ERROR_CODES.TAB_ERROR,
          message: "Failed to close tabs",
          details: error
        }
      };
    }
  }
  /**
   * 获取工作区真正管理的标签页（Workona 风格：完全基于 ID 映射）
   */
  static async getWorkspaceRelatedTabs(workspace) {
    try {
      const workonaTabIds = await WorkonaTabManager.getWorkspaceWorkonaTabIds(workspace.id);
      if (!workonaTabIds.success) {
        console.log(`❌ 获取工作区 Workona 标签页ID失败:`, workonaTabIds.error);
        return { success: true, data: [] };
      }
      const relatedTabs = [];
      const validWorkonaIds = [];
      for (const workonaId of workonaTabIds.data) {
        const chromeIdResult = await WorkonaTabManager.getChromeIdByWorkonaId(workonaId);
        if (chromeIdResult.success && chromeIdResult.data) {
          try {
            const tab = await chrome.tabs.get(chromeIdResult.data);
            if (tab) {
              relatedTabs.push({
                id: tab.id,
                url: tab.url,
                title: tab.title,
                favicon: tab.favIconUrl || "",
                isPinned: tab.pinned,
                isActive: tab.active,
                windowId: tab.windowId,
                index: tab.index
              });
              validWorkonaIds.push(workonaId);
              console.log(`✅ 找到 Workona 管理的标签页: ${tab.title} (${workonaId})`);
            }
          } catch (tabError) {
            await WorkonaTabManager.removeTabMapping(workonaId);
            console.log(`🗑️ 清理无效的 Workona ID 映射: ${workonaId}`);
          }
        } else {
          await WorkonaTabManager.removeTabMapping(workonaId);
          console.log(`🗑️ 清理无效的 Workona ID 映射: ${workonaId}`);
        }
      }
      return { success: true, data: relatedTabs };
    } catch (error) {
      console.error("获取工作区相关标签页失败:", error);
      return {
        success: false,
        error: {
          code: ERROR_CODES.TAB_ERROR,
          message: "Failed to get workspace related tabs",
          details: error
        }
      };
    }
  }
  /**
   * 获取非工作区相关的标签页（仅在当前窗口中查找）
   */
  static async getNonWorkspaceRelatedTabs(workspace) {
    try {
      console.log(`查找非工作区 "${workspace.name}" 相关的标签页`);
      const tabs = await chrome.tabs.query({ currentWindow: true });
      const currentTabs = tabs.map((tab) => ({
        id: tab.id,
        url: tab.url || "",
        title: tab.title || "",
        favicon: tab.favIconUrl || "",
        isPinned: tab.pinned,
        isActive: tab.active,
        windowId: tab.windowId,
        index: tab.index
      }));
      const workspaceUrls = workspace.websites.map((w) => w.url);
      const nonRelatedTabs = currentTabs.filter(
        (tab) => !workspaceUrls.some((url) => tab.url.startsWith(url))
      );
      return { success: true, data: nonRelatedTabs };
    } catch (error) {
      return {
        success: false,
        error: {
          code: ERROR_CODES.TAB_ERROR,
          message: "Failed to get non-workspace related tabs",
          details: error
        }
      };
    }
  }
  /**
   * 检查标签页是否为用户手动打开的（非工作区配置的标签页）
   */
  static async isUserOpenedTab(tabId) {
    try {
      const workonaIdResult = await WorkonaTabManager.getWorkonaIdByChromeId(tabId);
      if (!workonaIdResult.success || !workonaIdResult.data) {
        return true;
      }
      const metadataResult = await WorkonaTabManager.getTabMetadata(workonaIdResult.data);
      if (!metadataResult.success || !metadataResult.data) {
        return true;
      }
      const { metadata } = metadataResult.data;
      return metadata?.source === "user_opened";
    } catch {
      return true;
    }
  }
}

let TabClassificationUtils$1 = class TabClassificationUtils {
  /**
   * 判断是否为系统标签页
   */
  static isSystemTab(url) {
    return url.includes("chrome://") || url.includes("chrome-extension://") || url.includes("about:") || url.includes("edge://") || url.includes("workspace-placeholder.html") || url === "chrome://newtab/" || url === "about:blank" || url === "";
  }
  /**
   * 检查是否为工作区专属标签页（具有Workona ID映射）
   */
  static async isWorkspaceSpecificTab(tabId) {
    try {
      const workonaIdResult = await WorkonaTabManager.getWorkonaIdByChromeId(tabId);
      return workonaIdResult.success && !!workonaIdResult.data;
    } catch {
      return false;
    }
  }
  /**
   * 检查是否为用户标签页（非系统且非工作区专属）
   */
  static async isUserTab(tab) {
    if (!tab.url || this.isSystemTab(tab.url)) {
      return false;
    }
    const isWorkspaceSpecific = await this.isWorkspaceSpecificTab(tab.id);
    return !isWorkspaceSpecific;
  }
  /**
   * 批量分类标签页
   */
  static async classifyTabs(tabs) {
    const systemTabs = [];
    const workspaceTabs = [];
    const userTabs = [];
    for (const tab of tabs) {
      if (!tab.url || !tab.id) continue;
      if (this.isSystemTab(tab.url)) {
        systemTabs.push(tab);
      } else {
        const isWorkspaceSpecific = await this.isWorkspaceSpecificTab(tab.id);
        if (isWorkspaceSpecific) {
          workspaceTabs.push(tab);
        } else {
          userTabs.push(tab);
        }
      }
    }
    return { systemTabs, workspaceTabs, userTabs };
  }
  /**
   * 获取标签页的分类类型
   */
  static async getTabCategory(tab) {
    if (!tab.url || this.isSystemTab(tab.url)) {
      return "system";
    }
    const isWorkspaceSpecific = await this.isWorkspaceSpecificTab(tab.id);
    return isWorkspaceSpecific ? "workspace" : "user";
  }
  /**
   * 检查标签页是否可以被移动
   */
  static canMoveTab(tab) {
    if (!tab.url) return false;
    if (this.isSystemTab(tab.url)) {
      return false;
    }
    const unmovableUrls = [
      "chrome://newtab/",
      "about:blank",
      "chrome://extensions/",
      "chrome://settings/"
    ];
    return !unmovableUrls.some((url) => tab.url.startsWith(url));
  }
  /**
   * 过滤出可移动的标签页
   */
  static filterMovableTabs(tabs) {
    return tabs.filter((tab) => this.canMoveTab(tab));
  }
  /**
   * 检查标签页是否为工作区占位符页面
   */
  static isWorkspacePlaceholder(url) {
    return url.includes("workspace-placeholder.html");
  }
  /**
   * 检查标签页是否为新标签页
   */
  static isNewTab(url) {
    return url === "chrome://newtab/" || url === "about:blank" || url === "";
  }
};

class WorkspaceSwitchCore {
  static isBackgroundSetupInProgress = false;
  // 后台设置进行中标志
  static currentSetupWorkspaceId = null;
  // 当前正在设置的工作区ID
  /**
   * 检查是否有后台设置正在进行
   */
  static isSetupInProgress() {
    return this.isBackgroundSetupInProgress;
  }
  /**
   * 获取当前正在设置的工作区ID
   */
  static getCurrentSetupWorkspaceId() {
    return this.currentSetupWorkspaceId;
  }
  /**
   * 设置后台设置状态
   */
  static setBackgroundSetupStatus(workspaceId, inProgress) {
    this.isBackgroundSetupInProgress = inProgress;
    this.currentSetupWorkspaceId = workspaceId;
    if (inProgress && workspaceId) {
      console.log(`🔧 开始后台设置工作区: ${workspaceId}`);
    } else {
      console.log(`✅ 后台设置完成`);
    }
  }
  /**
   * 获取当前活跃工作区
   */
  static async getCurrentWorkspace() {
    try {
      const activeIdResult = await StorageManager.getActiveWorkspaceId();
      if (activeIdResult.success && activeIdResult.data) {
        const workspaceResult = await StorageManager.getWorkspace(activeIdResult.data);
        if (workspaceResult.success) {
          return { success: true, data: workspaceResult.data };
        }
      }
      return { success: true, data: null };
    } catch (error) {
      return {
        success: false,
        error: {
          code: ERROR_CODES.STORAGE_ERROR,
          message: "Failed to get current workspace",
          details: error
        }
      };
    }
  }
  /**
   * 设置当前活跃工作区
   */
  static async setCurrentWorkspace(workspaceId) {
    try {
      const result = await StorageManager.setActiveWorkspaceId(workspaceId);
      if (result.success) {
        console.log(`📌 设置活跃工作区: ${workspaceId || "none"}`);
      }
      return result;
    } catch (error) {
      return {
        success: false,
        error: {
          code: ERROR_CODES.STORAGE_ERROR,
          message: "Failed to set current workspace",
          details: error
        }
      };
    }
  }
  /**
   * 准备工作区切换选项
   */
  static async prepareSwitchOptions(options = {}) {
    try {
      const settingsResult = await StorageManager.getSettings();
      if (!settingsResult.success) {
        return { success: false, error: settingsResult.error };
      }
      const settings = settingsResult.data;
      const switchOptions = {
        closeOtherTabs: options.closeOtherTabs ?? settings.autoCloseOtherTabs,
        preserveUserOpenedTabs: options.preserveUserOpenedTabs ?? settings.preserveUserOpenedTabs,
        focusFirstTab: options.focusFirstTab ?? false,
        // 默认不自动聚焦到第一个标签页
        openInNewWindow: options.openInNewWindow ?? false,
        skipAnimation: options.skipAnimation ?? false
      };
      console.log(`⚙️ 切换选项:`, switchOptions);
      return { success: true, data: switchOptions };
    } catch (error) {
      return {
        success: false,
        error: {
          code: ERROR_CODES.STORAGE_ERROR,
          message: "Failed to prepare switch options",
          details: error
        }
      };
    }
  }
  /**
   * 验证工作区切换请求
   */
  static async validateSwitchRequest(workspaceId) {
    try {
      if (this.isBackgroundSetupInProgress) {
        const currentSetupId = this.currentSetupWorkspaceId;
        console.warn(`⚠️ 后台工作区设置正在进行中 (${currentSetupId})，拒绝切换到 ${workspaceId}`);
        return {
          success: false,
          error: {
            code: ERROR_CODES.TAB_ERROR,
            message: "Background workspace setup in progress",
            details: `Cannot switch to ${workspaceId} while ${currentSetupId} is being set up`
          }
        };
      }
      const workspaceResult = await StorageManager.getWorkspace(workspaceId);
      if (!workspaceResult.success) {
        return { success: false, error: workspaceResult.error };
      }
      const workspace = workspaceResult.data;
      console.log(`📋 目标工作区: ${workspace.name} (${workspace.websites.length} 个网站)`);
      return { success: true, data: workspace };
    } catch (error) {
      return {
        success: false,
        error: {
          code: ERROR_CODES.WORKSPACE_ERROR,
          message: "Failed to validate switch request",
          details: error
        }
      };
    }
  }
  /**
   * 检查是否需要切换（避免重复切换到同一工作区）
   */
  static async shouldPerformSwitch(targetWorkspaceId) {
    try {
      const currentWorkspaceResult = await this.getCurrentWorkspace();
      if (currentWorkspaceResult.success && currentWorkspaceResult.data) {
        const currentWorkspace = currentWorkspaceResult.data;
        if (currentWorkspace.id === targetWorkspaceId) {
          console.log(`ℹ️ 已在目标工作区 "${currentWorkspace.name}"，跳过切换`);
          return false;
        }
      }
      return true;
    } catch (error) {
      console.warn("检查是否需要切换时出错:", error);
      return true;
    }
  }
  /**
   * 完成工作区切换后的清理工作
   */
  static async finalizeSwitchOperation(workspaceId) {
    try {
      const setActiveResult = await this.setCurrentWorkspace(workspaceId);
      if (!setActiveResult.success) {
        return setActiveResult;
      }
      this.setBackgroundSetupStatus(null, false);
      console.log(`✅ 工作区切换完成: ${workspaceId}`);
      return { success: true };
    } catch (error) {
      return {
        success: false,
        error: {
          code: ERROR_CODES.WORKSPACE_ERROR,
          message: "Failed to finalize switch operation",
          details: error
        }
      };
    }
  }
}

class WorkspaceTabMover {
  /**
   * 将非目标工作区的标签页移动到后台窗口
   */
  static async moveNonTargetWorkspaceTabsToWindow(targetWorkspaceId) {
    try {
      console.log(`🔄 移动非目标工作区标签页到后台窗口 (目标工作区: ${targetWorkspaceId})`);
      const currentTabs = await chrome.tabs.query({ currentWindow: true });
      const tabsToMove = [];
      for (const tab of currentTabs) {
        if (!tab.id || !tab.url) continue;
        if (TabClassificationUtils$1.isSystemTab(tab.url)) {
          continue;
        }
        if (tab.active) {
          continue;
        }
        const workonaIdResult = await WorkonaTabManager.getWorkonaIdByChromeId(tab.id);
        if (workonaIdResult.success && workonaIdResult.data) {
          const tabWorkspaceId = workonaIdResult.data.split("-")[1];
          if (tabWorkspaceId !== targetWorkspaceId) {
            tabsToMove.push(tab.id);
          }
        } else {
          tabsToMove.push(tab.id);
        }
      }
      if (tabsToMove.length === 0) {
        console.log(`ℹ️ 没有需要移动的标签页`);
        return { success: true };
      }
      const backgroundWindow = await this.getOrCreateBackgroundWindow();
      if (!backgroundWindow) {
        return {
          success: false,
          error: {
            code: ERROR_CODES.WINDOW_ERROR,
            message: "Failed to create background window"
          }
        };
      }
      await chrome.tabs.move(tabsToMove, {
        windowId: backgroundWindow.id,
        index: -1
      });
      console.log(`✅ 成功移动 ${tabsToMove.length} 个标签页到后台窗口`);
      return { success: true };
    } catch (error) {
      return {
        success: false,
        error: {
          code: ERROR_CODES.TAB_ERROR,
          message: "Failed to move non-target workspace tabs",
          details: error
        }
      };
    }
  }
  /**
   * 将当前标签页移动到工作区专属窗口
   */
  static async moveCurrentTabsToWorkspaceWindow(workspace) {
    try {
      console.log(`🔄 移动当前标签页到工作区 "${workspace.name}" 的专属窗口`);
      const currentTabs = await chrome.tabs.query({ currentWindow: true });
      const workspaceTabIds = [];
      for (const tab of currentTabs) {
        if (!tab.id || !tab.url) continue;
        if (TabClassificationUtils$1.isSystemTab(tab.url)) {
          continue;
        }
        const workonaIdResult = await WorkonaTabManager.getWorkonaIdByChromeId(tab.id);
        if (workonaIdResult.success && workonaIdResult.data) {
          const tabWorkspaceId = workonaIdResult.data.split("-")[1];
          if (tabWorkspaceId === workspace.id) {
            workspaceTabIds.push(tab.id);
          }
        }
      }
      if (workspaceTabIds.length === 0) {
        console.log(`ℹ️ 当前窗口没有属于工作区 "${workspace.name}" 的标签页`);
        return { success: true };
      }
      const workspaceWindow = await this.getOrCreateWorkspaceWindow(workspace);
      if (!workspaceWindow) {
        return {
          success: false,
          error: {
            code: ERROR_CODES.WINDOW_ERROR,
            message: "Failed to create workspace window"
          }
        };
      }
      await chrome.tabs.move(workspaceTabIds, {
        windowId: workspaceWindow.id,
        index: -1
      });
      console.log(`✅ 成功移动 ${workspaceTabIds.length} 个标签页到工作区窗口`);
      return { success: true };
    } catch (error) {
      return {
        success: false,
        error: {
          code: ERROR_CODES.TAB_ERROR,
          message: "Failed to move tabs to workspace window",
          details: error
        }
      };
    }
  }
  /**
   * 将标签页从工作区窗口移动回主窗口
   */
  static async moveTabsFromWorkspaceWindow(workspace) {
    try {
      console.log(`🔄 将标签页从工作区 "${workspace.name}" 窗口移动回主窗口`);
      const allWindows = await chrome.windows.getAll({ populate: true });
      const currentWindow = await chrome.windows.getCurrent();
      const tabsToMove = [];
      for (const window of allWindows) {
        if (window.id === currentWindow.id || !window.tabs) {
          continue;
        }
        for (const tab of window.tabs) {
          if (!tab.id || !tab.url) continue;
          if (TabClassificationUtils$1.isSystemTab(tab.url)) {
            continue;
          }
          const workonaIdResult = await WorkonaTabManager.getWorkonaIdByChromeId(tab.id);
          if (workonaIdResult.success && workonaIdResult.data) {
            const tabWorkspaceId = workonaIdResult.data.split("-")[1];
            if (tabWorkspaceId === workspace.id) {
              tabsToMove.push(tab.id);
            }
          }
        }
      }
      if (tabsToMove.length === 0) {
        console.log(`ℹ️ 没有需要移动回主窗口的标签页`);
        return { success: true };
      }
      await chrome.tabs.move(tabsToMove, {
        windowId: currentWindow.id,
        index: -1
      });
      console.log(`✅ 成功移动 ${tabsToMove.length} 个标签页回主窗口`);
      return { success: true };
    } catch (error) {
      return {
        success: false,
        error: {
          code: ERROR_CODES.TAB_ERROR,
          message: "Failed to move tabs from workspace window",
          details: error
        }
      };
    }
  }
  /**
   * 获取或创建后台窗口
   */
  static async getOrCreateBackgroundWindow() {
    try {
      const allWindows = await chrome.windows.getAll();
      const backgroundWindow = allWindows.find(
        (window) => window.state === "minimized" && window.type === "normal"
      );
      if (backgroundWindow) {
        return backgroundWindow;
      }
      const newWindow = await chrome.windows.create({
        url: "about:blank",
        focused: false,
        state: "minimized",
        type: "normal"
      });
      return newWindow;
    } catch (error) {
      console.error("创建后台窗口失败:", error);
      return null;
    }
  }
  /**
   * 获取或创建工作区专属窗口
   */
  static async getOrCreateWorkspaceWindow(_workspace) {
    try {
      const currentWindow = await chrome.windows.getCurrent();
      return currentWindow;
    } catch (error) {
      console.error("获取工作区窗口失败:", error);
      return null;
    }
  }
}

class WorkspaceProtectionManager {
  /**
   * 确保系统标签页保护
   * 防止重要的系统标签页被意外关闭或移动
   */
  static async ensureSystemTabProtection() {
    try {
      console.log("🛡️ 执行系统标签页保护检查");
      const currentTabs = await chrome.tabs.query({ currentWindow: true });
      const systemTabs = currentTabs.filter(
        (tab) => tab.url && TabClassificationUtils$1.isSystemTab(tab.url)
      );
      if (systemTabs.length > 0) {
        console.log(`🛡️ 发现 ${systemTabs.length} 个系统标签页，确保其保护状态`);
        for (const tab of systemTabs) {
          if (tab.id) {
            console.log(`🛡️ 保护系统标签页: ${tab.url}`);
          }
        }
      }
      const nonSystemTabs = currentTabs.filter(
        (tab) => tab.url && !TabClassificationUtils$1.isSystemTab(tab.url)
      );
      if (nonSystemTabs.length === 0) {
        console.warn("⚠️ 当前窗口没有非系统标签页，可能需要创建占位符标签页");
        await chrome.tabs.create({
          url: "about:blank",
          active: false
        });
        console.log("✅ 已创建占位符标签页");
      }
    } catch (error) {
      console.error("系统标签页保护失败:", error);
    }
  }
  /**
   * 确保工作区切换保护
   * 防止切换过程中的数据丢失和状态混乱
   */
  static async ensureWorkspaceSwitchProtection(targetWorkspaceId) {
    try {
      console.log(`🛡️ 执行工作区切换保护检查 (目标: ${targetWorkspaceId})`);
      const currentWindow = await chrome.windows.getCurrent();
      if (!currentWindow.id) {
        throw new Error("无法获取当前窗口ID");
      }
      const currentTabs = await chrome.tabs.query({ windowId: currentWindow.id });
      console.log(`🛡️ 当前窗口有 ${currentTabs.length} 个标签页`);
      if (currentTabs.length === 0) {
        console.warn("⚠️ 当前窗口没有标签页，创建占位符标签页");
        await chrome.tabs.create({
          url: "about:blank",
          windowId: currentWindow.id,
          active: true
        });
      }
      const activeTabs = currentTabs.filter((tab) => tab.active);
      if (activeTabs.length === 0 && currentTabs.length > 0) {
        console.warn("⚠️ 没有活跃标签页，激活第一个标签页");
        const firstTab = currentTabs[0];
        if (firstTab.id) {
          await chrome.tabs.update(firstTab.id, { active: true });
        }
      }
      console.log("✅ 工作区切换保护检查完成");
    } catch (error) {
      console.error("工作区切换保护失败:", error);
    }
  }
  /**
   * 验证标签页状态完整性
   */
  static async validateTabStateIntegrity() {
    try {
      console.log("🔍 验证标签页状态完整性");
      const allWindows = await chrome.windows.getAll({ populate: true });
      let totalTabs = 0;
      let systemTabs = 0;
      let userTabs = 0;
      for (const window of allWindows) {
        if (!window.tabs) continue;
        for (const tab of window.tabs) {
          if (!tab.url) continue;
          totalTabs++;
          if (TabClassificationUtils$1.isSystemTab(tab.url)) {
            systemTabs++;
          } else {
            userTabs++;
          }
        }
      }
      console.log(`📊 标签页统计: 总计 ${totalTabs}, 系统 ${systemTabs}, 用户 ${userTabs}`);
      if (totalTabs === 0) {
        return {
          success: false,
          error: {
            code: ERROR_CODES.TAB_ERROR,
            message: "No tabs found in any window"
          }
        };
      }
      if (allWindows.length === 0) {
        return {
          success: false,
          error: {
            code: ERROR_CODES.WINDOW_ERROR,
            message: "No windows found"
          }
        };
      }
      console.log("✅ 标签页状态完整性验证通过");
      return { success: true };
    } catch (error) {
      return {
        success: false,
        error: {
          code: ERROR_CODES.TAB_ERROR,
          message: "Failed to validate tab state integrity",
          details: error
        }
      };
    }
  }
  /**
   * 检测并处理孤立标签页
   * 查找没有正确Workona ID映射的标签页
   */
  static async detectAndHandleOrphanedTabs() {
    try {
      console.log("🔍 检测孤立标签页");
      const allWindows = await chrome.windows.getAll({ populate: true });
      const orphanedTabs = [];
      for (const window of allWindows) {
        if (!window.tabs) continue;
        for (const tab of window.tabs) {
          if (!tab.id || !tab.url) continue;
          if (TabClassificationUtils$1.isSystemTab(tab.url)) {
            continue;
          }
        }
      }
      console.log(`📊 发现 ${orphanedTabs.length} 个孤立标签页`);
      return { success: true, data: orphanedTabs.length };
    } catch (error) {
      return {
        success: false,
        error: {
          code: ERROR_CODES.TAB_ERROR,
          message: "Failed to detect orphaned tabs",
          details: error
        }
      };
    }
  }
  /**
   * 紧急恢复机制
   * 在检测到严重问题时执行的恢复操作
   */
  static async emergencyRecovery() {
    try {
      console.log("🚨 执行紧急恢复机制");
      const allWindows = await chrome.windows.getAll();
      if (allWindows.length === 0) {
        await chrome.windows.create({
          url: "about:blank",
          focused: true,
          type: "normal"
        });
        console.log("🚨 已创建紧急恢复窗口");
      }
      const currentWindow = await chrome.windows.getCurrent();
      const currentTabs = await chrome.tabs.query({ windowId: currentWindow.id });
      if (currentTabs.length === 0) {
        await chrome.tabs.create({
          url: "about:blank",
          windowId: currentWindow.id,
          active: true
        });
        console.log("🚨 已创建紧急恢复标签页");
      }
      console.log("✅ 紧急恢复完成");
      return { success: true };
    } catch (error) {
      return {
        success: false,
        error: {
          code: ERROR_CODES.SYSTEM_ERROR,
          message: "Emergency recovery failed",
          details: error
        }
      };
    }
  }
}

class WorkspaceNotificationManager {
  /**
   * 通知工作区切换完成
   */
  static async notifyWorkspaceSwitchComplete(workspaceId) {
    try {
      console.log(`📢 通知工作区切换完成: ${workspaceId}`);
      const message = {
        type: "WORKSPACE_SWITCHED",
        workspaceId,
        timestamp: Date.now()
      };
      try {
        await chrome.runtime.sendMessage(message);
      } catch (error) {
        console.log("📢 没有消息监听器，跳过消息发送");
      }
      if (typeof window !== "undefined" && window.dispatchEvent) {
        const customEvent = new CustomEvent("workspaceSwitched", {
          detail: { workspaceId, timestamp: Date.now() }
        });
        window.dispatchEvent(customEvent);
      }
      console.log(`✅ 工作区切换通知发送完成: ${workspaceId}`);
    } catch (error) {
      console.error("发送工作区切换通知失败:", error);
    }
  }
  /**
   * 通知工作区设置状态变更
   */
  static async notifyWorkspaceSetupStatusChange(workspaceId, isSetupInProgress) {
    try {
      console.log(`📢 通知工作区设置状态变更: ${workspaceId}, 进行中: ${isSetupInProgress}`);
      const message = {
        type: "WORKSPACE_SETUP_STATUS_CHANGED",
        workspaceId,
        isSetupInProgress,
        timestamp: Date.now()
      };
      try {
        await chrome.runtime.sendMessage(message);
      } catch (error) {
        console.log("📢 没有消息监听器，跳过消息发送");
      }
      if (typeof window !== "undefined" && window.dispatchEvent) {
        const customEvent = new CustomEvent("workspaceSetupStatusChanged", {
          detail: { workspaceId, isSetupInProgress, timestamp: Date.now() }
        });
        window.dispatchEvent(customEvent);
      }
      console.log(`✅ 工作区设置状态通知发送完成: ${workspaceId}`);
    } catch (error) {
      console.error("发送工作区设置状态通知失败:", error);
    }
  }
  /**
   * 通知工作区标签页状态变更
   */
  static async notifyWorkspaceTabStateChange(workspaceId, changeType, details) {
    try {
      console.log(`📢 通知工作区标签页状态变更: ${workspaceId}, 类型: ${changeType}`);
      const message = {
        type: "WORKSPACE_TAB_STATE_CHANGED",
        workspaceId,
        changeType,
        details,
        timestamp: Date.now()
      };
      try {
        await chrome.runtime.sendMessage(message);
      } catch (error) {
        console.log("📢 没有消息监听器，跳过消息发送");
      }
      console.log(`✅ 工作区标签页状态变更通知发送完成: ${workspaceId}`);
    } catch (error) {
      console.error("发送工作区标签页状态变更通知失败:", error);
    }
  }
  /**
   * 通知用户标签页可见性变更
   */
  static async notifyUserTabsVisibilityChange(workspaceId, isHidden, affectedCount) {
    try {
      console.log(`📢 通知用户标签页可见性变更: ${workspaceId}, 隐藏: ${isHidden}, 影响数量: ${affectedCount}`);
      const message = {
        type: "USER_TABS_VISIBILITY_CHANGED",
        workspaceId,
        isHidden,
        affectedCount,
        timestamp: Date.now()
      };
      try {
        await chrome.runtime.sendMessage(message);
      } catch (error) {
        console.log("📢 没有消息监听器，跳过消息发送");
      }
      console.log(`✅ 用户标签页可见性变更通知发送完成: ${workspaceId}`);
    } catch (error) {
      console.error("发送用户标签页可见性变更通知失败:", error);
    }
  }
  /**
   * 发送通用工作区事件通知
   */
  static async sendWorkspaceEvent(eventType, workspaceId, data) {
    try {
      console.log(`📢 发送工作区事件: ${eventType}, 工作区: ${workspaceId}`);
      const message = {
        type: "WORKSPACE_EVENT",
        eventType,
        workspaceId,
        data,
        timestamp: Date.now()
      };
      try {
        await chrome.runtime.sendMessage(message);
      } catch (error) {
        console.log("📢 没有消息监听器，跳过消息发送");
      }
      if (typeof window !== "undefined" && window.dispatchEvent) {
        const customEvent = new CustomEvent("workspaceEvent", {
          detail: { eventType, workspaceId, data, timestamp: Date.now() }
        });
        window.dispatchEvent(customEvent);
      }
      console.log(`✅ 工作区事件通知发送完成: ${eventType}`);
    } catch (error) {
      console.error("发送工作区事件通知失败:", error);
    }
  }
  /**
   * 批量发送通知
   */
  static async sendBatchNotifications(notifications) {
    try {
      console.log(`📢 批量发送 ${notifications.length} 个通知`);
      const promises = notifications.map(
        (notification) => this.sendWorkspaceEvent(notification.type, notification.workspaceId, notification.data)
      );
      await Promise.allSettled(promises);
      console.log(`✅ 批量通知发送完成`);
    } catch (error) {
      console.error("批量发送通知失败:", error);
    }
  }
  /**
   * 清理过期的通知监听器
   */
  static cleanupExpiredListeners() {
    try {
      console.log("🧹 清理过期的通知监听器");
      console.log("✅ 通知监听器清理完成");
    } catch (error) {
      console.error("清理通知监听器失败:", error);
    }
  }
}

class WorkspaceSwitcher {
  /**
   * 检查是否有后台设置正在进行
   */
  static isSetupInProgress() {
    return WorkspaceSwitchCore.isSetupInProgress();
  }
  /**
   * 获取当前正在设置的工作区ID
   */
  static getCurrentSetupWorkspaceId() {
    return WorkspaceSwitchCore.getCurrentSetupWorkspaceId();
  }
  /**
   * 主要的工作区切换方法
   * 整合了所有拆分后的服务类功能
   */
  static async switchToWorkspace(workspaceId, options = {}) {
    try {
      console.log(`🔄 开始切换到工作区: ${workspaceId}`);
      const validationResult = await WorkspaceSwitchCore.validateSwitchRequest(workspaceId);
      if (!validationResult.success) {
        return { success: false, error: validationResult.error };
      }
      const workspace = validationResult.data;
      const shouldSwitch = await WorkspaceSwitchCore.shouldPerformSwitch(workspaceId);
      if (!shouldSwitch) {
        return { success: true };
      }
      const optionsResult = await WorkspaceSwitchCore.prepareSwitchOptions(options);
      if (!optionsResult.success) {
        return { success: false, error: optionsResult.error };
      }
      const switchOptions = optionsResult.data;
      const currentWorkspaceResult = await WorkspaceSwitchCore.getCurrentWorkspace();
      const currentWorkspace = currentWorkspaceResult.success ? currentWorkspaceResult.data : null;
      WorkspaceSwitchCore.setBackgroundSetupStatus(workspaceId, true);
      await WorkspaceProtectionManager.ensureSystemTabProtection();
      await WorkspaceProtectionManager.ensureWorkspaceSwitchProtection(workspaceId);
      if (currentWorkspace) {
        console.log(`💾 保存当前工作区状态: ${currentWorkspace.name}`);
        await WorkspaceSessionManager.syncCurrentWorkspaceState();
      }
      if (currentWorkspace && currentWorkspace.id !== workspaceId) {
        await WorkspaceTabMover.moveCurrentTabsToWorkspaceWindow(currentWorkspace);
      } else if (!currentWorkspace) {
        await WorkspaceTabMover.moveNonTargetWorkspaceTabsToWindow(workspaceId);
      }
      await this.handlePendingTabAssignment(workspaceId);
      if (switchOptions.autoOpenWebsites) {
        await this.openWorkspaceWebsites(workspace);
      }
      await this.handleUserTabsVisibilityState(workspace);
      const finalizeResult = await WorkspaceSwitchCore.finalizeSwitchOperation(workspaceId);
      if (!finalizeResult.success) {
        return finalizeResult;
      }
      await WorkspaceNotificationManager.notifyWorkspaceSwitchComplete(workspaceId);
      await WorkspaceNotificationManager.notifyWorkspaceSetupStatusChange(workspaceId, false);
      console.log(`✅ 工作区切换完成: ${workspace.name}`);
      return { success: true };
    } catch (error) {
      WorkspaceSwitchCore.setBackgroundSetupStatus(null, false);
      return {
        success: false,
        error: {
          code: ERROR_CODES.WORKSPACE_ERROR,
          message: "Failed to switch workspace",
          details: error
        }
      };
    }
  }
  /**
   * 获取当前工作区
   */
  static async getCurrentWorkspace() {
    return WorkspaceSwitchCore.getCurrentWorkspace();
  }
  /**
   * 检测活跃工作区
   */
  static async detectActiveWorkspace() {
    try {
      const currentResult = await WorkspaceSwitchCore.getCurrentWorkspace();
      if (currentResult.success && currentResult.data) {
        return currentResult;
      }
      const activeTabResult = await TabManager.getActiveTab();
      if (!activeTabResult.success || !activeTabResult.data) {
        return { success: true, data: null };
      }
      const activeTab = activeTabResult.data;
      const workspacesResult = await StorageManager.getWorkspaces();
      if (!workspacesResult.success) {
        return { success: false, error: workspacesResult.error };
      }
      const workspaces = workspacesResult.data;
      const workonaIdResult = await WorkonaTabManager.getWorkonaIdByChromeId(activeTab.id);
      if (workonaIdResult.success && workonaIdResult.data) {
        const workspaceId = workonaIdResult.data.split("-")[1];
        const workspace = workspaces.find((w) => w.id === workspaceId);
        if (workspace) {
          console.log(`🎯 通过Workona ID检测到活跃工作区: ${workspace.name}`);
          return { success: true, data: workspace };
        }
      }
      const matchResult = await WorkspaceTabContentMatcher.isWorkspaceTab(activeTab);
      if (matchResult.isMatch && matchResult.workspaceId) {
        const matchingWorkspace = workspaces.find((w) => w.id === matchResult.workspaceId);
        if (matchingWorkspace) {
          console.log(`🏢 通过智能匹配检测到活跃工作区: ${matchingWorkspace.name} (置信度: ${matchResult.confidence})`);
          return { success: true, data: matchingWorkspace };
        }
      }
      return { success: true, data: null };
    } catch (error) {
      return {
        success: false,
        error: {
          code: ERROR_CODES.WORKSPACE_ERROR,
          message: "Failed to detect active workspace",
          details: error
        }
      };
    }
  }
  /**
   * 处理等待归属的标签页
   */
  static async handlePendingTabAssignment(workspaceId) {
    try {
      console.log(`🔄 处理等待归属的标签页，分配到工作区: ${workspaceId}`);
      const allTabs = await chrome.tabs.query({});
      const pendingTabs = [];
      for (const tab of allTabs) {
        if (!tab.id || !tab.url) continue;
        if (TabClassificationUtils$1.isSystemTab(tab.url)) {
          continue;
        }
        const workonaIdResult = await WorkonaTabManager.getWorkonaIdByChromeId(tab.id);
        if (workonaIdResult.success && workonaIdResult.data) {
          const tabWorkspaceId = workonaIdResult.data.split("-")[1];
          if (tabWorkspaceId === "pending-assignment") {
            pendingTabs.push(tab);
          }
        }
      }
      if (pendingTabs.length === 0) {
        console.log(`ℹ️ 没有等待归属的标签页`);
        return;
      }
      for (const tab of pendingTabs) {
        if (!tab.id) continue;
        const oldWorkonaIdResult = await WorkonaTabManager.getWorkonaIdByChromeId(tab.id);
        if (oldWorkonaIdResult.success && oldWorkonaIdResult.data) {
          await WorkonaTabManager.removeTabMapping(oldWorkonaIdResult.data);
          const newWorkonaId = WorkonaTabManager.generateWorkonaTabId(workspaceId);
          await WorkonaTabManager.createTabIdMapping(
            newWorkonaId,
            tab.id,
            workspaceId,
            void 0,
            {
              isWorkspaceCore: false,
              source: "user_opened"
            }
          );
          console.log(`✅ 重新分配标签页: ${tab.title} -> ${workspaceId}`);
        }
      }
      console.log(`✅ 完成 ${pendingTabs.length} 个等待归属标签页的重新分配`);
    } catch (error) {
      console.error("处理等待归属标签页失败:", error);
    }
  }
  /**
   * 打开工作区网站
   */
  static async openWorkspaceWebsites(workspace) {
    try {
      console.log(`🌐 打开工作区网站: ${workspace.name}`);
      for (const website of workspace.websites) {
        const existingTabs = await chrome.tabs.query({ url: website.url + "*" });
        if (existingTabs.length > 0) {
          console.log(`ℹ️ 网站已打开，跳过: ${website.title}`);
          continue;
        }
        const createResult = await TabManager.createTab(website.url, false, false);
        if (createResult.success) {
          console.log(`✅ 打开网站: ${website.title}`);
        } else {
          console.error(`❌ 打开网站失败: ${website.title}`, createResult.error);
        }
      }
    } catch (error) {
      console.error("打开工作区网站失败:", error);
    }
  }
  /**
   * 处理用户标签页可见性状态
   */
  static async handleUserTabsVisibilityState(workspace) {
    try {
      console.log(`👁️ 处理工作区用户标签页可见性: ${workspace.name}`);
      const stateResult = await WorkspaceUserTabsVisibilityManager.getWorkspaceUserTabsState(workspace.id);
      if (stateResult.success && stateResult.data) {
        const state = stateResult.data;
        console.log(`📊 用户标签页状态: 总计 ${state.totalUserTabs}, 可见 ${state.visibleUserTabs}, 隐藏 ${state.hiddenTabIds.length}`);
        await UserTabsRealTimeMonitor.forceRefreshWorkspaceState(workspace.id);
      }
    } catch (error) {
      console.error("处理用户标签页可见性状态失败:", error);
    }
  }
  /**
   * 向后兼容的方法 - 委托给新的服务类
   */
  // 系统保护相关
  static async ensureSystemTabProtection() {
    return WorkspaceProtectionManager.ensureSystemTabProtection();
  }
  static async ensureWorkspaceSwitchProtection(workspaceId) {
    return WorkspaceProtectionManager.ensureWorkspaceSwitchProtection(workspaceId);
  }
  // 标签页移动相关
  static async moveNonTargetWorkspaceTabsToWindow(targetWorkspaceId) {
    return WorkspaceTabMover.moveNonTargetWorkspaceTabsToWindow(targetWorkspaceId);
  }
  static async moveCurrentTabsToWorkspaceWindow(workspace) {
    return WorkspaceTabMover.moveCurrentTabsToWorkspaceWindow(workspace);
  }
  // 通知相关
  static async notifyWorkspaceSwitchComplete(workspaceId) {
    return WorkspaceNotificationManager.notifyWorkspaceSwitchComplete(workspaceId);
  }
}

class UserTabsRealTimeMonitor {
  static isMonitoring = false;
  static monitoringInterval = null;
  static lastStateSnapshot = /* @__PURE__ */ new Map();
  static MONITOR_INTERVAL = 2e3;
  // 优化为2秒检查间隔，减少日志洪水
  static pendingUpdate = false;
  // 防止重复更新
  /**
   * 启动实时监控
   */
  static startMonitoring() {
    if (this.isMonitoring) {
      return;
    }
    console.log("🚀 启动用户标签页实时监控");
    this.isMonitoring = true;
    this.checkUserTabsStateChanges();
    this.monitoringInterval = setInterval(() => {
      this.checkUserTabsStateChanges();
    }, this.MONITOR_INTERVAL);
  }
  /**
   * 停止实时监控
   */
  static stopMonitoring() {
    if (!this.isMonitoring) {
      return;
    }
    console.log("⏹️ 停止用户标签页实时监控");
    this.isMonitoring = false;
    if (this.monitoringInterval) {
      clearInterval(this.monitoringInterval);
      this.monitoringInterval = null;
    }
    this.lastStateSnapshot.clear();
  }
  /**
   * 检查用户标签页状态变化（优化版：防重复更新）
   */
  static async checkUserTabsStateChanges() {
    if (this.pendingUpdate) {
      return;
    }
    try {
      this.pendingUpdate = true;
      const activeWorkspace = await this.getCurrentActiveWorkspace();
      if (!activeWorkspace) {
        return;
      }
      const workspaceId = activeWorkspace.id;
      try {
        const { WorkspaceUserTabsVisibilityManager } = await __vitePreload(async () => { const { WorkspaceUserTabsVisibilityManager } = await Promise.resolve().then(() => WorkspaceUserTabsVisibilityManager$1);return { WorkspaceUserTabsVisibilityManager }},true?void 0:void 0);
        const currentState = await WorkspaceUserTabsVisibilityManager.getWorkspaceUserTabsState(workspaceId);
        if (!currentState.success) {
          return;
        }
        const stateData = currentState.data;
        const stateKey = `workspace_${workspaceId}`;
        const lastState = this.lastStateSnapshot.get(stateKey);
        const currentSnapshot = {
          isHidden: stateData.isHidden,
          hiddenTabsCount: stateData.hiddenTabIds.length,
          totalUserTabs: stateData.totalUserTabs,
          visibleUserTabs: stateData.visibleUserTabs,
          actionType: stateData.actionType,
          timestamp: Date.now()
        };
        if (lastState && this.hasStateChanged(lastState, currentSnapshot)) {
          console.log(`📊 检测到工作区 "${activeWorkspace.name}" 用户标签页状态变化:`, {
            前: lastState,
            后: currentSnapshot
          });
          await this.notifyStateChange(workspaceId, currentSnapshot);
        }
        this.lastStateSnapshot.set(stateKey, currentSnapshot);
      } catch (importError) {
        console.warn("动态导入WorkspaceUserTabsVisibilityManager失败:", importError);
        return;
      }
    } catch (error) {
      console.error("检查用户标签页状态变化失败:", error);
    } finally {
      this.pendingUpdate = false;
    }
  }
  /**
   * 获取当前活跃工作区（多策略检测）
   */
  static async getCurrentActiveWorkspace() {
    try {
      const activeIdResult = await StorageManager.getActiveWorkspaceId();
      if (activeIdResult.success && activeIdResult.data) {
        const workspacesResult = await StorageManager.getWorkspaces();
        if (workspacesResult.success && workspacesResult.data) {
          const workspace = workspacesResult.data.find((w) => w.id === activeIdResult.data);
          if (workspace) {
            return workspace;
          }
        }
      }
      try {
        const activeWorkspaceResult = await WorkspaceSwitcher.detectActiveWorkspace();
        if (activeWorkspaceResult.success && activeWorkspaceResult.data) {
          return activeWorkspaceResult.data;
        }
      } catch (error) {
        console.warn("通过WorkspaceSwitcher检测活跃工作区失败:", error);
      }
      return null;
    } catch (error) {
      console.error("获取当前活跃工作区失败:", error);
      return null;
    }
  }
  /**
   * 检查状态是否发生变化
   */
  static hasStateChanged(lastState, currentState) {
    return lastState.isHidden !== currentState.isHidden || lastState.hiddenTabsCount !== currentState.hiddenTabsCount || lastState.totalUserTabs !== currentState.totalUserTabs || lastState.visibleUserTabs !== currentState.visibleUserTabs || lastState.actionType !== currentState.actionType;
  }
  /**
   * 通知状态变化
   */
  static async notifyStateChange(workspaceId, _newState) {
    try {
      chrome.runtime.sendMessage({
        type: "USER_TABS_VISIBILITY_CHANGED",
        workspaceId,
        timestamp: Date.now()
      }).catch(() => {
      });
    } catch (error) {
      console.warn("通知状态变化失败:", error);
    }
  }
  /**
   * 强制刷新指定工作区状态
   */
  static async forceRefreshWorkspaceState(workspaceId) {
    try {
      console.log(`🔄 强制刷新工作区状态: ${workspaceId}`);
      const stateKey = `workspace_${workspaceId}`;
      this.lastStateSnapshot.delete(stateKey);
      if (this.isMonitoring) {
        await this.checkUserTabsStateChanges();
      }
      console.log(`✅ 工作区状态刷新完成: ${workspaceId}`);
    } catch (error) {
      console.error(`❌ 强制刷新工作区状态失败 (${workspaceId}):`, error);
    }
  }
  /**
   * 立即触发状态检查
   */
  static async triggerImmediateStateCheck() {
    if (!this.isMonitoring) {
      console.log("⚠️ 监控未启动，启动监控并执行状态检查");
      this.startMonitoring();
      return;
    }
    console.log("🔄 立即触发用户标签页状态检查");
    await this.checkUserTabsStateChanges();
  }
  /**
   * 获取监控状态
   */
  static getMonitoringStatus() {
    return {
      isMonitoring: this.isMonitoring,
      workspaceCount: this.lastStateSnapshot.size
    };
  }
}

const UserTabsRealTimeMonitor$1 = /*#__PURE__*/Object.freeze(/*#__PURE__*/Object.defineProperty({
  __proto__: null,
  UserTabsRealTimeMonitor
}, Symbol.toStringTag, { value: 'Module' }));

class WorkspaceUserTabsVisibilityManager {
  /**
   * 获取工作区用户标签页状态
   */
  static async getWorkspaceUserTabsState(workspaceId) {
    try {
      const workspaceResult = await StorageManager.getWorkspace(workspaceId);
      if (!workspaceResult.success) {
        return { success: false, error: workspaceResult.error };
      }
      const { TabClassificationService } = await __vitePreload(async () => { const { TabClassificationService } = await import('./TabClassificationService-CrGHN-lq.js');return { TabClassificationService }},true?[]:void 0);
      const currentTabsResult = await TabClassificationService.getCurrentWindowTabs();
      if (!currentTabsResult.success) {
        return { success: false, error: currentTabsResult.error };
      }
      const allTabs = currentTabsResult.data;
      const userTabs = [];
      const hiddenTabIds = [];
      const pinnedTabIds = [];
      for (const tab of allTabs) {
        if (TabClassificationUtils.isSystemTab(tab.url)) {
          continue;
        }
        const workonaIdResult = await WorkonaTabManager.getWorkonaIdByChromeId(tab.id);
        if (workonaIdResult.success && workonaIdResult.data) {
          const tabWorkspaceId = workonaIdResult.data.split("-")[1];
          if (tabWorkspaceId === workspaceId) {
            const metadataResult = await WorkonaTabManager.getTabMetadata(workonaIdResult.data);
            if (metadataResult.success && metadataResult.data) {
              const { isWorkspaceCore } = metadataResult.data;
              if (!isWorkspaceCore) {
                userTabs.push(tab);
                if (!tab.isActive && tab.windowId !== (await chrome.windows.getCurrent()).id) {
                  hiddenTabIds.push(tab.id);
                }
                if (tab.isPinned) {
                  pinnedTabIds.push(tab.id);
                }
              }
            }
          }
        } else {
          userTabs.push(tab);
          if (!tab.isActive && tab.windowId !== (await chrome.windows.getCurrent()).id) {
            hiddenTabIds.push(tab.id);
          }
          if (tab.isPinned) {
            pinnedTabIds.push(tab.id);
          }
        }
      }
      const totalUserTabs = userTabs.length;
      const visibleUserTabs = totalUserTabs - hiddenTabIds.length;
      const isHidden = hiddenTabIds.length > 0;
      const canContinueHiding = visibleUserTabs > 0;
      let actionType;
      if (isHidden && canContinueHiding) {
        actionType = "continue_hide";
      } else if (isHidden) {
        actionType = "show";
      } else {
        actionType = "hide";
      }
      return {
        success: true,
        data: {
          isHidden,
          hiddenTabIds,
          pinnedTabIds,
          totalUserTabs,
          visibleUserTabs,
          canContinueHiding,
          actionType
        }
      };
    } catch (error) {
      return {
        success: false,
        error: {
          code: ERROR_CODES.TAB_ERROR,
          message: "Failed to get workspace user tabs state",
          details: error
        }
      };
    }
  }
  /**
   * 切换工作区用户标签页可见性（兼容原版本签名）
   */
  static async toggleWorkspaceUserTabsVisibility(workspaceOrId) {
    try {
      const workspaceId = typeof workspaceOrId === "string" ? workspaceOrId : workspaceOrId.id;
      const stateResult = await this.getWorkspaceUserTabsState(workspaceId);
      if (!stateResult.success) {
        return { success: false, error: stateResult.error };
      }
      const state = stateResult.data;
      if (state.actionType === "show") {
        return await this.showWorkspaceUserTabs(workspaceId);
      } else if (state.actionType === "continue_hide") {
        return await this.continueHideWorkspaceUserTabs(workspaceId);
      } else {
        return await this.hideWorkspaceUserTabs(workspaceId);
      }
    } catch (error) {
      return {
        success: false,
        error: {
          code: ERROR_CODES.TAB_ERROR,
          message: "Failed to toggle workspace user tabs visibility",
          details: error
        }
      };
    }
  }
  /**
   * 隐藏工作区用户标签页
   */
  static async hideWorkspaceUserTabs(workspaceId) {
    try {
      const stateResult = await this.getWorkspaceUserTabsState(workspaceId);
      if (!stateResult.success) {
        return { success: false, error: stateResult.error };
      }
      const state = stateResult.data;
      const { TabClassificationService } = await __vitePreload(async () => { const { TabClassificationService } = await import('./TabClassificationService-CrGHN-lq.js');return { TabClassificationService }},true?[]:void 0);
      const currentTabsResult = await TabClassificationService.getCurrentWindowTabs();
      if (!currentTabsResult.success) {
        return { success: false, error: currentTabsResult.error };
      }
      const allTabs = currentTabsResult.data;
      const tabsToHide = [];
      for (const tab of allTabs) {
        if (TabClassificationUtils.isSystemTab(tab.url)) {
          continue;
        }
        if (tab.isActive) {
          continue;
        }
        const workonaIdResult = await WorkonaTabManager.getWorkonaIdByChromeId(tab.id);
        if (workonaIdResult.success && workonaIdResult.data) {
          const tabWorkspaceId = workonaIdResult.data.split("-")[1];
          if (tabWorkspaceId === workspaceId) {
            const metadataResult = await WorkonaTabManager.getTabMetadata(workonaIdResult.data);
            if (metadataResult.success && metadataResult.data) {
              const { isWorkspaceCore } = metadataResult.data;
              if (!isWorkspaceCore) {
                tabsToHide.push(tab.id);
              }
            }
          }
        } else {
          tabsToHide.push(tab.id);
        }
      }
      if (tabsToHide.length > 0) {
        const backgroundWindow = await chrome.windows.create({
          url: "about:blank",
          focused: false,
          state: "minimized"
        });
        await chrome.tabs.move(tabsToHide, {
          windowId: backgroundWindow.id,
          index: -1
        });
      }
      return {
        success: true,
        data: {
          action: "hidden",
          affectedTabsCount: tabsToHide.length,
          totalUserTabs: state.totalUserTabs
        }
      };
    } catch (error) {
      return {
        success: false,
        error: {
          code: ERROR_CODES.TAB_ERROR,
          message: "Failed to hide workspace user tabs",
          details: error
        }
      };
    }
  }
  /**
   * 继续隐藏工作区用户标签页
   */
  static async continueHideWorkspaceUserTabs(workspaceId) {
    try {
      const hideResult = await this.hideWorkspaceUserTabs(workspaceId);
      if (!hideResult.success) {
        return { success: false, error: hideResult.error };
      }
      return {
        success: true,
        data: {
          action: "continue_hidden",
          affectedTabsCount: hideResult.data?.affectedTabsCount || 0,
          totalUserTabs: hideResult.data?.totalUserTabs || 0
        }
      };
    } catch (error) {
      return {
        success: false,
        error: {
          code: ERROR_CODES.TAB_ERROR,
          message: "Failed to continue hide workspace user tabs",
          details: error
        }
      };
    }
  }
  /**
   * 显示工作区用户标签页
   */
  static async showWorkspaceUserTabs(workspaceId) {
    try {
      const allWindows = await chrome.windows.getAll({ populate: true });
      const currentWindow = await chrome.windows.getCurrent();
      const tabsToShow = [];
      for (const window of allWindows) {
        if (window.id === currentWindow.id || !window.tabs) {
          continue;
        }
        for (const tab of window.tabs) {
          if (!tab.id || !tab.url) continue;
          if (TabClassificationUtils.isSystemTab(tab.url)) {
            continue;
          }
          const workonaIdResult = await WorkonaTabManager.getWorkonaIdByChromeId(tab.id);
          if (workonaIdResult.success && workonaIdResult.data) {
            const tabWorkspaceId = workonaIdResult.data.split("-")[1];
            if (tabWorkspaceId === workspaceId) {
              const metadataResult = await WorkonaTabManager.getTabMetadata(workonaIdResult.data);
              if (metadataResult.success && metadataResult.data) {
                const { isWorkspaceCore } = metadataResult.data;
                if (!isWorkspaceCore) {
                  tabsToShow.push(tab.id);
                }
              }
            }
          }
        }
      }
      if (tabsToShow.length > 0) {
        await chrome.tabs.move(tabsToShow, {
          windowId: currentWindow.id,
          index: -1
        });
      }
      const stateResult = await this.getWorkspaceUserTabsState(workspaceId);
      const totalUserTabs = stateResult.success ? stateResult.data.totalUserTabs : 0;
      return {
        success: true,
        data: {
          action: "shown",
          affectedTabsCount: tabsToShow.length,
          totalUserTabs
        }
      };
    } catch (error) {
      return {
        success: false,
        error: {
          code: ERROR_CODES.TAB_ERROR,
          message: "Failed to show workspace user tabs",
          details: error
        }
      };
    }
  }
  /**
   * 设置工作区用户标签页状态（兼容性方法）
   * @deprecated 此方法主要用于向后兼容，建议使用具体的隐藏/显示方法
   */
  static async setWorkspaceUserTabsState(workspaceId, isHidden, _hiddenTabIds, _pinnedTabIds) {
    try {
      if (isHidden) {
        const hideResult = await this.hideWorkspaceUserTabs(workspaceId);
        return { success: hideResult.success, error: hideResult.error };
      } else {
        const showResult = await this.showWorkspaceUserTabs(workspaceId);
        return { success: showResult.success, error: showResult.error };
      }
    } catch (error) {
      return {
        success: false,
        error: {
          code: ERROR_CODES.TAB_ERROR,
          message: "Failed to set workspace user tabs state",
          details: error
        }
      };
    }
  }
}

const WorkspaceUserTabsVisibilityManager$1 = /*#__PURE__*/Object.freeze(/*#__PURE__*/Object.defineProperty({
  __proto__: null,
  WorkspaceUserTabsVisibilityManager
}, Symbol.toStringTag, { value: 'Module' }));

class WorkspaceTabContentMatcher {
  /**
   * 检查标签页是否匹配工作区内容
   */
  static async matchTabToWorkspace(tab, workspace) {
    const urlMatch = workspace.websites.some(
      (website) => tab.url.startsWith(website.url) || website.url.startsWith(tab.url)
    );
    if (urlMatch) {
      return {
        isMatch: true,
        confidence: 0.9,
        matchType: "prefix"
      };
    }
    return {
      isMatch: false,
      confidence: 0,
      matchType: "none"
    };
  }
  /**
   * 检查标签页是否为工作区标签页
   */
  static async isWorkspaceTab(tab) {
    if (TabClassificationUtils.isSystemTab(tab.url)) {
      return { isMatch: false };
    }
    return {
      isMatch: true,
      confidence: 0.5
      // 低置信度，因为我们没有具体的工作区匹配逻辑
    };
  }
}
class UserTabsUtils {
  /**
   * 检查标签页是否为系统标签页
   */
  static isSystemTab(url) {
    const systemPrefixes = [
      "chrome://",
      "chrome-extension://",
      "edge://",
      "about:",
      "moz-extension://",
      "safari-extension://"
    ];
    return systemPrefixes.some((prefix) => url.startsWith(prefix));
  }
  /**
   * 检查标签页是否为扩展内部页面
   */
  static isExtensionTab(url) {
    return url.includes("workspace-placeholder.html") || url.startsWith("chrome-extension://") || url === "chrome://newtab/";
  }
}
class TabClassificationUtils {
  /**
   * 使用新的3分类系统判断系统页面
   */
  static isSystemTab(url) {
    return UserTabsUtils.isSystemTab(url) || UserTabsUtils.isExtensionTab(url);
  }
  /**
   * 检查标签页是否为用户标签页
   */
  static isUserTab(url) {
    return !this.isSystemTab(url);
  }
  /**
   * 获取标签页分类
   */
  static getTabCategory(url) {
    if (UserTabsUtils.isSystemTab(url)) {
      return "system";
    }
    if (UserTabsUtils.isExtensionTab(url)) {
      return "extension";
    }
    return "user";
  }
}

class MigrationManager {
  static CURRENT_VERSION = "1.0.0";
  static BACKUP_KEY = "migrationBackup";
  /**
   * 检测当前数据版本
   */
  static async detectDataVersion() {
    try {
      const versionResult = await StorageManager.getDataVersion();
      if (!versionResult.success) {
        return { success: true, data: "0.0.0" };
      }
      return { success: true, data: versionResult.data };
    } catch (error) {
      return {
        success: false,
        error: {
          code: ERROR_CODES.STORAGE_ERROR,
          message: "Failed to detect data version",
          details: error
        }
      };
    }
  }
  /**
   * 执行 Workona 格式迁移
   */
  static async migrateToWorkonaFormat(options = {}) {
    try {
      console.log("🚀 开始 Workona 格式数据迁移...");
      const versionResult = await this.detectDataVersion();
      if (!versionResult.success) {
        return { success: false, error: versionResult.error };
      }
      const currentVersion = versionResult.data;
      if (currentVersion === this.CURRENT_VERSION) {
        console.log("✅ 数据已是最新版本，无需迁移");
        return { success: true, data: false };
      }
      if (options.backupOriginalData !== false) {
        const backupResult = await this.backupOriginalData();
        if (!backupResult.success) {
          console.error("❌ 数据备份失败，中止迁移");
          return { success: false, error: backupResult.error };
        }
        console.log("💾 原始数据备份完成");
      }
      const allDataResult = await StorageManager.getAllData();
      if (!allDataResult.success) {
        return { success: false, error: allDataResult.error };
      }
      const data = allDataResult.data;
      console.log(`📊 检测到 ${data.workspaces.length} 个工作区需要迁移`);
      const migratedWorkspaces = await this.migrateWorkspaces(data.workspaces);
      if (!migratedWorkspaces.success) {
        if (options.rollbackOnError !== false) {
          await this.rollbackMigration();
        }
        return { success: false, error: migratedWorkspaces.error };
      }
      const initResult = await this.initializeWorkonaData();
      if (!initResult.success) {
        if (options.rollbackOnError !== false) {
          await this.rollbackMigration();
        }
        return { success: false, error: initResult.error };
      }
      const versionUpdateResult = await StorageManager.saveDataVersion(this.CURRENT_VERSION);
      if (!versionUpdateResult.success) {
        if (options.rollbackOnError !== false) {
          await this.rollbackMigration();
        }
        return { success: false, error: versionUpdateResult.error };
      }
      if (options.validateAfterMigration !== false) {
        const validationResult = await this.validateMigration();
        if (!validationResult.success) {
          console.error("❌ 迁移验证失败");
          if (options.rollbackOnError !== false) {
            await this.rollbackMigration();
          }
          return { success: false, error: validationResult.error };
        }
        console.log("✅ 迁移验证通过");
      }
      console.log("🎉 Workona 格式迁移完成！");
      return { success: true, data: true };
    } catch (error) {
      console.error("❌ 迁移过程中发生错误:", error);
      if (options.rollbackOnError !== false) {
        await this.rollbackMigration();
      }
      return {
        success: false,
        error: {
          code: ERROR_CODES.STORAGE_ERROR,
          message: "Migration failed",
          details: error
        }
      };
    }
  }
  /**
   * 迁移工作区数据
   */
  static async migrateWorkspaces(workspaces) {
    try {
      const migratedWorkspaces = [];
      for (const workspace of workspaces) {
        const migratedWorkspace = {
          ...workspace,
          // 添加 Workona 风格字段（如果不存在）
          type: workspace.type || "saved",
          pos: workspace.pos || workspace.createdAt,
          state: workspace.state || (workspace.isActive ? "active" : "inactive"),
          workonaTabIds: workspace.workonaTabIds || [],
          sessionId: workspace.sessionId || `session_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
          tabOrder: workspace.tabOrder || []
        };
        migratedWorkspaces.push(migratedWorkspace);
        console.log(`✨ 迁移工作区: ${workspace.name} -> Workona 格式`);
      }
      const saveResult = await StorageManager.saveWorkspaces(migratedWorkspaces);
      if (!saveResult.success) {
        return { success: false, error: saveResult.error };
      }
      return { success: true, data: migratedWorkspaces };
    } catch (error) {
      return {
        success: false,
        error: {
          code: ERROR_CODES.STORAGE_ERROR,
          message: "Failed to migrate workspaces",
          details: error
        }
      };
    }
  }
  /**
   * 初始化 Workona 数据结构
   */
  static async initializeWorkonaData() {
    try {
      await StorageManager.saveTabIdMappings([]);
      await StorageManager.saveLocalOpenWorkspaces({});
      await StorageManager.saveTabGroups({});
      await StorageManager.saveWorkspaceSessions({});
      console.log("🏗️ Workona 数据结构初始化完成");
      return { success: true };
    } catch (error) {
      return {
        success: false,
        error: {
          code: ERROR_CODES.STORAGE_ERROR,
          message: "Failed to initialize Workona data",
          details: error
        }
      };
    }
  }
  /**
   * 备份原始数据
   */
  static async backupOriginalData() {
    try {
      const allDataResult = await StorageManager.getAllData();
      if (!allDataResult.success) {
        return { success: false, error: allDataResult.error };
      }
      const backupData = {
        ...allDataResult.data,
        backupTimestamp: Date.now(),
        backupVersion: await this.detectDataVersion()
      };
      await chrome.storage.local.set({
        [this.BACKUP_KEY]: backupData
      });
      return { success: true };
    } catch (error) {
      return {
        success: false,
        error: {
          code: ERROR_CODES.STORAGE_ERROR,
          message: "Failed to backup original data",
          details: error
        }
      };
    }
  }
  /**
   * 回滚迁移
   */
  static async rollbackMigration() {
    try {
      console.log("🔄 开始回滚迁移...");
      const result = await chrome.storage.local.get([this.BACKUP_KEY]);
      const backupData = result[this.BACKUP_KEY];
      if (!backupData) {
        return {
          success: false,
          error: {
            code: ERROR_CODES.STORAGE_ERROR,
            message: "No backup data found for rollback"
          }
        };
      }
      await StorageManager.saveWorkspaces(backupData.workspaces);
      await StorageManager.saveSettings(backupData.settings);
      await StorageManager.setActiveWorkspaceId(backupData.activeWorkspaceId);
      await chrome.storage.local.remove([
        WORKONA_STORAGE_KEYS.TAB_ID_MAPPINGS,
        WORKONA_STORAGE_KEYS.LOCAL_OPEN_WORKSPACES,
        WORKONA_STORAGE_KEYS.TAB_GROUPS,
        WORKONA_STORAGE_KEYS.WORKSPACE_SESSIONS,
        WORKONA_STORAGE_KEYS.DATA_VERSION
      ]);
      console.log("✅ 迁移回滚完成");
      return { success: true };
    } catch (error) {
      return {
        success: false,
        error: {
          code: ERROR_CODES.STORAGE_ERROR,
          message: "Failed to rollback migration",
          details: error
        }
      };
    }
  }
  /**
   * 验证迁移结果
   */
  static async validateMigration() {
    try {
      const versionResult = await StorageManager.getDataVersion();
      if (!versionResult.success || versionResult.data !== this.CURRENT_VERSION) {
        throw new Error("Data version validation failed");
      }
      const workspacesResult = await StorageManager.getWorkspaces();
      if (!workspacesResult.success) {
        throw new Error("Workspaces validation failed");
      }
      const workspaces = workspacesResult.data;
      for (const workspace of workspaces) {
        if (!workspace.type || !workspace.pos || !workspace.state) {
          throw new Error(`Workspace ${workspace.name} missing Workona fields`);
        }
      }
      const mappingsResult = await StorageManager.getTabIdMappings();
      const sessionsResult = await StorageManager.getWorkspaceSessions();
      if (!mappingsResult.success || !sessionsResult.success) {
        throw new Error("Workona data structures validation failed");
      }
      return { success: true };
    } catch (error) {
      return {
        success: false,
        error: {
          code: ERROR_CODES.STORAGE_ERROR,
          message: "Migration validation failed",
          details: error
        }
      };
    }
  }
  /**
   * 清理备份数据
   */
  static async cleanupBackup() {
    try {
      await chrome.storage.local.remove([this.BACKUP_KEY]);
      console.log("🧹 迁移备份数据已清理");
      return { success: true };
    } catch (error) {
      return {
        success: false,
        error: {
          code: ERROR_CODES.STORAGE_ERROR,
          message: "Failed to cleanup backup",
          details: error
        }
      };
    }
  }
  // === 概念性重构：标签页元数据迁移方法 ===
  /**
   * 迁移现有 Workona ID 映射的元数据（概念性重构）
   */
  static async migrateTabMappingsMetadata() {
    try {
      console.log("🔄 迁移现有 Workona ID 映射的元数据...");
      const mappingsResult = await StorageManager.getTabIdMappings();
      if (!mappingsResult.success) {
        console.log("ℹ️ 没有现有的 Workona ID 映射需要迁移");
        return { success: true, data: 0 };
      }
      const mappings = mappingsResult.data;
      let migratedCount = 0;
      for (const mapping of mappings) {
        if (mapping.hasOwnProperty("isWorkspaceCore") && mapping.hasOwnProperty("tabType")) {
          continue;
        }
        const isWorkspaceCore = !!mapping.websiteId;
        const tabType = isWorkspaceCore ? "core" : "session";
        const source = isWorkspaceCore ? "workspace_website" : "user_opened";
        const updatedMapping = {
          ...mapping,
          isWorkspaceCore,
          tabType,
          metadata: {
            source,
            addedToWorkspaceAt: isWorkspaceCore ? mapping.createdAt : void 0
          }
        };
        const index = mappings.findIndex((m) => m.workonaId === mapping.workonaId);
        if (index >= 0) {
          mappings[index] = updatedMapping;
          migratedCount++;
        }
      }
      if (migratedCount > 0) {
        const saveResult = await StorageManager.saveTabIdMappings(mappings);
        if (saveResult.success) {
          console.log(`✅ 成功迁移 ${migratedCount} 个 Workona ID 映射的元数据`);
          return { success: true, data: migratedCount };
        } else {
          throw new Error("保存迁移后的映射失败");
        }
      } else {
        console.log("ℹ️ 所有 Workona ID 映射已包含最新元数据");
        return { success: true, data: 0 };
      }
    } catch (error) {
      console.error("❌ 迁移 Workona ID 映射元数据失败:", error);
      return {
        success: false,
        error: {
          code: ERROR_CODES.STORAGE_ERROR,
          message: "Failed to migrate tab mappings metadata",
          details: error
        }
      };
    }
  }
}

export { COMMANDS as C, DEFAULT_FAVICON as D, ERROR_CODES as E, MigrationManager as M, StorageManager as S, TabManager as T, UserTabsRealTimeMonitor as U, WORKSPACE_ICONS as W, __vitePreload as _, WORKSPACE_COLORS as a, WorkonaTabManager as b, WorkspaceSwitcher as c, WorkspaceStateSync as d, WorkspaceUserTabsVisibilityManager as e, WorkspaceSessionManager as f, TabClassificationUtils$1 as g, UserTabsRealTimeMonitor$1 as h, workonaTabManager as w };
