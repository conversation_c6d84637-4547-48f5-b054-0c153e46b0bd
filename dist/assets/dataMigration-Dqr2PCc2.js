const STORAGE_KEYS = {
  WORKSPACES: "workspaces",
  SETTINGS: "settings",
  ACTIVE_WORKSPACE_ID: "activeWorkspaceId",
  LAST_ACTIVE_WORKSPACE_IDS: "lastActiveWorkspaceIds"
};
const WORKONA_STORAGE_KEYS = {
  TAB_ID_MAPPINGS: "workonaTabIdMappings",
  LOCAL_OPEN_WORKSPACES: "localOpenWorkspaces",
  TAB_GROUPS: "tabGroups",
  WORKSPACE_SESSIONS: "workspaceSessions",
  GLOBAL_WORKSPACE_WINDOW_ID: "globalWorkspaceWindowId",
  DATA_VERSION: "workonaDataVersion",
  MIGRATION_BACKUP: "migrationBackup"
};
const DEFAULT_SETTINGS = {
  autoCloseOtherTabs: true,
  preserveUserOpenedTabs: false,
  defaultWorkspaceOnStartup: "",
  sidebarWidth: 320,
  theme: "dark",
  showFavicons: true,
  confirmBeforeDelete: true,
  maxRecentWorkspaces: 5
};
const WORKSPACE_COLORS = [
  "#3b82f6",
  // blue
  "#10b981",
  // emerald
  "#f59e0b",
  // amber
  "#ef4444",
  // red
  "#8b5cf6",
  // violet
  "#06b6d4",
  // cyan
  "#84cc16",
  // lime
  "#f97316",
  // orange
  "#ec4899",
  // pink
  "#6366f1"
  // indigo
];
const WORKSPACE_ICONS = [
  "🚀",
  "💼",
  "🔬",
  "🎨",
  "📊",
  "🛠️",
  "📚",
  "💡",
  "🎯",
  "⚡",
  "🌟",
  "🔥",
  "💎",
  "🎪",
  "🎭",
  "🎨",
  "🎵",
  "🎮",
  "🏆",
  "🎊",
  "📱",
  "💻",
  "🖥️",
  "⌨️",
  "🖱️",
  "🖨️",
  "📷",
  "📹",
  "🎥",
  "📺",
  "🔍",
  "🔎",
  "🔬",
  "🔭",
  "📡",
  "🛰️",
  "🚁",
  "✈️",
  "🛸"
];
const ERROR_CODES = {
  WORKSPACE_NOT_FOUND: "WORKSPACE_NOT_FOUND",
  WEBSITE_NOT_FOUND: "WEBSITE_NOT_FOUND",
  STORAGE_ERROR: "STORAGE_ERROR",
  TAB_ERROR: "TAB_ERROR",
  WINDOW_ERROR: "WINDOW_ERROR",
  PERMISSION_DENIED: "PERMISSION_DENIED",
  INVALID_URL: "INVALID_URL",
  DUPLICATE_WORKSPACE: "DUPLICATE_WORKSPACE",
  DUPLICATE_WEBSITE: "DUPLICATE_WEBSITE"
};
const COMMANDS = {
  SWITCH_WORKSPACE_1: "switch-workspace-1",
  SWITCH_WORKSPACE_2: "switch-workspace-2",
  SWITCH_WORKSPACE_3: "switch-workspace-3",
  TOGGLE_SIDEPANEL: "toggle-sidepanel"
};
const DEFAULT_FAVICON = 'data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><circle cx="12" cy="12" r="10"/><path d="m9 12 2 2 4-4"/></svg>';

class ImportDataProcessor {
  /**
   * 处理导入数据后的自动映射补全
   */
  static async processImportedData(importData) {
    try {
      console.log("🔄 开始处理导入数据的系统映射补全...");
      await this.completeWorkspaceWorkonaFields(importData.workspaces);
      await this.generateTabIdMappings(importData.workspaces);
      await this.generateWorkspaceSessions(importData.workspaces);
      await this.initializeSystemMappings();
      console.log("✅ 导入数据系统映射补全完成");
      return { success: true };
    } catch (error) {
      console.error("❌ 导入数据处理失败:", error);
      return {
        success: false,
        error: {
          code: ERROR_CODES.STORAGE_ERROR,
          message: "Failed to process imported data",
          details: error
        }
      };
    }
  }
  /**
   * 补全工作区的 Workona 风格字段
   */
  static async completeWorkspaceWorkonaFields(workspaces) {
    console.log("📝 补全工作区 Workona 风格字段...");
    const updatedWorkspaces = workspaces.map((workspace, index) => {
      const updatedWorkspace = {
        ...workspace,
        // 如果缺少 Workona 字段，自动补全
        type: workspace.type || "saved",
        pos: workspace.pos || Date.now() + index,
        state: workspace.state || "inactive",
        workonaTabIds: workspace.workonaTabIds || [],
        sessionId: workspace.sessionId || `session_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
        tabOrder: workspace.tabOrder || [],
        isActive: false
        // 导入后所有工作区都不激活
      };
      if (!updatedWorkspace.workonaTabIds || updatedWorkspace.workonaTabIds.length === 0 && updatedWorkspace.websites.length > 0) {
        updatedWorkspace.workonaTabIds = updatedWorkspace.websites.map(
          (website) => `t-${updatedWorkspace.id}-${website.id}`
        );
      }
      if (!updatedWorkspace.tabOrder || updatedWorkspace.tabOrder.length === 0 && updatedWorkspace.websites.length > 0) {
        updatedWorkspace.tabOrder = updatedWorkspace.websites.map((website) => website.id);
      }
      return updatedWorkspace;
    });
    await StorageManager.saveWorkspaces(updatedWorkspaces);
    console.log(`✅ 已补全 ${updatedWorkspaces.length} 个工作区的 Workona 字段`);
  }
  /**
   * 生成标签页ID映射
   */
  static async generateTabIdMappings(workspaces) {
    console.log("🔗 生成标签页ID映射...");
    const tabIdMappings = [];
    for (const workspace of workspaces) {
      for (const website of workspace.websites) {
        const workonaId = `t-${workspace.id}-${website.id}`;
        const mapping = {
          workonaId,
          chromeId: -1,
          // 导入时没有实际的 Chrome 标签页ID，使用-1表示无效
          workspaceId: workspace.id,
          websiteId: website.id,
          isWorkspaceCore: true,
          // 工作区网站默认为核心标签页
          tabType: "core",
          // 添加必需的tabType字段
          createdAt: Date.now(),
          lastSyncAt: Date.now(),
          // 添加必需的lastSyncAt字段
          metadata: {
            source: "workspace_website",
            addedToWorkspaceAt: Date.now(),
            isPinned: website.isPinned || false,
            pinnedAt: website.isPinned ? Date.now() : void 0
          }
        };
        tabIdMappings.push(mapping);
      }
    }
    await StorageManager.saveTabIdMappings(tabIdMappings);
    console.log(`✅ 已生成 ${tabIdMappings.length} 个标签页ID映射`);
  }
  /**
   * 生成工作区会话数据
   */
  static async generateWorkspaceSessions(workspaces) {
    console.log("📊 生成工作区会话数据...");
    const workspaceSessions = {};
    for (const workspace of workspaces) {
      const session = {
        workspaceId: workspace.id,
        lastAccessedAt: Date.now(),
        tabOrder: workspace.tabOrder || workspace.websites.map((w) => w.id),
        activeTabId: workspace.websites.length > 0 ? workspace.websites[0].id : void 0,
        windowId: void 0,
        // 导入时没有实际的窗口ID
        isActive: false,
        metadata: {
          totalTabs: workspace.websites.length,
          coreTabsCount: workspace.websites.length,
          sessionTabsCount: 0,
          lastSwitchedAt: Date.now(),
          source: "imported_data"
        }
      };
      workspaceSessions[workspace.id] = session;
    }
    await StorageManager.saveWorkspaceSessions(workspaceSessions);
    console.log(`✅ 已生成 ${Object.keys(workspaceSessions).length} 个工作区会话`);
  }
  /**
   * 初始化其他系统映射
   */
  static async initializeSystemMappings() {
    console.log("🔧 初始化其他系统映射...");
    await StorageManager.saveLocalOpenWorkspaces({});
    await StorageManager.saveTabGroups({});
    await StorageManager.clearGlobalWorkspaceWindowId();
    await StorageManager.saveDataVersion("2.0.0");
    console.log("✅ 系统映射初始化完成");
  }
  /**
   * 验证导入数据的完整性
   */
  static validateImportedData(importData) {
    const errors = [];
    if (!importData.workspaces || !Array.isArray(importData.workspaces)) {
      errors.push("缺少工作区数据或格式错误");
    }
    if (importData.workspaces) {
      for (let i = 0; i < importData.workspaces.length; i++) {
        const workspace = importData.workspaces[i];
        if (!workspace.id) {
          errors.push(`工作区 ${i + 1} 缺少ID`);
        }
        if (!workspace.name) {
          errors.push(`工作区 ${i + 1} 缺少名称`);
        }
        if (!workspace.websites || !Array.isArray(workspace.websites)) {
          errors.push(`工作区 ${i + 1} 缺少网站数据或格式错误`);
        } else {
          for (let j = 0; j < workspace.websites.length; j++) {
            const website = workspace.websites[j];
            if (!website.id) {
              errors.push(`工作区 ${i + 1} 的网站 ${j + 1} 缺少ID`);
            }
            if (!website.url) {
              errors.push(`工作区 ${i + 1} 的网站 ${j + 1} 缺少URL`);
            }
          }
        }
      }
    }
    return {
      isValid: errors.length === 0,
      errors
    };
  }
  /**
   * 清理导入数据中的无效字段
   */
  static cleanImportData(importData) {
    const cleanedData = { ...importData };
    if (cleanedData.workspaces) {
      cleanedData.workspaces = cleanedData.workspaces.map((workspace) => ({
        ...workspace,
        isActive: false,
        // 确保导入后所有工作区都不激活
        // 移除可能导致冲突的字段
        windowId: void 0,
        lastActiveAt: void 0
      }));
    }
    cleanedData.activeWorkspaceId = null;
    cleanedData.globalWorkspaceWindowId = null;
    return cleanedData;
  }
}

class StorageManager {
  /**
   * 获取所有存储数据
   */
  static async getAllData() {
    try {
      const result = await chrome.storage.local.get([
        STORAGE_KEYS.WORKSPACES,
        STORAGE_KEYS.SETTINGS,
        STORAGE_KEYS.ACTIVE_WORKSPACE_ID,
        STORAGE_KEYS.LAST_ACTIVE_WORKSPACE_IDS,
        // Workona 风格数据
        WORKONA_STORAGE_KEYS.TAB_ID_MAPPINGS,
        WORKONA_STORAGE_KEYS.LOCAL_OPEN_WORKSPACES,
        WORKONA_STORAGE_KEYS.TAB_GROUPS,
        WORKONA_STORAGE_KEYS.WORKSPACE_SESSIONS,
        WORKONA_STORAGE_KEYS.GLOBAL_WORKSPACE_WINDOW_ID,
        WORKONA_STORAGE_KEYS.DATA_VERSION
      ]);
      const data = {
        workspaces: result[STORAGE_KEYS.WORKSPACES] || [],
        settings: { ...DEFAULT_SETTINGS, ...result[STORAGE_KEYS.SETTINGS] },
        activeWorkspaceId: result[STORAGE_KEYS.ACTIVE_WORKSPACE_ID] || null,
        lastActiveWorkspaceIds: result[STORAGE_KEYS.LAST_ACTIVE_WORKSPACE_IDS] || [],
        // Workona 风格扩展数据（可选）
        tabIdMappings: result[WORKONA_STORAGE_KEYS.TAB_ID_MAPPINGS] || [],
        localOpenWorkspaces: result[WORKONA_STORAGE_KEYS.LOCAL_OPEN_WORKSPACES] || {},
        tabGroups: result[WORKONA_STORAGE_KEYS.TAB_GROUPS] || {},
        workspaceSessions: result[WORKONA_STORAGE_KEYS.WORKSPACE_SESSIONS] || {},
        globalWorkspaceWindowId: result[WORKONA_STORAGE_KEYS.GLOBAL_WORKSPACE_WINDOW_ID] || void 0,
        dataVersion: result[WORKONA_STORAGE_KEYS.DATA_VERSION] || "1.0.0"
      };
      return { success: true, data };
    } catch (error) {
      return {
        success: false,
        error: {
          code: ERROR_CODES.STORAGE_ERROR,
          message: "Failed to get storage data",
          details: error
        }
      };
    }
  }
  /**
   * 获取所有工作区
   */
  static async getWorkspaces() {
    try {
      const result = await chrome.storage.local.get(STORAGE_KEYS.WORKSPACES);
      const workspaces = result[STORAGE_KEYS.WORKSPACES] || [];
      workspaces.sort((a, b) => a.order - b.order);
      return { success: true, data: workspaces };
    } catch (error) {
      return {
        success: false,
        error: {
          code: ERROR_CODES.STORAGE_ERROR,
          message: "Failed to get workspaces",
          details: error
        }
      };
    }
  }
  /**
   * 保存工作区列表
   */
  static async saveWorkspaces(workspaces) {
    try {
      await chrome.storage.local.set({
        [STORAGE_KEYS.WORKSPACES]: workspaces
      });
      return { success: true };
    } catch (error) {
      return {
        success: false,
        error: {
          code: ERROR_CODES.STORAGE_ERROR,
          message: "Failed to save workspaces",
          details: error
        }
      };
    }
  }
  /**
   * 获取单个工作区
   */
  static async getWorkspace(id) {
    const result = await this.getWorkspaces();
    if (!result.success) {
      return {
        success: false,
        error: result.error
      };
    }
    const workspace = result.data.find((w) => w.id === id);
    if (!workspace) {
      return {
        success: false,
        error: {
          code: ERROR_CODES.WORKSPACE_NOT_FOUND,
          message: `Workspace with id ${id} not found`
        }
      };
    }
    return { success: true, data: workspace };
  }
  /**
   * 获取设置
   */
  static async getSettings() {
    try {
      const result = await chrome.storage.local.get(STORAGE_KEYS.SETTINGS);
      const settings = { ...DEFAULT_SETTINGS, ...result[STORAGE_KEYS.SETTINGS] };
      return { success: true, data: settings };
    } catch (error) {
      return {
        success: false,
        error: {
          code: ERROR_CODES.STORAGE_ERROR,
          message: "Failed to get settings",
          details: error
        }
      };
    }
  }
  /**
   * 保存设置
   */
  static async saveSettings(settings) {
    try {
      const currentResult = await this.getSettings();
      if (!currentResult.success) {
        return {
          success: false,
          error: currentResult.error
        };
      }
      const updatedSettings = { ...currentResult.data, ...settings };
      await chrome.storage.local.set({
        [STORAGE_KEYS.SETTINGS]: updatedSettings
      });
      return { success: true };
    } catch (error) {
      return {
        success: false,
        error: {
          code: ERROR_CODES.STORAGE_ERROR,
          message: "Failed to save settings",
          details: error
        }
      };
    }
  }
  /**
   * 获取当前活跃工作区ID
   */
  static async getActiveWorkspaceId() {
    try {
      const result = await chrome.storage.local.get(STORAGE_KEYS.ACTIVE_WORKSPACE_ID);
      return { success: true, data: result[STORAGE_KEYS.ACTIVE_WORKSPACE_ID] || null };
    } catch (error) {
      return {
        success: false,
        error: {
          code: ERROR_CODES.STORAGE_ERROR,
          message: "Failed to get active workspace ID",
          details: error
        }
      };
    }
  }
  /**
   * 设置当前活跃工作区ID
   */
  static async setActiveWorkspaceId(id) {
    try {
      const currentIdResult = await this.getActiveWorkspaceId();
      const currentId = currentIdResult.success ? currentIdResult.data : null;
      if (currentId !== id) {
        const stack = new Error().stack;
        const caller = stack?.split("\n")[2]?.trim() || "unknown";
        if (id === null) {
          console.warn(`🚨 工作区状态被清除: ${currentId} -> null`);
          console.warn(`🔍 调用来源: ${caller}`);
          console.warn(`📋 调用栈:`, stack);
        } else {
          console.log(`🔄 工作区状态变化: ${currentId} -> ${id}`);
          console.log(`🔍 调用来源: ${caller}`);
        }
      }
      await chrome.storage.local.set({
        [STORAGE_KEYS.ACTIVE_WORKSPACE_ID]: id
      });
      if (id) {
        await this.updateLastActiveWorkspaces(id);
      }
      return { success: true };
    } catch (error) {
      return {
        success: false,
        error: {
          code: ERROR_CODES.STORAGE_ERROR,
          message: "Failed to set active workspace ID",
          details: error
        }
      };
    }
  }
  /**
   * 更新最近使用的工作区列表
   */
  static async updateLastActiveWorkspaces(workspaceId) {
    try {
      const result = await chrome.storage.local.get(STORAGE_KEYS.LAST_ACTIVE_WORKSPACE_IDS);
      let lastActiveIds = result[STORAGE_KEYS.LAST_ACTIVE_WORKSPACE_IDS] || [];
      lastActiveIds = lastActiveIds.filter((id) => id !== workspaceId);
      lastActiveIds.unshift(workspaceId);
      const settingsResult = await this.getSettings();
      const maxRecent = settingsResult.success ? settingsResult.data.maxRecentWorkspaces : 5;
      lastActiveIds = lastActiveIds.slice(0, maxRecent);
      await chrome.storage.local.set({
        [STORAGE_KEYS.LAST_ACTIVE_WORKSPACE_IDS]: lastActiveIds
      });
      return { success: true };
    } catch (error) {
      return {
        success: false,
        error: {
          code: ERROR_CODES.STORAGE_ERROR,
          message: "Failed to update last active workspaces",
          details: error
        }
      };
    }
  }
  /**
   * 清除所有数据（增强版：确保清除所有存储键）
   */
  static async clearAll() {
    try {
      console.log("🗑️ 开始清除所有数据...");
      const allKeys = [
        // 基础存储键
        ...Object.values(STORAGE_KEYS),
        // Workona 存储键
        ...Object.values(WORKONA_STORAGE_KEYS)
      ];
      const allStoredData = await chrome.storage.local.get(null);
      const storedKeys = Object.keys(allStoredData);
      const keysToRemove = storedKeys.filter((key) => {
        return key.startsWith("workspacePinnedTabIds_") || key.includes("-hidden-tabs") || allKeys.includes(key);
      });
      console.log(`🔍 发现 ${keysToRemove.length} 个存储键需要清除`);
      await chrome.storage.local.clear();
      console.log("✅ 所有数据已清除");
      return { success: true };
    } catch (error) {
      console.error("❌ 清除数据失败:", error);
      return {
        success: false,
        error: {
          code: ERROR_CODES.STORAGE_ERROR,
          message: "Failed to clear storage",
          details: error
        }
      };
    }
  }
  /**
   * 监听存储变化
   */
  static onChanged(callback) {
    chrome.storage.onChanged.addListener((changes, areaName) => {
      if (areaName === "local") {
        callback(changes);
      }
    });
  }
  /**
   * 导出数据（增强版：包含完整的工作区数据结构）
   */
  static async exportData() {
    try {
      const dataResult = await this.getAllData();
      if (!dataResult.success) {
        return {
          success: false,
          error: dataResult.error
        };
      }
      const data = dataResult.data;
      const exportData = {
        version: "2.0.0",
        // 升级版本号以支持新的数据结构
        exportedAt: Date.now(),
        // 基础数据
        workspaces: data.workspaces,
        settings: data.settings,
        activeWorkspaceId: data.activeWorkspaceId,
        lastActiveWorkspaceIds: data.lastActiveWorkspaceIds,
        // 工作区增强数据
        workspaceSessions: data.workspaceSessions || {},
        tabIdMappings: data.tabIdMappings || [],
        localOpenWorkspaces: data.localOpenWorkspaces || {},
        tabGroups: data.tabGroups || {},
        globalWorkspaceWindowId: data.globalWorkspaceWindowId,
        dataVersion: data.dataVersion || "1.0.0",
        // 导出元数据
        exportMetadata: {
          totalWorkspaces: data.workspaces.length,
          totalWebsites: data.workspaces.reduce((sum, ws) => sum + ws.websites.length, 0),
          totalSessions: Object.keys(data.workspaceSessions || {}).length,
          totalTabMappings: (data.tabIdMappings || []).length,
          hasActiveWorkspace: !!data.activeWorkspaceId,
          exportSource: "WorkSpace Pro Chrome Extension"
        }
      };
      console.log(`📦 导出完整数据: ${exportData.exportMetadata.totalWorkspaces} 个工作区, ${exportData.exportMetadata.totalWebsites} 个网站, ${exportData.exportMetadata.totalSessions} 个会话`);
      return { success: true, data: JSON.stringify(exportData, null, 2) };
    } catch (error) {
      return {
        success: false,
        error: {
          code: ERROR_CODES.STORAGE_ERROR,
          message: "Failed to export data",
          details: error
        }
      };
    }
  }
  /**
   * 导入数据（增强版：支持完整数据结构和版本兼容）
   */
  static async importData(jsonData) {
    try {
      let importData = JSON.parse(jsonData);
      const validation = ImportDataProcessor.validateImportedData(importData);
      if (!validation.isValid) {
        throw new Error(`数据验证失败: ${validation.errors.join(", ")}`);
      }
      importData = ImportDataProcessor.cleanImportData(importData);
      const version = importData.version || "1.0.0";
      console.log(`📥 导入数据版本: ${version}`);
      if (!importData.workspaces || !Array.isArray(importData.workspaces)) {
        throw new Error("Invalid data format: workspaces array is required");
      }
      const existingWorkspacesResult = await this.getWorkspaces();
      const existingWorkspaces = existingWorkspacesResult.success ? existingWorkspacesResult.data : [];
      console.log(`📋 开始增量导入 ${importData.workspaces.length} 个工作区（现有 ${existingWorkspaces.length} 个）`);
      const importResult = await this.performIncrementalImport(existingWorkspaces, importData.workspaces);
      await this.saveWorkspaces(importResult.mergedWorkspaces);
      console.log(`✅ 增量导入完成: 新增 ${importResult.addedWorkspaces} 个工作区, 新增 ${importResult.addedWebsites} 个网站, 跳过 ${importResult.skippedWorkspaces} 个重复工作区`);
      if (importData.settings) {
        console.log("⚙️ 导入设置配置");
        await this.saveSettings(importData.settings);
      }
      if (version === "2.0.0" || importData.workspaceSessions) {
        console.log("🔄 导入工作区会话数据");
        if (importData.workspaceSessions) {
          await this.saveWorkspaceSessions(importData.workspaceSessions);
          console.log(`📊 导入 ${Object.keys(importData.workspaceSessions).length} 个工作区会话`);
        }
        if (importData.tabIdMappings) {
          await this.saveTabIdMappings(importData.tabIdMappings);
          console.log(`🔗 导入 ${importData.tabIdMappings.length} 个标签页ID映射`);
        }
        if (importData.localOpenWorkspaces) {
          await this.saveLocalOpenWorkspaces(importData.localOpenWorkspaces);
          console.log(`💻 导入本地打开工作区数据`);
        }
        if (importData.tabGroups) {
          await this.saveTabGroups(importData.tabGroups);
          console.log(`📁 导入标签组数据`);
        }
        if (importData.globalWorkspaceWindowId) {
          await chrome.storage.local.set({
            [WORKONA_STORAGE_KEYS.GLOBAL_WORKSPACE_WINDOW_ID]: importData.globalWorkspaceWindowId
          });
          console.log(`🪟 导入全局工作区窗口ID`);
        }
        if (importData.dataVersion) {
          await this.saveDataVersion(importData.dataVersion);
        }
      } else {
        console.log("🔄 初始化增强数据结构（兼容旧版本）");
        await this.saveWorkspaceSessions({});
        await this.saveTabIdMappings([]);
        await this.saveLocalOpenWorkspaces({});
        await this.saveTabGroups({});
        await this.saveDataVersion("2.0.0");
      }
      console.log("🔄 开始系统映射自动补全...");
      const processingResult = await ImportDataProcessor.processImportedData(importData);
      if (!processingResult.success) {
        console.warn("⚠️ 系统映射补全失败，但导入数据已保存:", processingResult.error);
      }
      console.log(`✅ 增量导入完成: 新增 ${importResult.addedWorkspaces} 个工作区, 新增 ${importResult.addedWebsites} 个网站`);
      if (importResult.skippedWorkspaces > 0) {
        console.log(`ℹ️ 跳过 ${importResult.skippedWorkspaces} 个重复工作区`);
      }
      return { success: true };
    } catch (error) {
      console.error("❌ 导入数据失败:", error);
      return {
        success: false,
        error: {
          code: ERROR_CODES.STORAGE_ERROR,
          message: "Failed to import data",
          details: error
        }
      };
    }
  }
  // ===== Workona 风格存储方法 =====
  /**
   * 保存标签页ID映射表
   */
  static async saveTabIdMappings(mappings) {
    try {
      await chrome.storage.local.set({
        [WORKONA_STORAGE_KEYS.TAB_ID_MAPPINGS]: mappings
      });
      return { success: true };
    } catch (error) {
      return {
        success: false,
        error: {
          code: ERROR_CODES.STORAGE_ERROR,
          message: "Failed to save tab ID mappings",
          details: error
        }
      };
    }
  }
  /**
   * 获取标签页ID映射表
   */
  static async getTabIdMappings() {
    try {
      const result = await chrome.storage.local.get([WORKONA_STORAGE_KEYS.TAB_ID_MAPPINGS]);
      const mappings = result[WORKONA_STORAGE_KEYS.TAB_ID_MAPPINGS] || [];
      return { success: true, data: mappings };
    } catch (error) {
      return {
        success: false,
        error: {
          code: ERROR_CODES.STORAGE_ERROR,
          message: "Failed to get tab ID mappings",
          details: error
        }
      };
    }
  }
  /**
   * 保存本地打开工作区
   */
  static async saveLocalOpenWorkspaces(workspaces) {
    try {
      await chrome.storage.local.set({
        [WORKONA_STORAGE_KEYS.LOCAL_OPEN_WORKSPACES]: workspaces
      });
      return { success: true };
    } catch (error) {
      return {
        success: false,
        error: {
          code: ERROR_CODES.STORAGE_ERROR,
          message: "Failed to save local open workspaces",
          details: error
        }
      };
    }
  }
  /**
   * 获取本地打开工作区
   */
  static async getLocalOpenWorkspaces() {
    try {
      const result = await chrome.storage.local.get([WORKONA_STORAGE_KEYS.LOCAL_OPEN_WORKSPACES]);
      const workspaces = result[WORKONA_STORAGE_KEYS.LOCAL_OPEN_WORKSPACES] || {};
      return { success: true, data: workspaces };
    } catch (error) {
      return {
        success: false,
        error: {
          code: ERROR_CODES.STORAGE_ERROR,
          message: "Failed to get local open workspaces",
          details: error
        }
      };
    }
  }
  /**
   * 保存标签组信息
   */
  static async saveTabGroups(tabGroups) {
    try {
      await chrome.storage.local.set({
        [WORKONA_STORAGE_KEYS.TAB_GROUPS]: tabGroups
      });
      return { success: true };
    } catch (error) {
      return {
        success: false,
        error: {
          code: ERROR_CODES.STORAGE_ERROR,
          message: "Failed to save tab groups",
          details: error
        }
      };
    }
  }
  /**
   * 获取标签组信息
   */
  static async getTabGroups() {
    try {
      const result = await chrome.storage.local.get([WORKONA_STORAGE_KEYS.TAB_GROUPS]);
      const tabGroups = result[WORKONA_STORAGE_KEYS.TAB_GROUPS] || {};
      return { success: true, data: tabGroups };
    } catch (error) {
      return {
        success: false,
        error: {
          code: ERROR_CODES.STORAGE_ERROR,
          message: "Failed to get tab groups",
          details: error
        }
      };
    }
  }
  /**
   * 保存工作区会话
   */
  static async saveWorkspaceSessions(sessions) {
    try {
      await chrome.storage.local.set({
        [WORKONA_STORAGE_KEYS.WORKSPACE_SESSIONS]: sessions
      });
      return { success: true };
    } catch (error) {
      return {
        success: false,
        error: {
          code: ERROR_CODES.STORAGE_ERROR,
          message: "Failed to save workspace sessions",
          details: error
        }
      };
    }
  }
  /**
   * 获取工作区会话
   */
  static async getWorkspaceSessions() {
    try {
      const result = await chrome.storage.local.get([WORKONA_STORAGE_KEYS.WORKSPACE_SESSIONS]);
      const sessions = result[WORKONA_STORAGE_KEYS.WORKSPACE_SESSIONS] || {};
      return { success: true, data: sessions };
    } catch (error) {
      return {
        success: false,
        error: {
          code: ERROR_CODES.STORAGE_ERROR,
          message: "Failed to get workspace sessions",
          details: error
        }
      };
    }
  }
  /**
   * 保存全局工作区窗口ID
   */
  static async saveGlobalWorkspaceWindowId(windowId) {
    try {
      await chrome.storage.local.set({
        [WORKONA_STORAGE_KEYS.GLOBAL_WORKSPACE_WINDOW_ID]: windowId
      });
      return { success: true };
    } catch (error) {
      return {
        success: false,
        error: {
          code: ERROR_CODES.STORAGE_ERROR,
          message: "Failed to save global workspace window ID",
          details: error
        }
      };
    }
  }
  /**
   * 获取全局工作区窗口ID
   */
  static async getGlobalWorkspaceWindowId() {
    try {
      const result = await chrome.storage.local.get([WORKONA_STORAGE_KEYS.GLOBAL_WORKSPACE_WINDOW_ID]);
      const windowId = result[WORKONA_STORAGE_KEYS.GLOBAL_WORKSPACE_WINDOW_ID] || null;
      return { success: true, data: windowId };
    } catch (error) {
      return {
        success: false,
        error: {
          code: ERROR_CODES.STORAGE_ERROR,
          message: "Failed to get global workspace window ID",
          details: error
        }
      };
    }
  }
  /**
   * 保存数据版本
   */
  static async saveDataVersion(version) {
    try {
      await chrome.storage.local.set({
        [WORKONA_STORAGE_KEYS.DATA_VERSION]: version
      });
      return { success: true };
    } catch (error) {
      return {
        success: false,
        error: {
          code: ERROR_CODES.STORAGE_ERROR,
          message: "Failed to save data version",
          details: error
        }
      };
    }
  }
  /**
   * 获取数据版本
   */
  static async getDataVersion() {
    try {
      const result = await chrome.storage.local.get([WORKONA_STORAGE_KEYS.DATA_VERSION]);
      const version = result[WORKONA_STORAGE_KEYS.DATA_VERSION] || "1.0.0";
      return { success: true, data: version };
    } catch (error) {
      return {
        success: false,
        error: {
          code: ERROR_CODES.STORAGE_ERROR,
          message: "Failed to get data version",
          details: error
        }
      };
    }
  }
  /**
   * 清除全局工作区窗口ID
   */
  static async clearGlobalWorkspaceWindowId() {
    try {
      await chrome.storage.local.remove([WORKONA_STORAGE_KEYS.GLOBAL_WORKSPACE_WINDOW_ID]);
      return { success: true };
    } catch (error) {
      return {
        success: false,
        error: {
          code: ERROR_CODES.STORAGE_ERROR,
          message: "Failed to clear global workspace window ID",
          details: error
        }
      };
    }
  }
  /**
   * 渐进式数据迁移检查
   * 检测现有数据格式，逐步迁移到 Workona 格式
   */
  static async migrateToWorkonaFormat() {
    try {
      console.log("🔄 开始检查 Workona 数据迁移需求...");
      const versionResult = await this.getDataVersion();
      if (!versionResult.success) {
        return { success: false, error: versionResult.error };
      }
      const currentVersion = versionResult.data;
      const targetVersion = "1.0.0";
      if (currentVersion === targetVersion) {
        console.log("✅ 数据版本已是最新，无需迁移");
        return { success: true, data: false };
      }
      console.log(`📦 检测到数据版本 ${currentVersion}，开始迁移到 ${targetVersion}...`);
      const allDataResult = await this.getAllData();
      if (!allDataResult.success) {
        return { success: false, error: allDataResult.error };
      }
      const data = allDataResult.data;
      if (!data.tabIdMappings) {
        await this.saveTabIdMappings([]);
      }
      if (!data.localOpenWorkspaces) {
        await this.saveLocalOpenWorkspaces({});
      }
      if (!data.tabGroups) {
        await this.saveTabGroups({});
      }
      if (!data.workspaceSessions) {
        await this.saveWorkspaceSessions({});
      }
      await this.saveDataVersion(targetVersion);
      console.log("✅ Workona 数据迁移完成");
      return { success: true, data: true };
    } catch (error) {
      console.error("❌ Workona 数据迁移失败:", error);
      return {
        success: false,
        error: {
          code: ERROR_CODES.STORAGE_ERROR,
          message: "Failed to migrate to Workona format",
          details: error
        }
      };
    }
  }
  /**
   * 执行增量导入逻辑（极简重构版 - 绝对安全）
   *
   * 核心原则：
   * 1. 永远不删除现有数据
   * 2. 只进行添加操作
   * 3. 使用最简单的逻辑
   * 4. 每个操作都是原子的和可验证的
   */
  static async performIncrementalImport(existingWorkspaces, importWorkspaces) {
    console.log(`🚀 开始极简增量导入`);
    console.log(`📊 现有工作区: ${existingWorkspaces.length} 个, 导入工作区: ${importWorkspaces.length} 个`);
    const result = JSON.parse(JSON.stringify(existingWorkspaces));
    console.log(`✅ 深拷贝现有工作区完成: ${result.length} 个`);
    let addedWorkspaces = 0;
    let addedWebsites = 0;
    let skippedWorkspaces = 0;
    for (const importWorkspace of importWorkspaces) {
      console.log(`
🔄 处理导入工作区: "${importWorkspace.name}"`);
      const existingWorkspace = result.find((ws) => ws.name.toLowerCase() === importWorkspace.name.toLowerCase());
      if (existingWorkspace) {
        console.log(`📝 发现同名工作区，开始合并网站...`);
        const addedCount = this.safelyMergeWebsites(existingWorkspace, importWorkspace);
        addedWebsites += addedCount;
        skippedWorkspaces++;
        console.log(`✅ 合并完成: 向 "${importWorkspace.name}" 添加了 ${addedCount} 个新网站`);
      } else {
        console.log(`🆕 创建新工作区...`);
        const newWorkspace = this.safelyCreateNewWorkspace(importWorkspace, result);
        result.push(newWorkspace);
        addedWorkspaces++;
        const websiteCount = newWorkspace.websites ? newWorkspace.websites.length : 0;
        addedWebsites += websiteCount;
        console.log(`✅ 创建完成: "${newWorkspace.name}" 包含 ${websiteCount} 个网站`);
      }
    }
    console.log(`
🎉 增量导入完成!`);
    console.log(`📊 最终结果: 工作区 ${result.length} 个, 新增工作区 ${addedWorkspaces} 个, 合并工作区 ${skippedWorkspaces} 个, 新增网站 ${addedWebsites} 个`);
    return {
      mergedWorkspaces: result,
      addedWorkspaces,
      addedWebsites,
      skippedWorkspaces
    };
  }
  /**
   * 安全地合并网站到现有工作区（极简版 - 绝对不会删除现有网站）
   */
  static safelyMergeWebsites(existingWorkspace, importWorkspace) {
    console.log(`📊 合并前: 现有工作区 "${existingWorkspace.name}" 有 ${existingWorkspace.websites?.length || 0} 个网站`);
    if (!existingWorkspace.websites) {
      existingWorkspace.websites = [];
    }
    const originalCount = existingWorkspace.websites.length;
    console.log(`📋 现有网站列表:`, existingWorkspace.websites.map((w) => `${w.title} (${w.url})`));
    const existingUrls = /* @__PURE__ */ new Set();
    existingWorkspace.websites.forEach((website) => {
      existingUrls.add(website.url.toLowerCase());
    });
    let addedCount = 0;
    if (importWorkspace.websites && importWorkspace.websites.length > 0) {
      console.log(`📋 导入网站列表:`, importWorkspace.websites.map((w) => `${w.title} (${w.url})`));
      for (const importWebsite of importWorkspace.websites) {
        const urlLower = importWebsite.url.toLowerCase();
        if (!existingUrls.has(urlLower)) {
          const newWebsite = {
            ...JSON.parse(JSON.stringify(importWebsite)),
            id: `site_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`
          };
          existingWorkspace.websites.push(newWebsite);
          existingUrls.add(urlLower);
          addedCount++;
          console.log(`✅ 添加新网站: ${importWebsite.title} (${importWebsite.url})`);
        } else {
          console.log(`⏭️ 跳过重复网站: ${importWebsite.title} (${importWebsite.url})`);
        }
      }
    }
    existingWorkspace.updatedAt = Date.now();
    const finalCount = existingWorkspace.websites.length;
    const expectedCount = originalCount + addedCount;
    console.log(`📊 合并后: 工作区 "${existingWorkspace.name}" 有 ${finalCount} 个网站`);
    console.log(`📋 最终网站列表:`, existingWorkspace.websites.map((w) => `${w.title} (${w.url})`));
    if (finalCount !== expectedCount) {
      console.error(`❌ 严重错误! 网站数量不匹配: 预期 ${expectedCount}, 实际 ${finalCount}`);
      throw new Error(`网站合并验证失败: ${existingWorkspace.name}`);
    }
    console.log(`✅ 验证通过: 保留 ${originalCount} 个现有网站, 新增 ${addedCount} 个网站`);
    return addedCount;
  }
  /**
   * 安全地创建新工作区（极简版 - 检查全局URL重复）
   */
  static safelyCreateNewWorkspace(importWorkspace, existingWorkspaces) {
    console.log(`🆕 创建新工作区: "${importWorkspace.name}"`);
    const globalUrls = /* @__PURE__ */ new Set();
    existingWorkspaces.forEach((workspace) => {
      if (workspace.websites) {
        workspace.websites.forEach((website) => {
          globalUrls.add(website.url.toLowerCase());
        });
      }
    });
    const processedWebsites = [];
    if (importWorkspace.websites && importWorkspace.websites.length > 0) {
      console.log(`📋 导入网站列表:`, importWorkspace.websites.map((w) => `${w.title} (${w.url})`));
      for (const importWebsite of importWorkspace.websites) {
        const urlLower = importWebsite.url.toLowerCase();
        if (!globalUrls.has(urlLower)) {
          const newWebsite = {
            ...JSON.parse(JSON.stringify(importWebsite)),
            id: `site_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`
          };
          processedWebsites.push(newWebsite);
          globalUrls.add(urlLower);
          console.log(`✅ 添加网站: ${importWebsite.title} (${importWebsite.url})`);
        } else {
          console.log(`⏭️ 跳过全局重复网站: ${importWebsite.title} (${importWebsite.url})`);
        }
      }
    }
    const newWorkspace = {
      ...JSON.parse(JSON.stringify(importWorkspace)),
      id: `ws_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
      websites: processedWebsites,
      isActive: false,
      createdAt: Date.now(),
      updatedAt: Date.now()
    };
    console.log(`✅ 新工作区创建完成: "${newWorkspace.name}" 包含 ${processedWebsites.length} 个网站`);
    console.log(`📋 网站列表:`, processedWebsites.map((w) => `${w.title} (${w.url})`));
    return newWorkspace;
  }
}

class WorkspaceStateSync {
  /**
   * 发送工作区状态更新事件
   */
  static sendWorkspaceStateUpdate(workspaceId, eventType) {
    try {
      if (typeof window !== "undefined") {
        const eventName = eventType === "switch" ? "workspaceSwitchComplete" : "userTabsVisibilityChanged";
        const event = new CustomEvent(eventName, {
          detail: { workspaceId }
        });
        window.dispatchEvent(event);
      }
      if (typeof chrome !== "undefined" && chrome.runtime) {
        const messageType = eventType === "switch" ? "WORKSPACE_SWITCH_COMPLETE" : "USER_TABS_VISIBILITY_CHANGED";
        chrome.runtime.sendMessage({
          type: messageType,
          workspaceId
        }).catch((error) => {
          console.log(`发送${eventType}事件消息失败:`, error);
        });
      }
    } catch (error) {
      console.error(`发送工作区状态更新事件失败:`, error);
    }
  }
  /**
   * 添加工作区状态监听器
   */
  static addStateListener(callback) {
    const handleWorkspaceSwitchComplete = (event) => {
      callback(event.detail.workspaceId, "switch");
    };
    const handleUserTabsVisibilityChanged = (event) => {
      callback(event.detail.workspaceId, "userTabsVisibility");
    };
    const handleChromeMessage = (message) => {
      if (message.type === "WORKSPACE_SWITCH_COMPLETE") {
        callback(message.workspaceId, "switch");
      } else if (message.type === "USER_TABS_VISIBILITY_CHANGED") {
        callback(message.workspaceId, "userTabsVisibility");
      }
    };
    if (typeof window !== "undefined") {
      window.addEventListener("workspaceSwitchComplete", handleWorkspaceSwitchComplete);
      window.addEventListener("userTabsVisibilityChanged", handleUserTabsVisibilityChanged);
    }
    if (typeof chrome !== "undefined" && chrome.runtime) {
      chrome.runtime.onMessage.addListener(handleChromeMessage);
    }
    return () => {
      if (typeof window !== "undefined") {
        window.removeEventListener("workspaceSwitchComplete", handleWorkspaceSwitchComplete);
        window.removeEventListener("userTabsVisibilityChanged", handleUserTabsVisibilityChanged);
      }
      if (typeof chrome !== "undefined" && chrome.runtime) {
        chrome.runtime.onMessage.removeListener(handleChromeMessage);
      }
    };
  }
}

class WorkspaceSessionManager {
  static currentSession = null;
  static isSessionSwitching = false;
  /**
   * 获取当前会话
   */
  static getCurrentSession() {
    return this.currentSession;
  }
  /**
   * 切换工作区会话
   * 复用现有的 WorkspaceStateSync 事件系统
   */
  static async switchSession(workspaceId, options = {}) {
    try {
      if (this.isSessionSwitching) {
        console.log("⏳ 会话切换正在进行中，跳过重复请求");
        return { success: true };
      }
      this.isSessionSwitching = true;
      console.log(`🔄 开始切换到工作区会话: ${workspaceId}`);
      if (this.currentSession && options.preserveCurrentSession !== false) {
        const saveResult = await this.saveSession(this.currentSession);
        if (!saveResult.success) {
          console.warn("保存当前会话失败:", saveResult.error);
        }
      }
      const loadResult = await this.loadSession(workspaceId);
      if (!loadResult.success) {
        this.isSessionSwitching = false;
        return { success: false, error: loadResult.error };
      }
      this.currentSession = loadResult.data;
      WorkspaceStateSync.sendWorkspaceStateUpdate(workspaceId, "switch");
      if (options.restoreTabOrder !== false && this.currentSession.tabOrder.length > 0) {
        await this.restoreTabOrder(this.currentSession);
      }
      if (!this.currentSession.activeTabId && this.currentSession.tabOrder.length > 0) {
        console.log("📋 没有保存的活跃标签页，智能选择合适的标签页激活");
        let targetTabId = null;
        for (const workonaId of this.currentSession.tabOrder) {
          const metadataResult = await WorkonaTabManager.getTabMetadata(workonaId);
          if (metadataResult.success && metadataResult.data?.isWorkspaceCore) {
            targetTabId = workonaId;
            console.log(`🎯 选择工作区核心标签页: ${workonaId}`);
            break;
          }
        }
        if (!targetTabId && this.currentSession.tabOrder.length > 0) {
          targetTabId = this.currentSession.tabOrder[0];
          console.log(`🎯 选择第一个标签页: ${targetTabId}`);
        }
        if (targetTabId) {
          const chromeIdResult = await WorkonaTabManager.getChromeIdByWorkonaId(targetTabId);
          if (chromeIdResult.success && chromeIdResult.data) {
            try {
              await chrome.tabs.update(chromeIdResult.data, { active: true });
              console.log(`✨ 智能激活标签页: ${targetTabId}`);
              this.currentSession.activeTabId = targetTabId;
              await this.saveSession(this.currentSession);
            } catch (error) {
              console.warn("智能激活标签页失败:", error);
            }
          }
        }
      }
      console.log(`✅ 成功切换到工作区会话: ${workspaceId}`);
      return { success: true };
    } catch (error) {
      return {
        success: false,
        error: {
          code: ERROR_CODES.TAB_ERROR,
          message: "Failed to switch workspace session",
          details: error
        }
      };
    } finally {
      this.isSessionSwitching = false;
    }
  }
  /**
   * 获取当前工作区的标签页
   * 确保会话只包含当前工作区的标签页
   */
  static getCurrentWorkspaceTabs() {
    if (!this.currentSession) {
      return [];
    }
    return Object.values(this.currentSession.tabs);
  }
  /**
   * 获取当前工作区的标签页顺序
   */
  static getCurrentTabOrder() {
    if (!this.currentSession) {
      return [];
    }
    return [...this.currentSession.tabOrder];
  }
  /**
   * 更新会话中的标签页
   */
  static async updateSessionTab(workonaId, tab) {
    try {
      if (!this.currentSession) {
        return {
          success: false,
          error: {
            code: ERROR_CODES.TAB_ERROR,
            message: "No active session"
          }
        };
      }
      const existingTab = this.currentSession.tabs[workonaId];
      this.currentSession.tabs[workonaId] = {
        ...existingTab,
        // 保持现有属性
        ...tab
        // 应用更新
      };
      this.currentSession.lastActiveAt = Date.now();
      if (!this.currentSession.tabOrder.includes(workonaId)) {
        this.currentSession.tabOrder.push(workonaId);
        console.log(`📝 添加新标签页到会话顺序: ${workonaId}`);
      } else {
        console.log(`🔄 更新现有标签页会话信息: ${workonaId}`);
      }
      const saveResult = await this.saveSession(this.currentSession);
      if (!saveResult.success) {
        return { success: false, error: saveResult.error };
      }
      return { success: true };
    } catch (error) {
      return {
        success: false,
        error: {
          code: ERROR_CODES.TAB_ERROR,
          message: "Failed to update session tab",
          details: error
        }
      };
    }
  }
  /**
   * 从会话中移除标签页
   */
  static async removeSessionTab(workonaId) {
    try {
      if (!this.currentSession) {
        return { success: true };
      }
      delete this.currentSession.tabs[workonaId];
      this.currentSession.tabOrder = this.currentSession.tabOrder.filter((id) => id !== workonaId);
      if (this.currentSession.activeTabId === workonaId) {
        this.currentSession.activeTabId = void 0;
      }
      this.currentSession.lastActiveAt = Date.now();
      const saveResult = await this.saveSession(this.currentSession);
      if (!saveResult.success) {
        return { success: false, error: saveResult.error };
      }
      console.log(`🗑️ 从会话中移除标签页: ${workonaId}`);
      return { success: true };
    } catch (error) {
      return {
        success: false,
        error: {
          code: ERROR_CODES.TAB_ERROR,
          message: "Failed to remove session tab",
          details: error
        }
      };
    }
  }
  /**
   * 设置当前激活的标签页
   */
  static async setActiveTab(workonaId) {
    try {
      if (!this.currentSession) {
        return {
          success: false,
          error: {
            code: ERROR_CODES.TAB_ERROR,
            message: "No active session"
          }
        };
      }
      this.currentSession.activeTabId = workonaId;
      this.currentSession.lastActiveAt = Date.now();
      if (this.currentSession.tabs[workonaId]) {
        this.currentSession.tabs[workonaId].lastActiveAt = Date.now();
      }
      const saveResult = await this.saveSession(this.currentSession);
      if (!saveResult.success) {
        return { success: false, error: saveResult.error };
      }
      console.log(`📍 记录活跃标签页: ${workonaId} (工作区: ${this.currentSession.workspaceId})`);
      return { success: true };
    } catch (error) {
      return {
        success: false,
        error: {
          code: ERROR_CODES.TAB_ERROR,
          message: "Failed to set active tab",
          details: error
        }
      };
    }
  }
  /**
   * 实时同步当前工作区的标签页状态
   * 记录标签页顺序和活跃状态
   */
  static async syncCurrentWorkspaceState() {
    try {
      if (!this.currentSession) {
        return { success: true };
      }
      console.log(`🔄 同步工作区标签页状态: ${this.currentSession.workspaceId}`);
      const currentWindow = await chrome.windows.getCurrent();
      const allTabs = await chrome.tabs.query({ windowId: currentWindow.id });
      const workonaTabIds = await WorkonaTabManager.getWorkspaceWorkonaTabIds(this.currentSession.workspaceId);
      if (!workonaTabIds.success) {
        return { success: false, error: workonaTabIds.error };
      }
      const currentWorkspaceTabs = [];
      let activeWorkonaId;
      for (const workonaId of workonaTabIds.data) {
        const chromeIdResult = await WorkonaTabManager.getChromeIdByWorkonaId(workonaId);
        if (chromeIdResult.success && chromeIdResult.data) {
          const chromeTab = allTabs.find((tab) => tab.id === chromeIdResult.data);
          if (chromeTab) {
            currentWorkspaceTabs.push({
              workonaId,
              chromeTab,
              index: chromeTab.index
            });
            if (chromeTab.active) {
              activeWorkonaId = workonaId;
            }
          }
        }
      }
      currentWorkspaceTabs.sort((a, b) => a.index - b.index);
      const newTabOrder = currentWorkspaceTabs.map((item) => item.workonaId);
      let hasChanges = false;
      if (JSON.stringify(this.currentSession.tabOrder) !== JSON.stringify(newTabOrder)) {
        this.currentSession.tabOrder = newTabOrder;
        hasChanges = true;
        console.log(`📋 更新标签页顺序: [${newTabOrder.join(", ")}]`);
      }
      if (activeWorkonaId && this.currentSession.activeTabId !== activeWorkonaId) {
        this.currentSession.activeTabId = activeWorkonaId;
        hasChanges = true;
        const metadataResult = await WorkonaTabManager.getTabMetadata(activeWorkonaId);
        const isWorkspaceCore = metadataResult.success && metadataResult.data?.isWorkspaceCore;
        console.log(`📍 更新活跃标签页: ${activeWorkonaId} ${isWorkspaceCore ? "(工作区专用)" : "(用户标签页)"}`);
      }
      if (hasChanges) {
        this.currentSession.lastActiveAt = Date.now();
        const saveResult = await this.saveSession(this.currentSession);
        if (!saveResult.success) {
          return { success: false, error: saveResult.error };
        }
      }
      return { success: true };
    } catch (error) {
      return {
        success: false,
        error: {
          code: ERROR_CODES.TAB_ERROR,
          message: "Failed to sync workspace state",
          details: error
        }
      };
    }
  }
  /**
   * 更新标签页顺序
   */
  static async updateTabOrder(newOrder) {
    try {
      if (!this.currentSession) {
        return {
          success: false,
          error: {
            code: ERROR_CODES.TAB_ERROR,
            message: "No active session"
          }
        };
      }
      const validOrder = newOrder.filter((workonaId) => this.currentSession.tabs[workonaId]);
      this.currentSession.tabOrder = validOrder;
      this.currentSession.lastActiveAt = Date.now();
      const saveResult = await this.saveSession(this.currentSession);
      if (!saveResult.success) {
        return { success: false, error: saveResult.error };
      }
      console.log(`📋 更新标签页顺序: ${validOrder.length} 个标签页`);
      return { success: true };
    } catch (error) {
      return {
        success: false,
        error: {
          code: ERROR_CODES.TAB_ERROR,
          message: "Failed to update tab order",
          details: error
        }
      };
    }
  }
  /**
   * 清除当前会话状态
   * 用于浏览器重启后重置工作区状态
   */
  static async clearCurrentSession() {
    try {
      console.log("🔄 清除当前工作区会话状态");
      this.currentSession = null;
      this.isSessionSwitching = false;
      console.log("✅ 工作区会话状态已清除");
      return { success: true };
    } catch (error) {
      return {
        success: false,
        error: {
          code: ERROR_CODES.TAB_ERROR,
          message: "Failed to clear current session",
          details: error
        }
      };
    }
  }
  /**
   * 将新创建的标签页添加到当前会话
   * 用于后台标签页创建完成后的增量更新
   */
  static async addNewTabsToCurrentSession(newWorkonaIds) {
    try {
      if (!this.currentSession) {
        console.log("⚠️ 没有活跃会话，跳过添加新标签页");
        return { success: true };
      }
      if (newWorkonaIds.length === 0) {
        return { success: true };
      }
      console.log(`📝 添加 ${newWorkonaIds.length} 个新标签页到当前会话`);
      let addedCount = 0;
      for (const workonaId of newWorkonaIds) {
        if (!this.currentSession.tabOrder.includes(workonaId)) {
          this.currentSession.tabOrder.push(workonaId);
          addedCount++;
        }
      }
      if (addedCount > 0) {
        this.currentSession.lastActiveAt = Date.now();
        const saveResult = await this.saveSession(this.currentSession);
        if (!saveResult.success) {
          return { success: false, error: saveResult.error };
        }
        console.log(`✅ 成功添加 ${addedCount} 个新标签页到会话顺序`);
      } else {
        console.log("ℹ️ 所有新标签页都已存在于会话中");
      }
      return { success: true };
    } catch (error) {
      return {
        success: false,
        error: {
          code: ERROR_CODES.TAB_ERROR,
          message: "Failed to add new tabs to current session",
          details: error
        }
      };
    }
  }
  /**
   * 保存会话到存储
   */
  static async saveSession(session) {
    try {
      const sessionsResult = await StorageManager.getWorkspaceSessions();
      if (!sessionsResult.success) {
        return { success: false, error: sessionsResult.error };
      }
      const sessions = sessionsResult.data;
      sessions[session.workspaceId] = session;
      const saveResult = await StorageManager.saveWorkspaceSessions(sessions);
      if (!saveResult.success) {
        return { success: false, error: saveResult.error };
      }
      return { success: true };
    } catch (error) {
      return {
        success: false,
        error: {
          code: ERROR_CODES.STORAGE_ERROR,
          message: "Failed to save session",
          details: error
        }
      };
    }
  }
  /**
   * 从存储加载会话
   */
  static async loadSession(workspaceId) {
    try {
      const sessionsResult = await StorageManager.getWorkspaceSessions();
      if (!sessionsResult.success) {
        return { success: false, error: sessionsResult.error };
      }
      const sessions = sessionsResult.data;
      let session = sessions[workspaceId];
      if (!session) {
        session = {
          workspaceId,
          tabs: {},
          tabOrder: [],
          lastActiveAt: Date.now()
        };
        sessions[workspaceId] = session;
        const saveResult = await StorageManager.saveWorkspaceSessions(sessions);
        if (!saveResult.success) {
          return { success: false, error: saveResult.error };
        }
        console.log(`✨ 创建新的工作区会话: ${workspaceId}`);
      }
      return { success: true, data: session };
    } catch (error) {
      return {
        success: false,
        error: {
          code: ERROR_CODES.STORAGE_ERROR,
          message: "Failed to load session",
          details: error
        }
      };
    }
  }
  /**
   * 恢复标签页顺序和活跃状态
   */
  static async restoreTabOrder(session) {
    try {
      console.log(`🔄 恢复工作区状态: ${session.tabOrder.length} 个标签页`);
      if (session.tabOrder.length === 0) {
        console.log("📋 没有保存的标签页顺序，跳过恢复");
        return;
      }
      const currentWindow = await chrome.windows.getCurrent();
      const allTabs = await chrome.tabs.query({ windowId: currentWindow.id });
      const tabsToReorder = [];
      for (let i = 0; i < session.tabOrder.length; i++) {
        const workonaId = session.tabOrder[i];
        const chromeIdResult = await WorkonaTabManager.getChromeIdByWorkonaId(workonaId);
        if (chromeIdResult.success && chromeIdResult.data) {
          const chromeTab = allTabs.find((tab) => tab.id === chromeIdResult.data);
          if (chromeTab) {
            tabsToReorder.push({
              workonaId,
              chromeId: chromeTab.id,
              targetIndex: i
            });
          }
        }
      }
      let targetActiveTabId = null;
      if (session.activeTabId) {
        const activeTabResult = await WorkonaTabManager.getChromeIdByWorkonaId(session.activeTabId);
        if (activeTabResult.success && activeTabResult.data) {
          const targetTab = allTabs.find((tab) => tab.id === activeTabResult.data);
          if (targetTab) {
            targetActiveTabId = activeTabResult.data;
            console.log(`🎯 确定目标活跃标签页: ${session.activeTabId} (Chrome ID: ${targetActiveTabId})`);
          }
        }
      }
      console.log(`📋 重新排列 ${tabsToReorder.length} 个标签页`);
      const movePromises = tabsToReorder.map(async (tabInfo) => {
        try {
          await chrome.tabs.move(tabInfo.chromeId, { index: tabInfo.targetIndex });
          console.log(`📍 移动标签页 ${tabInfo.workonaId} 到位置 ${tabInfo.targetIndex}`);
        } catch (error) {
          console.warn(`⚠️ 移动标签页 ${tabInfo.workonaId} 失败:`, error);
        }
      });
      await Promise.all(movePromises);
      console.log("✅ 所有标签页移动操作完成");
      if (targetActiveTabId) {
        try {
          const metadataResult = await WorkonaTabManager.getTabMetadata(session.activeTabId);
          const isWorkspaceCore = metadataResult.success && metadataResult.data?.isWorkspaceCore;
          await chrome.tabs.update(targetActiveTabId, { active: true });
          console.log(`✨ 立即激活目标标签页: ${session.activeTabId} ${isWorkspaceCore ? "(工作区专用)" : "(用户标签页)"}`);
          const currentWindow2 = await chrome.windows.getCurrent();
          if (currentWindow2.id) {
            chrome.windows.update(currentWindow2.id, { focused: true });
          }
          console.log(`🎯 工作区标签页状态恢复完成，无闪烁切换`);
        } catch (error) {
          console.warn(`⚠️ 激活目标标签页失败:`, error);
        }
      } else {
        console.log("📋 没有保存的活跃标签页，智能选择合适的标签页");
        if (session.tabOrder.length > 0) {
          let bestTabId = null;
          for (const workonaId of session.tabOrder) {
            const chromeIdResult = await WorkonaTabManager.getChromeIdByWorkonaId(workonaId);
            if (chromeIdResult.success && chromeIdResult.data) {
              const metadataResult = await WorkonaTabManager.getTabMetadata(workonaId);
              if (metadataResult.success && metadataResult.data?.isWorkspaceCore) {
                bestTabId = workonaId;
                break;
              }
              if (!bestTabId) {
                bestTabId = workonaId;
              }
            }
          }
          if (bestTabId) {
            const chromeIdResult = await WorkonaTabManager.getChromeIdByWorkonaId(bestTabId);
            if (chromeIdResult.success && chromeIdResult.data) {
              try {
                await chrome.tabs.update(chromeIdResult.data, { active: true });
                console.log(`✨ 智能激活标签页: ${bestTabId}`);
              } catch (error) {
                console.warn("智能激活标签页失败:", error);
              }
            }
          }
        }
      }
      console.log("✅ 工作区状态恢复完成");
    } catch (error) {
      console.warn("恢复工作区状态失败:", error);
    }
  }
  /**
   * 清理工作区会话
   */
  static async clearWorkspaceSession(workspaceId) {
    try {
      const sessionsResult = await StorageManager.getWorkspaceSessions();
      if (!sessionsResult.success) {
        return { success: false, error: sessionsResult.error };
      }
      const sessions = sessionsResult.data;
      delete sessions[workspaceId];
      const saveResult = await StorageManager.saveWorkspaceSessions(sessions);
      if (!saveResult.success) {
        return { success: false, error: saveResult.error };
      }
      if (this.currentSession && this.currentSession.workspaceId === workspaceId) {
        this.currentSession = null;
      }
      console.log(`🗑️ 清理工作区会话: ${workspaceId}`);
      return { success: true };
    } catch (error) {
      return {
        success: false,
        error: {
          code: ERROR_CODES.STORAGE_ERROR,
          message: "Failed to clear workspace session",
          details: error
        }
      };
    }
  }
}

const workspaceSessionManager = /*#__PURE__*/Object.freeze(/*#__PURE__*/Object.defineProperty({
  __proto__: null,
  WorkspaceSessionManager
}, Symbol.toStringTag, { value: 'Module' }));

class TabClassificationUtils {
  /**
   * 判断是否为系统标签页
   */
  static isSystemTab(url) {
    return url.includes("chrome://") || url.includes("chrome-extension://") || url.includes("about:") || url.includes("edge://") || url.includes("workspace-placeholder.html") || url === "chrome://newtab/" || url === "about:blank" || url === "";
  }
  /**
   * 检查是否为工作区专属标签页（具有Workona ID映射）
   */
  static async isWorkspaceSpecificTab(tabId) {
    try {
      const workonaIdResult = await WorkonaTabManager.getWorkonaIdByChromeId(tabId);
      return workonaIdResult.success && !!workonaIdResult.data;
    } catch {
      return false;
    }
  }
  /**
   * 检查是否为用户标签页（普通网站标签页，不具有Workona ID映射）
   */
  static async isUserTab(tab) {
    if (!tab.url || this.isSystemTab(tab.url)) {
      return false;
    }
    const isWorkspaceSpecific = await this.isWorkspaceSpecificTab(tab.id);
    return !isWorkspaceSpecific;
  }
  /**
   * 对标签页进行3分类统计
   */
  static async classifyTabs(tabs) {
    const systemTabs = [];
    const workspaceSpecificTabs = [];
    const userTabs = [];
    for (const tab of tabs) {
      if (!tab.url || !tab.id) continue;
      if (this.isSystemTab(tab.url)) {
        systemTabs.push(tab);
      } else {
        const isWorkspaceSpecific = await this.isWorkspaceSpecificTab(tab.id);
        if (isWorkspaceSpecific) {
          workspaceSpecificTabs.push(tab);
        } else {
          userTabs.push(tab);
        }
      }
    }
    return { systemTabs, workspaceSpecificTabs, userTabs };
  }
}
class WorkspaceSwitcher {
  static isBackgroundSetupInProgress = false;
  // 后台设置进行中标志
  static currentSetupWorkspaceId = null;
  // 当前正在设置的工作区ID
  /**
   * 检查是否有后台设置正在进行
   */
  static isSetupInProgress() {
    return this.isBackgroundSetupInProgress;
  }
  /**
   * 获取当前正在设置的工作区ID
   */
  static getCurrentSetupWorkspaceId() {
    return this.currentSetupWorkspaceId;
  }
  /**
   * 切换到指定工作区（专用窗口架构）
   */
  static async switchToWorkspace(workspaceId, options = {}) {
    try {
      if (this.isBackgroundSetupInProgress) {
        const currentSetupId = this.currentSetupWorkspaceId;
        console.warn(`⚠️ 后台工作区设置正在进行中 (${currentSetupId})，拒绝切换到 ${workspaceId}`);
        return {
          success: false,
          error: {
            code: ERROR_CODES.TAB_ERROR,
            message: "Background workspace setup in progress",
            details: `Cannot switch to ${workspaceId} while ${currentSetupId} is being set up`
          }
        };
      }
      console.log(`开始切换到工作区: ${workspaceId}`);
      const workspaceResult = await StorageManager.getWorkspace(workspaceId);
      if (!workspaceResult.success) {
        return { success: false, error: workspaceResult.error };
      }
      const workspace = workspaceResult.data;
      const currentWorkspaceResult = await this.getCurrentWorkspace();
      const currentWorkspace = currentWorkspaceResult.success ? currentWorkspaceResult.data : null;
      const settingsResult = await StorageManager.getSettings();
      if (!settingsResult.success) {
        return { success: false, error: settingsResult.error };
      }
      const settings = settingsResult.data;
      const switchOptions = {
        closeOtherTabs: options.closeOtherTabs ?? settings.autoCloseOtherTabs,
        preserveUserOpenedTabs: options.preserveUserOpenedTabs ?? settings.preserveUserOpenedTabs,
        focusFirstTab: options.focusFirstTab ?? false,
        // 默认不自动聚焦到第一个标签页
        autoOpenWebsites: options.autoOpenWebsites ?? false
        // 默认不自动打开工作区网站，优化性能
      };
      await this.ensureSystemTabProtection();
      await this.ensureWorkspaceSwitchProtection(workspaceId);
      if (currentWorkspace) {
        console.log(`💾 保存当前工作区状态: ${currentWorkspace.name}`);
        await WorkspaceSessionManager.syncCurrentWorkspaceState();
      }
      await this.reclassifyTemporaryTabs(workspaceId);
      if (currentWorkspace && currentWorkspace.id !== workspaceId) {
        await this.moveCurrentTabsToWorkspaceWindow(currentWorkspace);
      } else if (!currentWorkspace) {
        await this.moveNonTargetWorkspaceTabsToWindow(workspaceId);
      }
      console.log(`🎯 立即设置工作区激活状态，提升用户体验`);
      await StorageManager.setActiveWorkspaceId(workspaceId);
      const workspacesResult = await StorageManager.getWorkspaces();
      if (workspacesResult.success) {
        const workspaces = workspacesResult.data;
        workspaces.forEach((w) => {
          w.isActive = w.id === workspaceId;
        });
        await StorageManager.saveWorkspaces(workspaces);
      }
      await this.notifyWorkspaceSwitchComplete(workspaceId);
      await this.moveTabsFromWorkspaceWindow(workspace);
      console.log(`🔄 开始基础工作区会话恢复: ${workspaceId}`);
      const sessionSwitchResult = await WorkspaceSessionManager.switchSession(workspaceId, {
        preserveCurrentSession: true,
        restoreTabOrder: true,
        // 启用现有标签页顺序恢复
        activateFirstTab: false,
        // 不自动激活第一个标签页，而是恢复上次的活跃标签页
        closeOtherWorkspaceTabs: switchOptions.closeOtherTabs
      });
      if (!sessionSwitchResult.success) {
        console.warn("基础工作区会话恢复失败，但工作区切换成功:", sessionSwitchResult.error);
      } else {
        console.log("✅ 基础工作区会话恢复成功");
      }
      console.log(`🔄 开始异步后台工作区完整设置`);
      this.isBackgroundSetupInProgress = true;
      this.currentSetupWorkspaceId = workspaceId;
      console.log(`🔒 设置工作区后台设置标志: ${workspaceId}, 状态: ${this.isBackgroundSetupInProgress}`);
      await this.notifyWorkspaceSetupStatusChange(workspaceId, true);
      this.performBackgroundWorkspaceSetup(workspace, workspaceId, switchOptions).catch((error) => {
        console.error("❌ 后台工作区设置失败:", error);
      });
      console.log(`✅ 成功切换到工作区: ${workspace.name} (UI已更新，后台设置进行中)`);
      return { success: true };
    } catch (error) {
      console.error(`切换工作区失败:`, error);
      return {
        success: false,
        error: {
          code: ERROR_CODES.TAB_ERROR,
          message: "Failed to switch workspace",
          details: error
        }
      };
    }
  }
  /**
   * 🔄 异步执行后台工作区完整设置
   * 包括标签页创建、用户标签页处理、归属机制等耗时操作
   */
  static async performBackgroundWorkspaceSetup(workspace, workspaceId, switchOptions) {
    try {
      console.log(`🔄 开始后台工作区完整设置: ${workspace.name}`);
      if (switchOptions.autoOpenWebsites !== false) {
        console.log(`📝 创建缺失的工作区网站标签页`);
        const openWebsitesResult = await this.openWorkspaceWebsites(workspace);
        if (!openWebsitesResult.success) {
          console.warn("打开工作区网站失败:", openWebsitesResult.error);
        }
      } else {
        console.log(`⏭️ 跳过自动打开工作区网站（性能优化）`);
      }
      console.log(`👤 处理用户标签页隐藏状态`);
      const handleUserTabsResult = await this.handleUserTabsVisibilityState(workspace);
      if (!handleUserTabsResult.success) {
        console.warn("处理用户标签页状态失败:", handleUserTabsResult.error);
      }
      console.log(`🎯 处理等待归属的标签页`);
      await this.handlePendingTabAssignment(workspaceId);
      console.log(`🔄 重新同步工作区会话状态`);
      const syncResult = await WorkspaceSessionManager.syncCurrentWorkspaceState();
      if (!syncResult.success) {
        console.warn("同步工作区会话状态失败:", syncResult.error);
      }
      console.log(`✅ 后台工作区完整设置完成: ${workspace.name}`);
    } catch (error) {
      console.error(`❌ 后台工作区设置失败:`, error);
      throw error;
    } finally {
      this.isBackgroundSetupInProgress = false;
      this.currentSetupWorkspaceId = null;
      console.log(`🔓 清除工作区后台设置标志: ${workspaceId}, 状态: ${this.isBackgroundSetupInProgress}`);
      try {
        await this.notifyWorkspaceSetupStatusChange(workspaceId, false);
      } catch (notifyError) {
        console.warn("通知工作区设置状态变更失败:", notifyError);
      }
      console.log(`🏁 工作区 ${workspace.name} 后台设置流程结束`);
    }
  }
  /**
   * 系统标签页保护机制：确保窗口始终保留至少一个系统标签页
   * 在工作区切换前检查并创建必要的系统标签页，防止窗口意外关闭
   */
  static async ensureSystemTabProtection() {
    try {
      console.log("🛡️ [系统保护] 开始检查系统标签页保护机制...");
      const currentWindow = await chrome.windows.getCurrent();
      const allTabs = await chrome.tabs.query({ windowId: currentWindow.id });
      console.log(`🔍 [系统保护] 当前窗口共有 ${allTabs.length} 个标签页`);
      const systemTabs = allTabs.filter(
        (tab) => tab.url && TabClassificationUtils.isSystemTab(tab.url)
      );
      console.log(`📊 [系统保护] 发现 ${systemTabs.length} 个系统标签页`);
      if (systemTabs.length > 0) {
        console.log("✅ [系统保护] 窗口已有系统标签页，无需创建保护标签页");
        systemTabs.forEach((tab) => {
          console.log(`   - ${tab.title} (${tab.url})`);
        });
        return;
      }
      console.log("🆕 [系统保护] 窗口缺少系统标签页，创建保护标签页...");
      const protectionTab = await chrome.tabs.create({
        windowId: currentWindow.id,
        url: "chrome://newtab/",
        active: false,
        // 不获得焦点
        index: 0
        // 放在第一个位置
      });
      if (protectionTab.id) {
        console.log(`✅ [系统保护] 成功创建保护标签页: ${protectionTab.id} (chrome://newtab/)`);
      } else {
        console.warn("⚠️ [系统保护] 保护标签页创建成功但未获得ID");
      }
    } catch (error) {
      console.error("❌ [系统保护] 系统标签页保护机制执行失败:", error);
      try {
        console.log("🆘 [系统保护] 尝试创建紧急保护标签页...");
        const emergencyTab = await chrome.tabs.create({
          url: "chrome://newtab/",
          active: false
        });
        if (emergencyTab.id) {
          console.log(`✅ [系统保护] 紧急保护标签页创建成功: ${emergencyTab.id}`);
        }
      } catch (emergencyError) {
        console.error("❌ [系统保护] 紧急保护标签页创建失败:", emergencyError);
      }
    }
  }
  /**
   * 增强工作区切换保护：确保切换后窗口不会为空
   */
  static async ensureWorkspaceSwitchProtection(targetWorkspaceId) {
    try {
      console.log(`🛡️ [切换保护] 检查工作区切换保护机制: ${targetWorkspaceId}`);
      const currentWindow = await chrome.windows.getCurrent();
      const allTabs = await chrome.tabs.query({ windowId: currentWindow.id });
      console.log(`🔍 [切换保护] 当前窗口共有 ${allTabs.length} 个标签页`);
      const hasNewTab = allTabs.some(
        (tab) => tab.url === "chrome://newtab/" || tab.url === "chrome://new-tab-page/" || tab.pendingUrl === "chrome://newtab/"
      );
      console.log(`🔍 [切换保护] 当前窗口是否有新标签页: ${hasNewTab}`);
      if (!hasNewTab) {
        console.log(`🆘 [切换保护] 缺少新标签页，立即创建窗口保护标签页...`);
        const protectionTab = await chrome.tabs.create({
          windowId: currentWindow.id,
          url: "chrome://newtab/",
          active: false,
          index: 0
        });
        if (protectionTab.id) {
          console.log(`✅ [切换保护] 成功创建窗口保护标签页: ${protectionTab.id}`);
          console.log(`⏳ [切换保护] 等待新标签页完全加载...`);
          let retryCount = 0;
          const maxRetries = 10;
          const retryDelay = 100;
          while (retryCount < maxRetries) {
            try {
              const verifyTab = await chrome.tabs.get(protectionTab.id);
              if (verifyTab && (verifyTab.status === "complete" || verifyTab.url === "chrome://newtab/")) {
                console.log(`✅ [切换保护] 新标签页已完全创建并加载完毕: ${verifyTab.status}`);
                break;
              }
              console.log(`⏳ [切换保护] 新标签页仍在加载中，等待重试... (${retryCount + 1}/${maxRetries})`);
              await new Promise((resolve) => setTimeout(resolve, retryDelay));
              retryCount++;
            } catch (error) {
              console.warn(`⚠️ [切换保护] 检查新标签页状态失败，重试... (${retryCount + 1}/${maxRetries})`, error);
              await new Promise((resolve) => setTimeout(resolve, retryDelay));
              retryCount++;
            }
          }
          if (retryCount >= maxRetries) {
            console.warn(`⚠️ [切换保护] 新标签页创建验证超时，但继续执行切换流程`);
          }
          allTabs.unshift(protectionTab);
        } else {
          console.error(`❌ [切换保护] 创建窗口保护标签页失败`);
          throw new Error("无法创建窗口保护标签页");
        }
      } else {
        console.log(`✅ [切换保护] 当前窗口已有新标签页，窗口安全`);
      }
      const targetWorkspaceResult = await StorageManager.getWorkspace(targetWorkspaceId);
      if (!targetWorkspaceResult.success || !targetWorkspaceResult.data) {
        console.warn(`⚠️ [切换保护] 无法获取目标工作区信息: ${targetWorkspaceId}`);
        return;
      }
      const targetWorkspace = targetWorkspaceResult.data;
      const hasWorkspaceWebsites = targetWorkspace.websites && targetWorkspace.websites.length > 0;
      console.log(`📊 [切换保护] 目标工作区 "${targetWorkspace.name}" 有 ${targetWorkspace.websites?.length || 0} 个网站`);
      const globalWindowId = WindowManager.getGlobalWorkspaceWindowId();
      let hasWorkspaceTabs = false;
      if (globalWindowId) {
        try {
          const windowTabs = await chrome.tabs.query({ windowId: globalWindowId });
          for (const tab of windowTabs) {
            if (tab.id) {
              const workonaIdResult = await WorkonaTabManager.getWorkonaIdByChromeId(tab.id);
              if (workonaIdResult.success && workonaIdResult.data) {
                const workspaceId = workonaIdResult.data.split("-")[1];
                if (workspaceId === targetWorkspaceId) {
                  hasWorkspaceTabs = true;
                  break;
                }
              }
            }
          }
          console.log(`📊 [切换保护] 目标工作区在专用窗口中有标签页: ${hasWorkspaceTabs}`);
        } catch (error) {
          console.warn(`⚠️ [切换保护] 检查专用窗口标签页失败:`, error);
        }
      } else {
        console.log(`📊 [切换保护] 没有全局专用窗口`);
      }
      if (!hasWorkspaceWebsites && !hasWorkspaceTabs && allTabs.length <= 2) {
        console.log(`🆕 [切换保护] 目标工作区缺少内容且当前窗口标签页较少，创建额外保护标签页...`);
        const additionalProtectionTab = await chrome.tabs.create({
          windowId: currentWindow.id,
          url: "chrome://newtab/",
          active: false,
          index: 1
          // 放在第二个位置，避免与第一个保护标签页冲突
        });
        if (additionalProtectionTab.id) {
          console.log(`✅ [切换保护] 成功创建额外保护标签页: ${additionalProtectionTab.id}`);
        }
      } else {
        console.log(`✅ [切换保护] 目标工作区有足够内容或当前窗口安全，无需额外保护标签页`);
      }
    } catch (error) {
      console.error("❌ [切换保护] 工作区切换保护机制执行失败:", error);
      try {
        console.log("🆘 [切换保护] 创建紧急保护标签页...");
        const emergencyTab = await chrome.tabs.create({
          url: "chrome://newtab/",
          active: false
        });
        if (emergencyTab.id) {
          console.log(`✅ [切换保护] 紧急保护标签页创建成功: ${emergencyTab.id}`);
        }
      } catch (emergencyError) {
        console.error("❌ [切换保护] 紧急保护标签页创建失败:", emergencyError);
      }
    }
  }
  /**
   * 智能工作区归属机制：处理等待归属的标签页
   * 当用户切换到工作区时，自动将等待归属的标签页分配给该工作区
   */
  static async handlePendingTabAssignment(workspaceId) {
    try {
      console.log(`🎯 [智能归属] 开始处理等待归属的标签页，目标工作区: ${workspaceId}`);
      const mappingsResult = await StorageManager.getTabIdMappings();
      if (!mappingsResult.success || !mappingsResult.data) {
        console.log(`ℹ️ [智能归属] 无标签页映射数据，跳过处理`);
        return;
      }
      const mappings = mappingsResult.data;
      const pendingMappings = mappings.filter(
        (mapping) => mapping.workspaceId === "pending-assignment"
      );
      if (pendingMappings.length === 0) {
        console.log(`ℹ️ [智能归属] 没有等待归属的标签页`);
        return;
      }
      console.log(`🔍 [智能归属] 发现 ${pendingMappings.length} 个等待归属的标签页`);
      let assignedCount = 0;
      for (const mapping of pendingMappings) {
        try {
          const tab = await chrome.tabs.get(mapping.chromeId);
          if (!tab) {
            console.log(`⚠️ [智能归属] 标签页 ${mapping.chromeId} 不存在，跳过`);
            continue;
          }
          const newWorkonaId = WorkonaTabManager.generateWorkonaTabId(workspaceId);
          await WorkonaTabManager.removeTabMapping(mapping.workonaId);
          const newMappingResult = await WorkonaTabManager.createTabIdMapping(
            newWorkonaId,
            mapping.chromeId,
            workspaceId,
            void 0,
            // 暂时没有关联的网站ID
            {
              isWorkspaceCore: false,
              // 标记为会话临时标签页
              source: "user_opened"
            }
          );
          if (newMappingResult.success) {
            assignedCount++;
            console.log(`✅ [智能归属] 成功将标签页 ${tab.title} 归属到工作区 ${workspaceId}`);
          } else {
            console.error(`❌ [智能归属] 归属标签页失败:`, newMappingResult.error);
          }
        } catch (error) {
          console.warn(`⚠️ [智能归属] 处理标签页 ${mapping.chromeId} 时出错:`, error);
        }
      }
      console.log(`✅ [智能归属] 完成处理，成功归属 ${assignedCount} 个标签页到工作区 ${workspaceId}`);
    } catch (error) {
      console.error(`❌ [智能归属] 处理等待归属标签页时出错:`, error);
    }
  }
  /**
   * 移动非目标工作区的标签页到专用窗口
   */
  static async moveNonTargetWorkspaceTabsToWindow(targetWorkspaceId) {
    try {
      console.log(`检查并移动非目标工作区的标签页到专用窗口，目标工作区: ${targetWorkspaceId}`);
      const workspacesResult = await StorageManager.getWorkspaces();
      if (!workspacesResult.success) {
        console.log("获取工作区列表失败:", workspacesResult.error);
        return { success: true };
      }
      const workspaces = workspacesResult.data;
      const currentWindow = await chrome.windows.getCurrent();
      const currentTabs = await chrome.tabs.query({ windowId: currentWindow.id });
      console.log(`当前窗口共有 ${currentTabs.length} 个标签页`);
      for (const workspace of workspaces) {
        if (workspace.id === targetWorkspaceId) {
          continue;
        }
        const relatedTabs = [];
        for (const tab of currentTabs) {
          if (!tab.url || !tab.id) continue;
          const workonaIdResult = await WorkonaTabManager.getWorkonaIdByChromeId(tab.id);
          if (workonaIdResult.success && workonaIdResult.data) {
            const metadataResult = await WorkonaTabManager.getTabMetadata(workonaIdResult.data);
            if (metadataResult.success && metadataResult.data?.workspaceId === workspace.id) {
              relatedTabs.push({
                id: tab.id,
                url: tab.url,
                title: tab.title || "",
                favicon: tab.favIconUrl || "",
                isPinned: tab.pinned,
                isActive: tab.active,
                windowId: tab.windowId,
                index: tab.index
              });
            }
          }
        }
        if (relatedTabs.length > 0) {
          console.log(`发现工作区 "${workspace.name}" 的 ${relatedTabs.length} 个真正管理的标签页需要移动到专用窗口`);
          const tabIds = relatedTabs.map((tab) => tab.id);
          const moveResult = await WindowManager.moveTabsToWorkspaceWindow(
            tabIds,
            workspace.id,
            workspace.name
          );
          if (moveResult.success) {
            console.log(`成功移动工作区 "${workspace.name}" 的 ${tabIds.length} 个标签页到专用窗口`);
          } else {
            console.error(`移动工作区 "${workspace.name}" 的标签页失败:`, moveResult.error);
          }
        }
      }
      return { success: true };
    } catch (error) {
      console.error("移动非目标工作区标签页失败:", error);
      return {
        success: false,
        error: {
          code: ERROR_CODES.TAB_ERROR,
          message: "Failed to move non-target workspace tabs",
          details: error
        }
      };
    }
  }
  /**
   * 将当前窗口的所有标签页移动到工作区专用窗口（Workona 风格：完整会话隔离）
   */
  static async moveCurrentTabsToWorkspaceWindow(workspace) {
    try {
      console.log(`🔄 将当前窗口的所有标签页移动到工作区 ${workspace.name} 的专用窗口 (完整会话隔离)`);
      const currentWindow = await chrome.windows.getCurrent();
      const allTabs = await chrome.tabs.query({ windowId: currentWindow.id });
      await this.saveWorkspacePinnedStates(workspace, allTabs);
      const tabsToMove = [];
      const systemTabsCount = allTabs.filter(
        (tab) => tab.url && TabClassificationUtils.isSystemTab(tab.url)
      ).length;
      for (const tab of allTabs) {
        if (tab.url && TabClassificationUtils.isSystemTab(tab.url)) {
          console.log(`🚫 跳过系统标签页: ${tab.url}`);
          continue;
        }
        if (tab.id) {
          tabsToMove.push(tab.id);
          console.log(`📦 准备移动标签页: ${tab.title} (${tab.url})`);
        }
      }
      console.log(`📊 当前窗口状态: 总标签页 ${allTabs.length} 个，系统标签页 ${systemTabsCount} 个，需移动 ${tabsToMove.length} 个`);
      if (tabsToMove.length === 0) {
        console.log(`ℹ️ 当前窗口没有需要移动的标签页`);
        return { success: true };
      }
      const remainingTabsCount = allTabs.length - tabsToMove.length;
      console.log("📊 移动前状态分析:", {
        当前窗口总标签页: allTabs.length,
        系统标签页: systemTabsCount,
        要移动的标签页: tabsToMove.length,
        移动后剩余标签页: remainingTabsCount
      });
      console.log(`🚀 准备移动 ${tabsToMove.length} 个标签页到专用窗口`);
      const moveResult = await WindowManager.moveTabsToWorkspaceWindow(
        tabsToMove,
        workspace.id,
        workspace.name
      );
      if (moveResult.success) {
        console.log(`✅ 成功移动 ${tabsToMove.length} 个标签页到工作区专用窗口`);
        const finalTabs = await chrome.tabs.query({ windowId: currentWindow.id });
        const finalSystemTabsCount = finalTabs.filter(
          (tab) => tab.url && TabClassificationUtils.isSystemTab(tab.url)
        ).length;
        console.log("📊 移动后窗口状态:", {
          剩余标签页: finalTabs.length,
          系统标签页: finalSystemTabsCount,
          用户标签页: finalTabs.length - finalSystemTabsCount
        });
      } else {
        console.error(`❌ 移动标签页到专用窗口失败:`, moveResult.error);
      }
      return moveResult;
    } catch (error) {
      console.error(`移动当前标签页到工作区专用窗口失败:`, error);
      return {
        success: false,
        error: {
          code: ERROR_CODES.TAB_ERROR,
          message: "Failed to move current tabs to workspace window",
          details: error
        }
      };
    }
  }
  /**
   * 从工作区专用窗口移动标签页到当前窗口（优化版）
   */
  static async moveTabsFromWorkspaceWindow(workspace) {
    try {
      console.log(`📥 从工作区 ${workspace.name} 的专用窗口移动标签页到主窗口`);
      const moveResult = await WindowManager.moveTabsFromWorkspaceWindow(workspace.id);
      if (moveResult.success) {
        const movedTabs = moveResult.data;
        console.log(`✅ 成功从专用窗口移动 ${movedTabs.length} 个标签页到主窗口`);
        if (movedTabs.length > 0) {
          console.log("⏳ 等待标签页完全加载到主窗口...");
          await new Promise((resolve) => setTimeout(resolve, 50));
          await this.restoreWorkspacePinnedStates(workspace);
          await this.cleanupLegacyPinnedStates(workspace);
        }
      } else {
        console.error(`❌ 从专用窗口移动标签页失败:`, moveResult.error);
      }
      return { success: true };
    } catch (error) {
      console.error(`❌ 从工作区专用窗口移动标签页失败:`, error);
      return {
        success: false,
        error: {
          code: ERROR_CODES.TAB_ERROR,
          message: "Failed to move tabs from workspace window",
          details: error
        }
      };
    }
  }
  /**
   * 保存工作区的固定状态到 Workona ID 映射中
   */
  static async saveWorkspacePinnedStates(workspace, tabs) {
    try {
      console.log(`💾 保存工作区 "${workspace.name}" 的固定状态到 Workona ID 映射...`);
      let savedCount = 0;
      for (const tab of tabs) {
        if (tab.pinned && tab.id) {
          const workonaIdResult = await WorkonaTabManager.getWorkonaIdByChromeId(tab.id);
          if (workonaIdResult.success && workonaIdResult.data) {
            const workonaId = workonaIdResult.data;
            const metadataResult = await WorkonaTabManager.getTabMetadata(workonaId);
            const existingMetadata = metadataResult.success ? metadataResult.data?.metadata : void 0;
            await WorkonaTabManager.updateTabMetadata(workonaId, {
              metadata: {
                ...existingMetadata,
                source: existingMetadata?.source || "workspace_website",
                isPinned: true,
                pinnedAt: Date.now()
              }
            });
            console.log(`💾 保存固定状态: ${workonaId} -> Chrome ID ${tab.id} (${tab.title})`);
            savedCount++;
          } else {
            console.warn(`⚠️ 无法找到标签页 ${tab.id} 的 Workona ID，跳过固定状态保存`);
          }
        }
      }
      console.log(`✅ 工作区 "${workspace.name}" 固定状态保存完成: ${savedCount} 个标签页`);
    } catch (error) {
      console.error("保存工作区固定状态失败:", error);
    }
  }
  /**
   * 恢复工作区的固定状态（简化版：仅恢复用户主动设置的固定状态）
   */
  static async restoreWorkspacePinnedStates(workspace) {
    try {
      console.log(`📌 开始恢复工作区 "${workspace.name}" 的用户设置固定状态...`);
      const mappingsResult = await StorageManager.getTabIdMappings();
      if (!mappingsResult.success) {
        console.warn("获取标签页映射失败:", mappingsResult.error);
        return;
      }
      const mappings = mappingsResult.data;
      const workspaceMappings = mappings.filter(
        (mapping) => mapping.workspaceId === workspace.id && mapping.chromeId !== null && mapping.metadata?.isPinned === true && mapping.metadata?.pinnedAt && // 必须有明确的固定时间戳
        mapping.metadata?.source !== "session_restored"
        // 排除浏览器重启恢复的
      );
      if (workspaceMappings.length === 0) {
        console.log(`📌 工作区 "${workspace.name}" 没有用户主动设置的固定状态需要恢复`);
        return;
      }
      console.log(`📌 找到 ${workspaceMappings.length} 个用户主动设置的固定标签页`);
      let restoredCount = 0;
      let failedCount = 0;
      for (const mapping of workspaceMappings) {
        try {
          const tab = await chrome.tabs.get(mapping.chromeId);
          if (tab && !tab.pinned) {
            await chrome.tabs.update(mapping.chromeId, { pinned: true });
            console.log(`📌 恢复用户固定状态: ${mapping.workonaId} -> Chrome ID ${mapping.chromeId} (${tab.title})`);
            restoredCount++;
          } else if (tab && tab.pinned) {
            console.log(`✅ 标签页已经是固定状态: ${mapping.workonaId} -> Chrome ID ${mapping.chromeId}`);
            restoredCount++;
          }
        } catch (error) {
          console.warn(`⚠️ 恢复标签页 ${mapping.workonaId} (Chrome ID: ${mapping.chromeId}) 固定状态失败:`, error);
          failedCount++;
          const errorMessage = error instanceof Error ? error.message : String(error);
          if (errorMessage.includes("No tab with id")) {
            await WorkonaTabManager.updateTabMetadata(mapping.workonaId, {
              metadata: {
                ...mapping.metadata,
                source: mapping.metadata?.source || "workspace_website",
                isPinned: false,
                unpinnedAt: Date.now()
              }
            });
            console.log(`🗑️ 清理无效标签页的固定状态: ${mapping.workonaId}`);
          }
        }
      }
      console.log(`✅ 工作区 "${workspace.name}" 用户固定状态恢复完成: 成功 ${restoredCount} 个, 失败 ${failedCount} 个`);
    } catch (error) {
      console.error("恢复工作区固定状态失败:", error);
    }
  }
  /**
   * 清理旧的基于 Chrome ID 的固定状态存储
   */
  static async cleanupLegacyPinnedStates(workspace) {
    try {
      const storageKey = `workspacePinnedTabIds_${workspace.id}`;
      const result = await chrome.storage.local.get([storageKey]);
      const legacyPinnedTabIds = result[storageKey];
      if (legacyPinnedTabIds && Array.isArray(legacyPinnedTabIds) && legacyPinnedTabIds.length > 0) {
        console.log(`🗑️ 清理工作区 "${workspace.name}" 的旧固定状态存储: ${legacyPinnedTabIds.length} 个条目`);
        await chrome.storage.local.remove([storageKey]);
        console.log(`✅ 已清理工作区 "${workspace.name}" 的旧固定状态存储`);
      }
    } catch (error) {
      console.warn("清理旧固定状态存储失败:", error);
    }
  }
  /**
   * 智能检查并自动打开工作区中缺失的网站（修复版）
   */
  static async openWorkspaceWebsites(workspace) {
    try {
      console.log(`🔍 智能检查并打开工作区 ${workspace.name} 中缺失的网站`);
      if (!workspace.websites || workspace.websites.length === 0) {
        console.log(`⚠️ 工作区 "${workspace.name}" 没有配置任何网站，确保窗口安全`);
        console.log("ℹ️ [WorkspaceSwitcher] 空工作区，无需特殊窗口保护处理");
        return { success: true };
      }
      console.log("⏳ 等待标签页移动操作完全完成...");
      await new Promise((resolve) => setTimeout(resolve, 100));
      const currentTabsResult = await TabManager.getCurrentWindowTabs();
      if (!currentTabsResult.success) {
        console.log("❌ 获取当前窗口标签页失败:", currentTabsResult.error);
        return { success: true };
      }
      const currentTabs = currentTabsResult.data;
      const currentUrls = currentTabs.map((tab) => tab.url);
      console.log(`📋 当前窗口已有 ${currentTabs.length} 个标签页:`, currentUrls);
      const missingWebsites = [];
      const existingTabs = [];
      for (const website of workspace.websites) {
        const existingTab = await this.findExistingCoreTabByWorkonaId(workspace.id, website.id, currentTabs);
        if (!existingTab) {
          missingWebsites.push(website);
          console.log(`🔍 发现缺失的网站: ${website.title} (${website.url})`);
        } else {
          existingTabs.push({
            tab: existingTab,
            website
          });
          console.log(`✅ 网站已存在: ${website.title} - 通过 Workona ID 识别`);
          try {
            const workonaIdResult = await WorkonaTabManager.getWorkonaIdByChromeId(existingTab.id);
            if (workonaIdResult.success && workonaIdResult.data) {
              const pinnedStatus = existingTab.isPinned ? "(固定)" : "(非固定)";
              console.log(`✅ 现有标签页已有 Workona ID: ${workonaIdResult.data} ${pinnedStatus}`);
              console.log(`ℹ️ 保持现有元数据，不同步浏览器级别的固定状态`);
            } else {
              console.warn(`⚠️ 现有标签页缺少 Workona ID 映射，尝试重新建立映射`);
              const newWorkonaId = WorkonaTabManager.generateWorkonaTabId(workspace.id);
              await WorkonaTabManager.createTabIdMapping(
                newWorkonaId,
                existingTab.id,
                workspace.id,
                website.id,
                {
                  isWorkspaceCore: true,
                  source: "session_restored"
                }
              );
              console.log(`✅ 重新建立 Workona ID 映射: ${newWorkonaId}`);
            }
          } catch (error) {
            console.error(`❌ 验证现有标签页 Workona ID 失败:`, error);
          }
        }
      }
      if (missingWebsites.length === 0) {
        console.log(`✅ 工作区 "${workspace.name}" 的所有网站都已打开，无需创建新标签页`);
        return { success: true };
      }
      console.log(`🚀 需要打开 ${missingWebsites.length} 个缺失的网站`);
      let successCount = 0;
      let failCount = 0;
      const createdWorkonaIds = [];
      for (const website of missingWebsites) {
        try {
          console.log(`📝 正在创建工作区核心标签页: ${website.title} (${website.url})`);
          const tab = await chrome.tabs.create({
            url: website.url,
            pinned: false,
            // Workona 风格：不使用固定状态
            active: false
            // 不立即激活，保持当前标签页活跃
          });
          if (tab.id) {
            const newWorkonaId = WorkonaTabManager.generateWorkonaTabId(workspace.id);
            const mappingResult = await WorkonaTabManager.createTabIdMapping(
              newWorkonaId,
              tab.id,
              workspace.id,
              website.id,
              {
                isWorkspaceCore: true,
                source: "workspace_website"
              }
            );
            if (mappingResult.success) {
              console.log(`✅ 成功创建工作区核心标签页: ${website.title} (Workona ID: ${newWorkonaId})`);
              createdWorkonaIds.push(newWorkonaId);
              successCount++;
            } else {
              console.error(`❌ 创建 Workona ID 映射失败 ${website.title}:`, mappingResult.error);
              failCount++;
            }
          } else {
            console.error(`❌ 创建标签页失败 ${website.title}: 无标签页ID`);
            failCount++;
          }
        } catch (error) {
          console.error(`❌ 处理网站 ${website.title} 时出错:`, error);
          failCount++;
        }
      }
      if (createdWorkonaIds.length > 0) {
        console.log(`📝 通知会话管理器添加 ${createdWorkonaIds.length} 个新标签页`);
        const addResult = await WorkspaceSessionManager.addNewTabsToCurrentSession(createdWorkonaIds);
        if (!addResult.success) {
          console.warn("添加新标签页到会话失败:", addResult.error);
        }
      }
      console.log(`🎯 工作区 "${workspace.name}" 缺失网站打开完成: 成功 ${successCount} 个，失败 ${failCount} 个`);
      return { success: true };
    } catch (error) {
      console.error("❌ 自动打开缺失网站时出错:", error);
      return {
        success: false,
        error: {
          code: ERROR_CODES.TAB_ERROR,
          message: "Failed to open workspace websites",
          details: error
        }
      };
    }
  }
  /**
   * 重新分类临时标签页到当前工作区
   * 将之前没有工作区时创建的临时标签页重新分类到正确的工作区
   */
  static async reclassifyTemporaryTabs(targetWorkspaceId) {
    try {
      console.log(`🔄 重新分类临时标签页到工作区: ${targetWorkspaceId}`);
      const mappingsResult = await StorageManager.getTabIdMappings();
      if (!mappingsResult.success) {
        console.warn("获取标签页映射失败，跳过重新分类");
        return;
      }
      const mappings = mappingsResult.data;
      const tempMappings = mappings.filter((m) => m.workspaceId === "temp-user-session");
      if (tempMappings.length === 0) {
        console.log("没有找到需要重新分类的临时标签页");
        return;
      }
      console.log(`🔍 找到 ${tempMappings.length} 个临时标签页需要重新分类`);
      const currentWindow = await chrome.windows.getCurrent();
      const allTabs = await chrome.tabs.query({ windowId: currentWindow.id });
      let reclassifiedCount = 0;
      for (const mapping of tempMappings) {
        try {
          const chromeTab = allTabs.find((tab) => tab.id === mapping.chromeId);
          if (!chromeTab) {
            await WorkonaTabManager.removeTabMapping(mapping.workonaId);
            console.log(`🗑️ 删除已关闭标签页的映射: ${mapping.workonaId}`);
            continue;
          }
          const updateResult = await WorkonaTabManager.updateTabMetadata(mapping.workonaId, {
            // 保持原有的标签页类型和核心属性
            isWorkspaceCore: mapping.isWorkspaceCore,
            tabType: mapping.tabType,
            metadata: {
              ...mapping.metadata,
              // 更新工作区归属，但保持其他元数据
              source: mapping.metadata?.source || "user_opened"
            }
          });
          if (updateResult.success) {
            const mappingsResult2 = await StorageManager.getTabIdMappings();
            if (mappingsResult2.success) {
              const allMappings = mappingsResult2.data;
              const mappingIndex = allMappings.findIndex((m) => m.workonaId === mapping.workonaId);
              if (mappingIndex !== -1) {
                allMappings[mappingIndex].workspaceId = targetWorkspaceId;
                allMappings[mappingIndex].lastSyncAt = Date.now();
                await StorageManager.saveTabIdMappings(allMappings);
                console.log(`🔄 更新标签页工作区归属: ${mapping.workonaId} -> 工作区 ${targetWorkspaceId}`);
              }
            }
          }
          if (updateResult.success) {
            reclassifiedCount++;
            console.log(`✅ 更新标签页工作区归属: ${chromeTab.title} (${mapping.workonaId} -> 工作区 ${targetWorkspaceId})`);
          } else {
            console.error(`❌ 更新标签页工作区归属失败: ${chromeTab.title}`, updateResult.error);
          }
        } catch (error) {
          console.error(`❌ 处理临时标签页映射时出错:`, error);
        }
      }
      console.log(`✅ 重新分类完成: ${reclassifiedCount} 个标签页已分类到工作区 ${targetWorkspaceId}`);
    } catch (error) {
      console.error("❌ 重新分类临时标签页失败:", error);
    }
  }
  /**
   * 获取当前活跃的工作区
   */
  static async getCurrentWorkspace() {
    try {
      const activeIdResult = await StorageManager.getActiveWorkspaceId();
      if (!activeIdResult.success) {
        return { success: false, error: activeIdResult.error };
      }
      const activeId = activeIdResult.data;
      if (!activeId) {
        return { success: true, data: null };
      }
      const workspaceResult = await StorageManager.getWorkspace(activeId);
      if (!workspaceResult.success) {
        console.warn(`⚠️ 无法获取活跃工作区 ${activeId}，但保持状态防止意外退出:`, workspaceResult.error);
        const allWorkspacesResult = await StorageManager.getWorkspaces();
        if (allWorkspacesResult.success && allWorkspacesResult.data && allWorkspacesResult.data.length > 0) {
          const fallbackWorkspace = allWorkspacesResult.data[0];
          console.log(`🔄 使用回退工作区: ${fallbackWorkspace.name} (防止工作区意外退出)`);
          return { success: true, data: fallbackWorkspace };
        }
        return { success: true, data: null };
      }
      return { success: true, data: workspaceResult.data };
    } catch (error) {
      return {
        success: false,
        error: {
          code: ERROR_CODES.STORAGE_ERROR,
          message: "Failed to get current workspace",
          details: error
        }
      };
    }
  }
  /**
   * 智能检测当前应该激活的工作区（Workona 风格：完全基于 ID 映射和智能匹配）
   */
  static async detectActiveWorkspace() {
    try {
      const workspacesResult = await StorageManager.getWorkspaces();
      if (!workspacesResult.success) {
        return { success: false, error: workspacesResult.error };
      }
      const workspaces = workspacesResult.data;
      if (workspaces.length === 0) {
        return { success: true, data: null };
      }
      const activeTabResult = await TabManager.getActiveTab();
      if (!activeTabResult.success) {
        return { success: true, data: null };
      }
      const activeTab = activeTabResult.data;
      const workonaIdResult = await WorkonaTabManager.getWorkonaIdByChromeId(activeTab.id);
      if (workonaIdResult.success && workonaIdResult.data) {
        const workonaId = workonaIdResult.data;
        const workspaceId = workonaId.split("-")[1];
        const matchingWorkspace = workspaces.find((w) => w.id === workspaceId);
        if (matchingWorkspace) {
          const metadataResult = await WorkonaTabManager.getTabMetadata(workonaId);
          if (metadataResult.success && metadataResult.data && metadataResult.data.isWorkspaceCore) {
            console.log(`🔗 通过 Workona ID 检测到活跃工作区: ${matchingWorkspace.name} (${workonaId})`);
            return { success: true, data: matchingWorkspace };
          } else {
            console.log(`⚠️ 标签页已降级为会话标签页，不再是工作区专属: ${workonaId}`);
          }
        } else {
          await WorkonaTabManager.removeTabMapping(workonaId);
          console.log(`🗑️ 清理无效的 Workona ID 映射: ${workonaId}`);
        }
      }
      const matchResult = await WorkspaceTabContentMatcher.isWorkspaceTab(activeTab);
      if (matchResult.isMatch && matchResult.workspaceId) {
        const matchingWorkspace = workspaces.find((w) => w.id === matchResult.workspaceId);
        if (matchingWorkspace) {
          console.log(`🏢 通过智能匹配检测到活跃工作区: ${matchingWorkspace.name} (置信度: ${matchResult.confidence})`);
          return { success: true, data: matchingWorkspace };
        }
      }
      console.log(`ℹ️ 活跃标签页未找到对应的工作区: ${activeTab.url}`);
      console.log(`🔄 回退到存储的活跃工作区ID检查`);
      const activeIdResult = await StorageManager.getActiveWorkspaceId();
      if (activeIdResult.success && activeIdResult.data) {
        const storedActiveId = activeIdResult.data;
        const storedWorkspace = workspaces.find((w) => w.id === storedActiveId);
        if (storedWorkspace) {
          console.log(`🎯 使用存储的活跃工作区: ${storedWorkspace.name} (防止意外退出)`);
          return { success: true, data: storedWorkspace };
        } else {
          console.warn(`⚠️ 存储的工作区ID无效但保持状态: ${storedActiveId} (防止意外退出)`);
          if (workspaces.length > 0) {
            const fallbackWorkspace = workspaces[0];
            console.log(`🔄 使用回退工作区: ${fallbackWorkspace.name} (防止工作区意外退出)`);
            return { success: true, data: fallbackWorkspace };
          }
        }
      }
      console.log(`💡 提示：如需将此标签页纳入工作区管理，请手动添加到相应工作区`);
      return { success: true, data: null };
    } catch (error) {
      return {
        success: false,
        error: {
          code: ERROR_CODES.TAB_ERROR,
          message: "Failed to detect active workspace",
          details: error
        }
      };
    }
  }
  /**
   * 添加当前标签页到工作区（统一使用Workona ID血缘关系方法）
   */
  static async addCurrentTabToWorkspace(workspaceId) {
    try {
      const activeTabResult = await TabManager.getActiveTab();
      if (!activeTabResult.success) {
        return { success: false, error: activeTabResult.error };
      }
      const activeTab = activeTabResult.data;
      const addResult = await WorkspaceManager.addCurrentTabByWorkonaId(
        workspaceId,
        activeTab.id,
        {
          url: activeTab.url,
          title: activeTab.title,
          favicon: activeTab.favicon
        }
      );
      if (addResult.success) {
        return { success: true };
      } else {
        return { success: false, error: addResult.error };
      }
    } catch (error) {
      return {
        success: false,
        error: {
          code: ERROR_CODES.TAB_ERROR,
          message: "Failed to add current tab to workspace",
          details: error
        }
      };
    }
  }
  /**
   * 快速切换到下一个工作区
   */
  static async switchToNextWorkspace() {
    try {
      const workspacesResult = await StorageManager.getWorkspaces();
      if (!workspacesResult.success) {
        return { success: false, error: workspacesResult.error };
      }
      const workspaces = workspacesResult.data;
      if (workspaces.length === 0) {
        return {
          success: false,
          error: {
            code: ERROR_CODES.WORKSPACE_NOT_FOUND,
            message: "No workspaces available"
          }
        };
      }
      const currentResult = await this.getCurrentWorkspace();
      if (!currentResult.success) {
        return { success: false, error: currentResult.error };
      }
      const currentWorkspace = currentResult.data;
      let nextIndex = 0;
      if (currentWorkspace) {
        const currentIndex = workspaces.findIndex((w) => w.id === currentWorkspace.id);
        nextIndex = (currentIndex + 1) % workspaces.length;
      }
      const nextWorkspace = workspaces[nextIndex];
      return await this.switchToWorkspace(nextWorkspace.id);
    } catch (error) {
      return {
        success: false,
        error: {
          code: ERROR_CODES.TAB_ERROR,
          message: "Failed to switch to next workspace",
          details: error
        }
      };
    }
  }
  /**
   * 处理工作区切换时的用户标签页隐藏状态
   */
  static async handleUserTabsVisibilityState(workspace) {
    try {
      console.log(`🔄 检查工作区 "${workspace.name}" 的用户标签页隐藏状态`);
      const stateResult = await WorkspaceUserTabsVisibilityManager.getWorkspaceUserTabsState(workspace.id);
      if (!stateResult.success) {
        console.log("⚠️ 无法获取用户标签页隐藏状态，跳过处理");
        return { success: true };
      }
      const { isHidden, hiddenTabIds, pinnedTabIds } = stateResult.data;
      if (isHidden && hiddenTabIds.length > 0) {
        console.log(`🔒 工作区 "${workspace.name}" 有 ${hiddenTabIds.length} 个隐藏的用户标签页，需要保持隐藏状态`);
        const existingTabIds = [];
        for (const tabId of hiddenTabIds) {
          try {
            await chrome.tabs.get(tabId);
            existingTabIds.push(tabId);
          } catch {
            console.log(`⚠️ 隐藏的标签页 ${tabId} 已不存在`);
          }
        }
        if (existingTabIds.length !== hiddenTabIds.length) {
          const removedTabIds = hiddenTabIds.filter((id) => !existingTabIds.includes(id));
          console.log(`🗑️ 清理 ${removedTabIds.length} 个不存在的隐藏标签页`);
          if (existingTabIds.length === 0) {
            await WorkspaceUserTabsVisibilityManager.setWorkspaceUserTabsState(workspace.id, false, []);
            console.log(`✅ 工作区 "${workspace.name}" 的所有隐藏标签页都已不存在，清除隐藏状态`);
          } else {
            const validPinnedTabIds = pinnedTabIds.filter((id) => existingTabIds.includes(id));
            await WorkspaceUserTabsVisibilityManager.setWorkspaceUserTabsState(workspace.id, true, existingTabIds, validPinnedTabIds);
            console.log(`✅ 更新工作区 "${workspace.name}" 的隐藏标签页列表，现有 ${existingTabIds.length} 个`);
          }
        }
        if (existingTabIds.length > 0) {
          const currentWindow = await chrome.windows.getCurrent();
          const currentTabs = await chrome.tabs.query({ windowId: currentWindow.id });
          const tabsToHide = currentTabs.filter((tab) => existingTabIds.includes(tab.id));
          if (tabsToHide.length > 0) {
            console.log(`📤 发现 ${tabsToHide.length} 个应该隐藏的标签页在主窗口中，移动到专用窗口`);
            const tabIdsToMove = tabsToHide.map((tab) => tab.id);
            const moveResult = await WindowManager.moveTabsToWorkspaceWindow(
              tabIdsToMove,
              `workspace-${workspace.id}-hidden-tabs`,
              `工作区 ${workspace.name} - 隐藏的用户标签页`
            );
            if (moveResult.success) {
              console.log(`✅ 成功移动 ${tabIdsToMove.length} 个标签页到专用窗口`);
            } else {
              console.error("❌ 移动隐藏标签页到专用窗口失败:", moveResult.error);
            }
          }
        }
      } else {
        console.log(`✅ 工作区 "${workspace.name}" 没有隐藏的用户标签页`);
      }
      return { success: true };
    } catch (error) {
      console.error("❌ 处理用户标签页隐藏状态时出错:", error);
      return {
        success: false,
        error: {
          code: ERROR_CODES.TAB_ERROR,
          message: "Failed to handle user tabs visibility state",
          details: error
        }
      };
    }
  }
  /**
   * 通知工作区切换完成，触发UI状态更新
   */
  static async notifyWorkspaceSwitchComplete(workspaceId) {
    try {
      WorkspaceStateSync.sendWorkspaceStateUpdate(workspaceId, "switch");
      WorkspaceStateSync.sendWorkspaceStateUpdate(workspaceId, "userTabsVisibility");
      await UserTabsRealTimeMonitor.forceRefreshWorkspaceState(workspaceId);
      console.log(`📢 已发送工作区 ${workspaceId} 切换完成和用户标签页状态更新事件`);
    } catch (error) {
      console.error("通知工作区切换完成失败:", error);
    }
  }
  /**
   * 通知工作区设置状态变更
   */
  static async notifyWorkspaceSetupStatusChange(workspaceId, isSetupInProgress) {
    try {
      chrome.runtime.sendMessage({
        type: "WORKSPACE_SETUP_STATUS_CHANGE",
        workspaceId,
        isSetupInProgress,
        timestamp: Date.now()
      }).catch((error) => {
        console.log("发送工作区设置状态变更消息失败:", error);
      });
      console.log(`📢 已发送工作区 ${workspaceId} 设置状态变更事件: ${isSetupInProgress ? "开始" : "完成"}`);
    } catch (error) {
      console.error("通知工作区设置状态变更失败:", error);
    }
  }
  /**
   * 通过 Workona ID 查找现有的核心标签页
   */
  static async findExistingCoreTabByWorkonaId(workspaceId, websiteId, currentTabs) {
    try {
      const mappingsResult = await StorageManager.getTabIdMappings();
      if (!mappingsResult.success) {
        return null;
      }
      const mappings = mappingsResult.data;
      const mapping = mappings.find(
        (m) => m.workspaceId === workspaceId && m.websiteId === websiteId && m.isWorkspaceCore
      );
      if (!mapping) {
        return null;
      }
      const tab = currentTabs.find((t) => t.id === mapping.chromeId);
      if (tab) {
        tab.workonaId = mapping.workonaId;
        console.log(`🎯 通过 Workona ID 找到核心标签页: ${mapping.workonaId} <-> ${tab.id}`);
        return tab;
      }
      await WorkonaTabManager.removeTabMapping(mapping.workonaId);
      console.log(`🗑️ 清理无效的标签页映射: ${mapping.workonaId}`);
      return null;
    } catch (error) {
      console.error("查找 Workona ID 对应标签页失败:", error);
      return null;
    }
  }
  // Workona 风格：移除传统 URL 匹配逻辑
}

class TabManager {
  /**
   * 获取所有标签页信息
   */
  static async getAllTabs() {
    try {
      const tabs = await chrome.tabs.query({});
      const tabInfos = tabs.map((tab) => ({
        id: tab.id,
        url: tab.url || "",
        title: tab.title || "",
        favicon: tab.favIconUrl || "",
        isPinned: tab.pinned,
        isActive: tab.active,
        windowId: tab.windowId,
        index: tab.index
      }));
      return { success: true, data: tabInfos };
    } catch (error) {
      return {
        success: false,
        error: {
          code: ERROR_CODES.TAB_ERROR,
          message: "Failed to get tabs",
          details: error
        }
      };
    }
  }
  /**
   * 获取当前活跃标签页
   */
  static async getActiveTab() {
    try {
      const tabs = await chrome.tabs.query({ active: true, currentWindow: true });
      if (tabs.length === 0) {
        return {
          success: false,
          error: {
            code: ERROR_CODES.TAB_ERROR,
            message: "No active tab found"
          }
        };
      }
      const tab = tabs[0];
      const tabInfo = {
        id: tab.id,
        url: tab.url || "",
        title: tab.title || "",
        favicon: tab.favIconUrl || "",
        isPinned: tab.pinned,
        isActive: tab.active,
        windowId: tab.windowId,
        index: tab.index
      };
      return { success: true, data: tabInfo };
    } catch (error) {
      return {
        success: false,
        error: {
          code: ERROR_CODES.TAB_ERROR,
          message: "Failed to get active tab",
          details: error
        }
      };
    }
  }
  /**
   * 基于Workona ID血缘关系查找标签页（替代URL匹配）
   */
  static async findTabByWorkonaId(workonaId) {
    try {
      const chromeIdResult = await WorkonaTabManager.getChromeIdByWorkonaId(workonaId);
      if (!chromeIdResult.success || !chromeIdResult.data) {
        return { success: true, data: null };
      }
      try {
        const tab = await chrome.tabs.get(chromeIdResult.data);
        return {
          success: true,
          data: {
            id: tab.id,
            url: tab.url || "",
            title: tab.title || "",
            favicon: tab.favIconUrl || "",
            isPinned: tab.pinned,
            isActive: tab.active,
            windowId: tab.windowId,
            index: tab.index
          }
        };
      } catch {
        await WorkonaTabManager.removeTabMapping(workonaId);
        return { success: true, data: null };
      }
    } catch (error) {
      return {
        success: false,
        error: {
          code: ERROR_CODES.TAB_ERROR,
          message: "Failed to find tab by URL",
          details: error
        }
      };
    }
  }
  /**
   * 创建新标签页
   */
  static async createTab(url, pinned = false, active = true) {
    try {
      const tab = await chrome.tabs.create({
        url,
        pinned,
        active
      });
      const tabInfo = {
        id: tab.id,
        url: tab.url || "",
        title: tab.title || "",
        favicon: tab.favIconUrl || "",
        isPinned: tab.pinned,
        isActive: tab.active,
        windowId: tab.windowId,
        index: tab.index
      };
      return { success: true, data: tabInfo };
    } catch (error) {
      return {
        success: false,
        error: {
          code: ERROR_CODES.TAB_ERROR,
          message: "Failed to create tab",
          details: error
        }
      };
    }
  }
  /**
   * 激活标签页
   */
  static async activateTab(tabId) {
    try {
      console.log(`Activating tab ${tabId}`);
      await chrome.tabs.update(tabId, { active: true });
      console.log(`Successfully activated tab ${tabId}`);
      return { success: true };
    } catch (error) {
      console.error(`Failed to activate tab ${tabId}:`, error);
      return {
        success: false,
        error: {
          code: ERROR_CODES.TAB_ERROR,
          message: "Failed to activate tab",
          details: error
        }
      };
    }
  }
  // Workona 风格：移除固定标签页功能，完全基于 Workona ID 映射管理
  /**
   * 关闭标签页
   */
  static async closeTab(tabId) {
    try {
      try {
        const tab = await chrome.tabs.get(tabId);
        if (tab.pinned) {
          await chrome.tabs.update(tabId, { pinned: false });
        }
      } catch (error) {
        console.warn("取消标签页固定状态失败:", error);
      }
      await chrome.tabs.remove(tabId);
      return { success: true };
    } catch (error) {
      return {
        success: false,
        error: {
          code: ERROR_CODES.TAB_ERROR,
          message: "Failed to close tab",
          details: error
        }
      };
    }
  }
  /**
   * 关闭多个标签页
   */
  static async closeTabs(tabIds) {
    try {
      for (const tabId of tabIds) {
        try {
          const tab = await chrome.tabs.get(tabId);
          if (tab.pinned) {
            await chrome.tabs.update(tabId, { pinned: false });
          }
        } catch (error) {
          console.warn(`取消标签页 ${tabId} 固定状态失败:`, error);
        }
      }
      await chrome.tabs.remove(tabIds);
      return { success: true };
    } catch (error) {
      return {
        success: false,
        error: {
          code: ERROR_CODES.TAB_ERROR,
          message: "Failed to close tabs",
          details: error
        }
      };
    }
  }
  /**
   * 获取当前窗口的所有标签页
   */
  static async getCurrentWindowTabs() {
    try {
      const tabs = await chrome.tabs.query({ currentWindow: true });
      const tabInfos = tabs.map((tab) => ({
        id: tab.id,
        url: tab.url || "",
        title: tab.title || "",
        favicon: tab.favIconUrl || "",
        isPinned: tab.pinned,
        isActive: tab.active,
        windowId: tab.windowId,
        index: tab.index
      }));
      return { success: true, data: tabInfos };
    } catch (error) {
      return {
        success: false,
        error: {
          code: ERROR_CODES.TAB_ERROR,
          message: "Failed to get current window tabs",
          details: error
        }
      };
    }
  }
  /**
   * 获取工作区真正管理的标签页（Workona 风格：完全基于 ID 映射）
   */
  static async getWorkspaceRelatedTabs(workspace) {
    try {
      console.log(`🔍 查找工作区 "${workspace.name}" 的 Workona 管理标签页`);
      const workonaTabIds = await WorkonaTabManager.getWorkspaceWorkonaTabIds(workspace.id);
      if (!workonaTabIds.success) {
        console.log(`❌ 获取工作区 Workona 标签页ID失败:`, workonaTabIds.error);
        return { success: true, data: [] };
      }
      const relatedTabs = [];
      const validWorkonaIds = [];
      for (const workonaId of workonaTabIds.data) {
        const chromeIdResult = await WorkonaTabManager.getChromeIdByWorkonaId(workonaId);
        if (chromeIdResult.success && chromeIdResult.data) {
          try {
            const tab = await chrome.tabs.get(chromeIdResult.data);
            if (tab) {
              relatedTabs.push({
                id: tab.id,
                url: tab.url,
                title: tab.title,
                favicon: tab.favIconUrl || "",
                isPinned: tab.pinned,
                isActive: tab.active,
                windowId: tab.windowId,
                index: tab.index
              });
              validWorkonaIds.push(workonaId);
              console.log(`✅ 找到 Workona 管理的标签页: ${tab.title} (${workonaId})`);
            }
          } catch (tabError) {
            await WorkonaTabManager.removeTabMapping(workonaId);
            console.log(`🗑️ 清理无效的 Workona ID 映射: ${workonaId}`);
          }
        } else {
          await WorkonaTabManager.removeTabMapping(workonaId);
          console.log(`🗑️ 清理无效的 Workona ID 映射: ${workonaId}`);
        }
      }
      console.log(`📊 工作区 "${workspace.name}" Workona 管理的标签页: ${relatedTabs.length} 个 (有效映射: ${validWorkonaIds.length})`);
      return { success: true, data: relatedTabs };
    } catch (error) {
      console.error("获取工作区相关标签页失败:", error);
      return {
        success: false,
        error: {
          code: ERROR_CODES.TAB_ERROR,
          message: "Failed to get workspace related tabs",
          details: error
        }
      };
    }
  }
  /**
   * 获取非工作区相关的标签页（仅在当前窗口中查找）
   */
  static async getNonWorkspaceRelatedTabs(workspace) {
    try {
      console.log(`查找非工作区 "${workspace.name}" 相关的标签页`);
      const currentTabsResult = await this.getCurrentWindowTabs();
      if (!currentTabsResult.success) return currentTabsResult;
      const currentTabs = currentTabsResult.data;
      const workspaceUrls = workspace.websites.map((w) => w.url);
      const nonRelatedTabs = currentTabs.filter(
        (tab) => !workspaceUrls.some((url) => tab.url.startsWith(url))
      );
      return { success: true, data: nonRelatedTabs };
    } catch (error) {
      return {
        success: false,
        error: {
          code: ERROR_CODES.TAB_ERROR,
          message: "Failed to get non-workspace related tabs",
          details: error
        }
      };
    }
  }
  /**
   * 检查标签页是否为用户手动打开的（非工作区配置的标签页）
   */
  static async isUserOpenedTab(tabId) {
    try {
      const workonaIdResult = await WorkonaTabManager.getWorkonaIdByChromeId(tabId);
      if (!workonaIdResult.success || !workonaIdResult.data) {
        return true;
      }
      const metadataResult = await WorkonaTabManager.getTabMetadata(workonaIdResult.data);
      if (!metadataResult.success || !metadataResult.data) {
        return true;
      }
      const { metadata } = metadataResult.data;
      return metadata?.source === "user_opened";
    } catch {
      return true;
    }
  }
  /**
   * 获取当前应该用于新标签页的工作区（避免循环依赖）
   */
  static async getCurrentWorkspaceForNewTab() {
    try {
      console.log(`🔍 [getCurrentWorkspaceForNewTab] 开始获取当前工作区...`);
      const activeIdResult = await StorageManager.getActiveWorkspaceId();
      if (activeIdResult.success && activeIdResult.data) {
        const workspacesResult = await StorageManager.getWorkspaces();
        if (workspacesResult.success && workspacesResult.data) {
          const workspace = workspacesResult.data.find((w) => w.id === activeIdResult.data);
          if (workspace) {
            console.log(`🎯 [getCurrentWorkspaceForNewTab] 通过存储的活跃工作区ID检测到当前工作区: ${workspace.name}`);
            return workspace;
          }
        }
      }
      const currentTabsResult = await this.getCurrentWindowTabs();
      if (!currentTabsResult.success) {
        console.log(`❌ [getCurrentWorkspaceForNewTab] 获取当前窗口标签页失败:`, currentTabsResult.error);
        return null;
      }
      const currentTabs = currentTabsResult.data;
      console.log(`🔍 [getCurrentWorkspaceForNewTab] 当前窗口有 ${currentTabs.length} 个标签页`);
      for (const tab of currentTabs) {
        console.log(`🔍 [getCurrentWorkspaceForNewTab] 检查标签页: ${tab.title} (${tab.url})`);
        const workonaIdResult = await WorkonaTabManager.getWorkonaIdByChromeId(tab.id);
        console.log(`🔍 [getCurrentWorkspaceForNewTab] 标签页 ${tab.id} 的 Workona ID:`, workonaIdResult);
        if (workonaIdResult.success && workonaIdResult.data) {
          const workspaceId = workonaIdResult.data.split("-")[1];
          console.log(`🔍 [getCurrentWorkspaceForNewTab] 从 Workona ID ${workonaIdResult.data} 提取工作区ID: ${workspaceId}`);
          const workspacesResult = await StorageManager.getWorkspaces();
          if (workspacesResult.success && workspacesResult.data) {
            const workspace = workspacesResult.data.find((w) => w.id === workspaceId);
            if (workspace) {
              console.log(`🎯 [getCurrentWorkspaceForNewTab] 通过窗口中其他标签页检测到当前工作区: ${workspace.name}`);
              return workspace;
            } else {
              console.log(`⚠️ [getCurrentWorkspaceForNewTab] 工作区 ${workspaceId} 不存在`);
            }
          }
        }
      }
      console.log(`🔍 [getCurrentWorkspaceForNewTab] 策略2失败，策略3：通过活跃工作区检测`);
      try {
        const activeWorkspaceResult = await WorkspaceSwitcher.detectActiveWorkspace();
        if (activeWorkspaceResult.success && activeWorkspaceResult.data) {
          const workspace = activeWorkspaceResult.data;
          console.log(`🎯 [getCurrentWorkspaceForNewTab] 通过 WorkspaceSwitcher 检测到当前工作区: ${workspace.name}`);
          return workspace;
        } else {
          console.log(`⚠️ [getCurrentWorkspaceForNewTab] WorkspaceSwitcher 未检测到活跃工作区`);
        }
      } catch (error) {
        console.warn(`⚠️ [getCurrentWorkspaceForNewTab] 策略3失败:`, error);
      }
      console.log(`🔍 [getCurrentWorkspaceForNewTab] 策略3失败，策略4：使用默认工作区`);
      try {
        const workspacesResult = await StorageManager.getWorkspaces();
        if (workspacesResult.success && workspacesResult.data && workspacesResult.data.length > 0) {
          const firstWorkspace = workspacesResult.data[0];
          console.log(`🎯 [getCurrentWorkspaceForNewTab] 使用默认工作区: ${firstWorkspace.name}`);
          return firstWorkspace;
        }
      } catch (error) {
        console.warn(`⚠️ [getCurrentWorkspaceForNewTab] 策略4失败:`, error);
      }
      console.log(`🎯 [getCurrentWorkspaceForNewTab] 所有策略失败，将创建临时工作区归属`);
      return null;
    } catch (error) {
      console.error("❌ [getCurrentWorkspaceForNewTab] 获取当前工作区失败:", error);
      return null;
    }
  }
  /**
   * 自动为新标签页创建会话临时 Workona ID（概念性重构）
   */
  static async autoClassifyNewTab(tabId, url) {
    try {
      console.log(`🎯 [autoClassifyNewTab] 开始自动分类标签页: ID=${tabId}, URL=${url}`);
      if (TabClassificationUtils.isSystemTab(url)) {
        console.log(`🚫 [autoClassifyNewTab] 跳过系统页面: ${url}`);
        return { success: true };
      }
      const existingResult = await WorkonaTabManager.getWorkonaIdByChromeId(tabId);
      console.log(`🔍 [autoClassifyNewTab] 检查现有 Workona ID:`, existingResult);
      if (existingResult.success && existingResult.data) {
        console.log(`ℹ️ [autoClassifyNewTab] 标签页已有 Workona ID: ${existingResult.data}，跳过分类`);
        return { success: true };
      }
      console.log(`🔍 [autoClassifyNewTab] 开始获取当前工作区...`);
      const activeWorkspace = await this.getCurrentWorkspaceForNewTab();
      console.log(`🔍 [autoClassifyNewTab] 检查当前工作区:`, activeWorkspace);
      if (!activeWorkspace) {
        console.warn(`⚠️ [autoClassifyNewTab] 无当前工作区，为标签页创建智能归属映射: ${url}`);
        const pendingWorkspaceId = "pending-assignment";
        const workonaId2 = WorkonaTabManager.generateWorkonaTabId(pendingWorkspaceId);
        console.log(`🆔 为等待归属的用户标签页生成 Workona ID: ${workonaId2}`);
        const mappingResult2 = await WorkonaTabManager.createTabIdMapping(
          workonaId2,
          tabId,
          pendingWorkspaceId,
          void 0,
          // 无对应的网站配置ID
          {
            isWorkspaceCore: false,
            // 标记为等待归属的标签页
            source: "user_opened"
          }
        );
        if (mappingResult2.success) {
          console.log(`✅ [autoClassifyNewTab] 成功为等待归属标签页创建映射: ${url} -> ${workonaId2}`);
        } else {
          console.error(`❌ [autoClassifyNewTab] 创建等待归属映射失败:`, mappingResult2.error);
        }
        return { success: mappingResult2.success, error: mappingResult2.error };
      }
      console.log(`✅ 找到活跃工作区: ${activeWorkspace.name} (ID: ${activeWorkspace.id})`);
      const workonaId = WorkonaTabManager.generateWorkonaTabId(activeWorkspace.id);
      console.log(`🆔 生成 Workona ID: ${workonaId}`);
      const mappingResult = await WorkonaTabManager.createTabIdMapping(
        workonaId,
        tabId,
        activeWorkspace.id,
        void 0,
        // 无对应的网站配置ID
        {
          isWorkspaceCore: false,
          // 标记为会话临时标签页
          source: "user_opened"
        }
      );
      console.log(`🔍 创建映射结果:`, mappingResult);
      if (mappingResult.success) {
        console.log(`✨ [autoClassifyNewTab] 自动为用户标签页创建会话临时 Workona ID: ${workonaId} (${url})`);
        const verifyResult = await WorkonaTabManager.getWorkonaIdByChromeId(tabId);
        console.log(`🔍 [autoClassifyNewTab] 验证映射创建结果:`, verifyResult);
        if (verifyResult.success && verifyResult.data) {
          console.log(`✅ [autoClassifyNewTab] 映射验证成功: ${verifyResult.data}`);
          try {
            console.log(`🔄 触发用户标签页状态更新 (工作区: ${activeWorkspace.id}, 新标签页分类)`);
            await UserTabsRealTimeMonitor.triggerImmediateStateCheck();
            await UserTabsRealTimeMonitor.forceRefreshWorkspaceState(activeWorkspace.id);
            console.log(`✅ 用户标签页状态更新完成 (工作区: ${activeWorkspace.id})`);
          } catch (updateError) {
            console.warn("触发用户标签页状态更新失败:", updateError);
          }
        } else {
          console.error(`❌ [autoClassifyNewTab] 映射验证失败:`, verifyResult);
        }
      } else {
        console.error(`❌ [autoClassifyNewTab] 创建 Workona ID 映射失败:`, mappingResult.error);
      }
      return { success: mappingResult.success, error: mappingResult.error };
    } catch (error) {
      return {
        success: false,
        error: {
          code: ERROR_CODES.TAB_ERROR,
          message: "Failed to auto-classify new tab",
          details: error
        }
      };
    }
  }
  /**
   * 获取当前工作区中用户自行打开的标签页（Workona 风格：基于 ID 映射）
   */
  static async getUserOpenedTabs(workspace) {
    try {
      console.log(`🔍 查找工作区 "${workspace.name}" 中用户自行打开的标签页 (Workona 风格)`);
      const currentTabsResult = await this.getCurrentWindowTabs();
      if (!currentTabsResult.success) return currentTabsResult;
      const currentTabs = currentTabsResult.data;
      const userOpenedTabs = [];
      for (const tab of currentTabs) {
        if (TabClassificationUtils.isSystemTab(tab.url)) {
          continue;
        }
        const workonaIdResult = await WorkonaTabManager.getWorkonaIdByChromeId(tab.id);
        if (!workonaIdResult.success || !workonaIdResult.data) {
          userOpenedTabs.push(tab);
          console.log(`👤 发现用户自行打开的标签页: ${tab.title} (${tab.url})`);
        } else {
          console.log(`🏢 跳过 Workona 管理的标签页: ${tab.title} (${workonaIdResult.data})`);
        }
      }
      console.log(`📊 工作区 "${workspace.name}" 中用户自行打开的标签页共 ${userOpenedTabs.length} 个`);
      return { success: true, data: userOpenedTabs };
    } catch (error) {
      console.error("获取用户自行打开的标签页失败:", error);
      return {
        success: false,
        error: {
          code: ERROR_CODES.TAB_ERROR,
          message: "Failed to get user opened tabs",
          details: error
        }
      };
    }
  }
  /**
   * 获取当前工作区中配置的标签页（工作区设置中明确添加的网站标签页）
   */
  static async getWorkspaceConfiguredTabs(workspace) {
    try {
      const currentTabsResult = await this.getCurrentWindowTabs();
      if (!currentTabsResult.success) return currentTabsResult;
      const currentTabs = currentTabsResult.data;
      const workspaceUrls = workspace.websites.map((w) => w.url);
      const configuredTabs = currentTabs.filter((tab) => {
        return workspaceUrls.some((url) => tab.url.startsWith(url));
      });
      console.log(`工作区 "${workspace.name}" 中配置的标签页共 ${configuredTabs.length} 个`);
      return { success: true, data: configuredTabs };
    } catch (error) {
      console.error("获取工作区配置的标签页失败:", error);
      return {
        success: false,
        error: {
          code: ERROR_CODES.TAB_ERROR,
          message: "Failed to get workspace configured tabs",
          details: error
        }
      };
    }
  }
  // ===== Workona 风格标签页管理方法 =====
  /**
   * 创建 Workona 风格标签页
   */
  static async createWorkonaTab(options) {
    try {
      console.log(`🚀 创建 Workona 标签页: ${options.url} (工作区: ${options.workspaceId})`);
      const workonaId = WorkonaTabManager.generateWorkonaTabId(options.workspaceId);
      const tab = await chrome.tabs.create({
        url: options.url,
        pinned: options.isPinned ?? true,
        active: options.isActive ?? false
      });
      if (!tab.id) {
        throw new Error("Failed to create Chrome tab");
      }
      const tabInfo = {
        id: tab.id,
        url: tab.url || options.url,
        title: tab.title || options.title || "",
        favicon: tab.favIconUrl || "",
        isPinned: tab.pinned,
        isActive: tab.active,
        windowId: tab.windowId,
        index: tab.index
      };
      const mappingResult = await WorkonaTabManager.createTabIdMapping(
        workonaId,
        tab.id,
        options.workspaceId,
        options.websiteId
      );
      if (!mappingResult.success) {
        try {
          await chrome.tabs.remove(tab.id);
        } catch (cleanupError) {
          console.warn("清理失败的标签页时出错:", cleanupError);
        }
        return { success: false, error: mappingResult.error };
      }
      console.log(`✅ 成功创建 Workona 标签页: ${workonaId} <-> ${tab.id}`);
      return {
        success: true,
        data: { tabInfo, workonaId }
      };
    } catch (error) {
      return {
        success: false,
        error: {
          code: ERROR_CODES.TAB_ERROR,
          message: "Failed to create Workona tab",
          details: error
        }
      };
    }
  }
  /**
   * 获取标签页的 Workona ID
   */
  static async getTabWorkonaId(chromeTabId) {
    return await WorkonaTabManager.getWorkonaIdByChromeId(chromeTabId);
  }
  /**
   * 通过 Workona ID 获取标签页信息
   */
  static async getTabByWorkonaId(workonaId) {
    try {
      const chromeIdResult = await WorkonaTabManager.getChromeIdByWorkonaId(workonaId);
      if (!chromeIdResult.success || !chromeIdResult.data) {
        return { success: true, data: null };
      }
      const chromeId = chromeIdResult.data;
      try {
        const tab = await chrome.tabs.get(chromeId);
        const tabInfo = {
          id: tab.id,
          url: tab.url || "",
          title: tab.title || "",
          favicon: tab.favIconUrl || "",
          isPinned: tab.pinned,
          isActive: tab.active,
          windowId: tab.windowId,
          index: tab.index
        };
        return { success: true, data: tabInfo };
      } catch (tabError) {
        await WorkonaTabManager.removeTabMapping(workonaId);
        return { success: true, data: null };
      }
    } catch (error) {
      return {
        success: false,
        error: {
          code: ERROR_CODES.TAB_ERROR,
          message: "Failed to get tab by Workona ID",
          details: error
        }
      };
    }
  }
  /**
   * 同步工作区的 Workona 标签页映射
   */
  static async syncWorkspaceTabMappings(workspaceId) {
    try {
      console.log(`🔄 同步工作区 ${workspaceId} 的标签页映射...`);
      const workonaIdsResult = await WorkonaTabManager.getWorkspaceWorkonaTabIds(workspaceId);
      if (!workonaIdsResult.success) {
        return { success: false, error: workonaIdsResult.error };
      }
      const workonaIds = workonaIdsResult.data;
      let syncedCount = 0;
      let cleanedCount = 0;
      for (const workonaId of workonaIds) {
        const tabResult = await this.getTabByWorkonaId(workonaId);
        if (!tabResult.success) {
          continue;
        }
        if (tabResult.data) {
          syncedCount++;
        } else {
          cleanedCount++;
        }
      }
      console.log(`✅ 工作区 ${workspaceId} 标签页映射同步完成: ${syncedCount} 个有效, ${cleanedCount} 个已清理`);
      return { success: true };
    } catch (error) {
      return {
        success: false,
        error: {
          code: ERROR_CODES.TAB_ERROR,
          message: "Failed to sync workspace tab mappings",
          details: error
        }
      };
    }
  }
}
class WorkspaceTabContentMatcher {
  /**
   * 检查标签页是否与工作区网站匹配（Workona 风格：完全基于 ID 映射和 URL 匹配）
   */
  static async isWorkspaceTab(tab) {
    try {
      if (TabClassificationUtils.isSystemTab(tab.url)) {
        return {
          isMatch: false,
          confidence: 0,
          matchType: "none"
        };
      }
      const workonaIdResult = await WorkonaTabManager.getWorkonaIdByChromeId(tab.id);
      let workonaTabId;
      if (workonaIdResult.success && workonaIdResult.data) {
        workonaTabId = workonaIdResult.data;
        const workspaceId = workonaTabId.split("-")[1];
        console.log(`🔗 通过 Workona ID 映射找到工作区标签页: ${tab.url} -> ${workonaTabId} (工作区: ${workspaceId})`);
        const workspacesResult = await StorageManager.getWorkspaces();
        if (workspacesResult.success && workspacesResult.data) {
          const matchingWorkspace = workspacesResult.data.find((w) => w.id === workspaceId);
          if (matchingWorkspace) {
            const metadataResult = await WorkonaTabManager.getTabMetadata(workonaTabId);
            const websiteId = metadataResult.success ? metadataResult.data?.websiteId : void 0;
            return {
              isMatch: true,
              workspaceId,
              websiteId,
              workonaTabId,
              confidence: 1,
              // 最高置信度
              matchType: "exact"
            };
          } else {
            await WorkonaTabManager.removeTabMapping(workonaTabId);
            console.log(`🗑️ 清理无效的 Workona ID 映射: ${workonaTabId}`);
          }
        }
      }
      console.log(`🔍 标签页 ${tab.url} 没有Workona ID映射，不属于任何工作区`);
      return {
        isMatch: false,
        confidence: 0,
        matchType: "none"
      };
    } catch (error) {
      console.error("检查工作区标签页匹配失败:", error);
      return {
        isMatch: false,
        confidence: 0,
        matchType: "none"
      };
    }
  }
  // URL匹配方法已移除，使用纯ID映射机制
  // getAllWorkspaceWebsitesWithDetails 方法已移除，使用纯ID映射机制
}
class DebugController {
  // 可以通过环境变量或设置控制调试模式
  static isDebugMode = false;
  // 设置为 true 启用详细日志
  static isVerboseMode = false;
  // 设置为 true 启用超详细日志
}
class UserTabsUtils {
  /**
   * 检查标签页是否为真正的用户标签页（非工作区管理的标签页）
   * 基于 isWorkspaceCore 元数据进行精确判断
   */
  static async isRealUserTab(tab) {
    try {
      if (DebugController.isVerboseMode) ;
      if (this.isNewTabPage(tab)) {
        console.log(`🚫 排除新标签页: ${tab.url} (${tab.title})`);
        return false;
      }
      const workonaIdResult = await WorkonaTabManager.getWorkonaIdByChromeId(tab.id);
      if (DebugController.isVerboseMode) ;
      if (!workonaIdResult.success || !workonaIdResult.data) {
        if (tab.url && !WorkspaceUserTabsVisibilityManager.isSystemTab(tab.url)) {
          if (DebugController.isDebugMode) ;
          try {
            console.log(`🔄 尝试为用户标签页创建 Workona ID: ${tab.title}`);
            const classifyResult = await TabManager.autoClassifyNewTab(tab.id, tab.url);
            if (classifyResult.success) {
              console.log(`✅ 成功为用户标签页创建 Workona ID: ${tab.title}`);
              return true;
            } else {
              console.warn(`⚠️ 为用户标签页创建 Workona ID 失败: ${tab.title}`, classifyResult.error);
              return true;
            }
          } catch (error) {
            console.warn(`⚠️ 自动分类标签页时出错: ${tab.title}`, error);
            return true;
          }
        } else {
          if (DebugController.isDebugMode) ;
          return false;
        }
      }
      const metadataResult = await WorkonaTabManager.getTabMetadata(workonaIdResult.data);
      if (DebugController.isVerboseMode) ;
      if (!metadataResult.success || !metadataResult.data) {
        console.log(`⚠️ Workona ID 映射损坏: ${workonaIdResult.data}`);
        return false;
      }
      const { isWorkspaceCore } = metadataResult.data;
      if (isWorkspaceCore) {
        if (DebugController.isDebugMode) ;
        return false;
      } else {
        if (DebugController.isDebugMode) ;
        return true;
      }
    } catch (error) {
      console.error(`❌ 检查用户标签页时出错:`, error);
      return false;
    }
  }
  /**
   * 检查是否为新标签页
   */
  static isNewTabPage(tab) {
    return tab.url === "chrome://newtab/" || tab.url === "chrome://new-tab-page/" || tab.url === "about:newtab" || tab.url === "edge://newtab/" || tab.url && tab.url.startsWith("chrome://newtab") || (tab.title === "New Tab" || tab.title === "新标签页" || tab.title === "Neuer Tab");
  }
}
class UserTabsRealTimeMonitor {
  static isMonitoring = false;
  static monitoringInterval = null;
  static lastStateSnapshot = /* @__PURE__ */ new Map();
  static MONITOR_INTERVAL = 2e3;
  // 优化为2秒检查间隔，减少日志洪水
  static pendingUpdate = false;
  // 防止重复更新
  /**
   * 启动实时监控
   */
  static startMonitoring() {
    if (this.isMonitoring) {
      console.log("📊 用户标签页实时监控已在运行中");
      return;
    }
    console.log("🚀 启动用户标签页实时监控");
    this.isMonitoring = true;
    this.checkUserTabsStateChanges();
    this.monitoringInterval = setInterval(() => {
      this.checkUserTabsStateChanges();
    }, this.MONITOR_INTERVAL);
  }
  /**
   * 停止实时监控
   */
  static stopMonitoring() {
    if (!this.isMonitoring) {
      return;
    }
    console.log("⏹️ 停止用户标签页实时监控");
    this.isMonitoring = false;
    if (this.monitoringInterval) {
      clearInterval(this.monitoringInterval);
      this.monitoringInterval = null;
    }
    this.lastStateSnapshot.clear();
  }
  /**
   * 检查用户标签页状态变化（优化版：防重复更新）
   */
  static async checkUserTabsStateChanges() {
    if (this.pendingUpdate) {
      return;
    }
    try {
      this.pendingUpdate = true;
      const activeWorkspace = await this.getCurrentActiveWorkspace();
      if (!activeWorkspace) {
        return;
      }
      const workspaceId = activeWorkspace.id;
      const currentState = await WorkspaceUserTabsVisibilityManager.getWorkspaceUserTabsState(workspaceId);
      if (!currentState.success) {
        return;
      }
      const stateData = currentState.data;
      const stateKey = `workspace_${workspaceId}`;
      const lastState = this.lastStateSnapshot.get(stateKey);
      const currentSnapshot = {
        isHidden: stateData.isHidden,
        hiddenTabsCount: stateData.hiddenTabIds.length,
        totalUserTabs: stateData.totalUserTabs,
        visibleUserTabs: stateData.visibleUserTabs,
        actionType: stateData.actionType,
        timestamp: Date.now()
      };
      if (lastState && this.hasStateChanged(lastState, currentSnapshot)) {
        if (DebugController.isDebugMode) ;
        this.notifyStateChange(workspaceId, currentSnapshot);
      }
      this.lastStateSnapshot.set(stateKey, currentSnapshot);
    } catch (error) {
      console.warn("检查用户标签页状态变化时出错:", error);
    } finally {
      this.pendingUpdate = false;
    }
  }
  /**
   * 获取当前活跃工作区（使用多种策略）
   */
  static async getCurrentActiveWorkspace() {
    try {
      const activeWorkspaceResult = await WorkspaceSwitcher.detectActiveWorkspace();
      if (activeWorkspaceResult.success && activeWorkspaceResult.data) {
        console.log(`🎯 通过 WorkspaceSwitcher 检测到活跃工作区: ${activeWorkspaceResult.data.name}`);
        return activeWorkspaceResult.data;
      }
      const currentWorkspaceResult = await WorkspaceSwitcher.getCurrentWorkspace();
      if (currentWorkspaceResult.success && currentWorkspaceResult.data) {
        if (DebugController.isVerboseMode) ;
        return currentWorkspaceResult.data;
      }
      const currentWindow = await chrome.windows.getCurrent();
      const windowTabs = await chrome.tabs.query({ windowId: currentWindow.id });
      const workspaceTabCounts = /* @__PURE__ */ new Map();
      for (const tab of windowTabs) {
        if (tab.id) {
          const workonaIdResult = await WorkonaTabManager.getWorkonaIdByChromeId(tab.id);
          if (workonaIdResult.success && workonaIdResult.data) {
            const workspaceId = workonaIdResult.data.split("-")[1];
            workspaceTabCounts.set(workspaceId, (workspaceTabCounts.get(workspaceId) || 0) + 1);
          }
        }
      }
      if (workspaceTabCounts.size > 0) {
        const [mostActiveWorkspaceId] = [...workspaceTabCounts.entries()].reduce((a, b) => a[1] > b[1] ? a : b);
        const workspacesResult = await StorageManager.getWorkspaces();
        if (workspacesResult.success && workspacesResult.data) {
          const workspace = workspacesResult.data.find((w) => w.id === mostActiveWorkspaceId);
          if (workspace) {
            console.log(`🎯 通过窗口标签页推断活跃工作区: ${workspace.name}`);
            return workspace;
          }
        }
      }
      console.log(`⚠️ 无法检测到当前活跃工作区`);
      return null;
    } catch (error) {
      console.warn("获取当前活跃工作区失败:", error);
      return null;
    }
  }
  /**
   * 检查状态是否发生变化
   */
  static hasStateChanged(lastState, currentState) {
    return lastState.isHidden !== currentState.isHidden || lastState.hiddenTabsCount !== currentState.hiddenTabsCount || lastState.totalUserTabs !== currentState.totalUserTabs || lastState.visibleUserTabs !== currentState.visibleUserTabs || lastState.actionType !== currentState.actionType;
  }
  /**
   * 通知状态变化
   */
  static async notifyStateChange(workspaceId, _newState) {
    try {
      WorkspaceStateSync.sendWorkspaceStateUpdate(workspaceId, "userTabsVisibility");
      console.log(`📢 已发送用户标签页状态变化通知: 工作区 ${workspaceId}`);
    } catch (error) {
      console.warn("发送用户标签页状态变化通知失败:", error);
    }
  }
  /**
   * 强制刷新指定工作区的状态
   */
  static async forceRefreshWorkspaceState(workspaceId) {
    try {
      const stateKey = `workspace_${workspaceId}`;
      const currentState = await WorkspaceUserTabsVisibilityManager.getWorkspaceUserTabsState(workspaceId);
      if (currentState.success) {
        const stateData = currentState.data;
        const newSnapshot = {
          isHidden: stateData.isHidden,
          hiddenTabsCount: stateData.hiddenTabIds.length,
          totalUserTabs: stateData.totalUserTabs,
          visibleUserTabs: stateData.visibleUserTabs,
          actionType: stateData.actionType,
          timestamp: Date.now()
        };
        this.lastStateSnapshot.set(stateKey, newSnapshot);
        await this.notifyStateChange(workspaceId, newSnapshot);
        console.log(`🔄 强制刷新工作区 ${workspaceId} 用户标签页状态完成`);
      }
    } catch (error) {
      console.warn(`强制刷新工作区 ${workspaceId} 状态失败:`, error);
    }
  }
  /**
   * 立即触发状态检查（用于标签页创建/关闭等事件）
   */
  static async triggerImmediateStateCheck() {
    if (!this.isMonitoring) {
      return;
    }
    this.checkUserTabsStateChanges();
    setTimeout(() => {
      this.checkUserTabsStateChanges();
    }, 100);
  }
  /**
   * 获取监控状态
   */
  static getMonitoringStatus() {
    return {
      isMonitoring: this.isMonitoring,
      workspaceCount: this.lastStateSnapshot.size
    };
  }
}
class WorkspaceUserTabsVisibilityManager {
  // 用于存储上次状态摘要，避免重复日志输出
  static lastStateSummary = null;
  /**
   * 获取工作区用户标签页状态
   */
  static async getWorkspaceUserTabsState(workspaceId) {
    try {
      const result = await chrome.storage.local.get([
        `workspaceUserTabsHidden_${workspaceId}`,
        `workspaceHiddenTabIds_${workspaceId}`,
        `workspacePinnedTabIds_${workspaceId}`
      ]);
      const hiddenTabIds = result[`workspaceHiddenTabIds_${workspaceId}`] || [];
      const pinnedTabIds = result[`workspacePinnedTabIds_${workspaceId}`] || [];
      const validHiddenTabIds = [];
      for (const tabId of hiddenTabIds) {
        try {
          await chrome.tabs.get(tabId);
          validHiddenTabIds.push(tabId);
        } catch {
        }
      }
      if (validHiddenTabIds.length !== hiddenTabIds.length) {
        await chrome.storage.local.set({
          [`workspaceHiddenTabIds_${workspaceId}`]: validHiddenTabIds
        });
        if (validHiddenTabIds.length === 0) {
          await chrome.storage.local.set({
            [`workspaceUserTabsHidden_${workspaceId}`]: false
          });
        }
      }
      const mainWindow = await chrome.windows.getCurrent();
      const allTabs = await chrome.tabs.query({ windowId: mainWindow.id });
      if (allTabs.length === 0) {
        return {
          success: true,
          data: {
            isHidden: false,
            hiddenTabIds: [],
            pinnedTabIds: [],
            totalUserTabs: 0,
            visibleUserTabs: 0,
            canContinueHiding: false,
            actionType: "hide"
          }
        };
      }
      const workspaceUserTabs = [];
      if (DebugController.isVerboseMode) ;
      for (const tab of allTabs) {
        if (!tab.id) continue;
        const tabInfo = {
          id: tab.id,
          url: tab.url || "",
          title: tab.title || "",
          favicon: tab.favIconUrl || "",
          isPinned: tab.pinned,
          isActive: tab.active,
          windowId: tab.windowId,
          index: tab.index
        };
        const isUserTab = await UserTabsUtils.isRealUserTab(tabInfo);
        if (!isUserTab) {
          continue;
        }
        const workonaIdResult = await WorkonaTabManager.getWorkonaIdByChromeId(tab.id);
        if (workonaIdResult.success && workonaIdResult.data) {
          const workspaceIdFromMapping = workonaIdResult.data.split("-")[1];
          if (workspaceIdFromMapping === workspaceId) {
            workspaceUserTabs.push(tab);
            if (DebugController.isVerboseMode) ;
          }
        } else {
          console.log(`⚠️ 标签页没有 Workona ID，进行智能归属判断: ${tab.title} (${tab.url})`);
          const shouldBelongToWorkspace = await this.shouldUnmappedTabBelongToWorkspace(tab, workspaceId);
          if (shouldBelongToWorkspace) {
            workspaceUserTabs.push(tab);
            console.log(`✅ 将未分类用户标签页归属到工作区: ${tab.title} (${tab.url})`);
            try {
              const classifyResult = await TabManager.autoClassifyNewTab(tab.id, tab.url || "");
              if (classifyResult.success) {
                console.log(`🔗 成功为未分类标签页创建 Workona ID: ${tab.title}`);
              }
            } catch (error) {
              console.warn(`创建 Workona ID 映射失败: ${tab.title}`, error);
            }
          }
        }
      }
      const currentWindow = await chrome.windows.getCurrent();
      const visibleUserTabs = workspaceUserTabs.filter(
        (tab) => tab.windowId === currentWindow.id && tab.id && !validHiddenTabIds.includes(tab.id)
      );
      const totalUserTabs = visibleUserTabs.length + validHiddenTabIds.length;
      const hasHiddenTabs = validHiddenTabIds.length > 0;
      const hasVisibleTabs = visibleUserTabs.length > 0;
      let actionType;
      let canContinueHiding = false;
      if (hasHiddenTabs && hasVisibleTabs) {
        actionType = "continue_hide";
        canContinueHiding = true;
      } else if (hasHiddenTabs && !hasVisibleTabs) {
        actionType = "show";
        canContinueHiding = false;
      } else {
        actionType = "hide";
        canContinueHiding = false;
      }
      const stateKey = `${workspaceId}_state_summary`;
      const currentSummary = `${totalUserTabs}_${visibleUserTabs.length}_${validHiddenTabIds.length}`;
      const lastSummary = this.lastStateSummary?.get(stateKey);
      if (lastSummary !== currentSummary) {
        console.log(`📊 工作区 ${workspaceId} 用户标签页状态: 总计 ${totalUserTabs} 个，可见 ${visibleUserTabs.length} 个，隐藏 ${validHiddenTabIds.length} 个`);
        if (!this.lastStateSummary) {
          this.lastStateSummary = /* @__PURE__ */ new Map();
        }
        this.lastStateSummary.set(stateKey, currentSummary);
      }
      return {
        success: true,
        data: {
          isHidden: hasHiddenTabs,
          hiddenTabIds: validHiddenTabIds,
          pinnedTabIds,
          totalUserTabs,
          visibleUserTabs: visibleUserTabs.length,
          canContinueHiding,
          actionType
        }
      };
    } catch (error) {
      return {
        success: false,
        error: {
          code: ERROR_CODES.STORAGE_ERROR,
          message: "Failed to get workspace user tabs state",
          details: error
        }
      };
    }
  }
  /**
   * 判断未映射的标签页是否应该归属到指定工作区
   */
  static async shouldUnmappedTabBelongToWorkspace(_tab, workspaceId) {
    try {
      const activeIdResult = await StorageManager.getActiveWorkspaceId();
      if (activeIdResult.success && activeIdResult.data === workspaceId) {
        console.log(`🎯 存储中的活跃工作区精确匹配，归属标签页到工作区 ${workspaceId}`);
        return true;
      }
      const activeWorkspaceResult = await WorkspaceSwitcher.detectActiveWorkspace();
      if (activeWorkspaceResult.success && activeWorkspaceResult.data && activeWorkspaceResult.data.id === workspaceId) {
        console.log(`🎯 检测到的活跃工作区匹配，归属标签页到工作区 ${workspaceId}`);
        return true;
      }
      const currentWindow = await chrome.windows.getCurrent();
      const windowTabs = await chrome.tabs.query({ windowId: currentWindow.id });
      let workspaceTabsCount = 0;
      let totalMappedTabs = 0;
      for (const windowTab of windowTabs) {
        if (windowTab.id) {
          const windowTabWorkonaId = await WorkonaTabManager.getWorkonaIdByChromeId(windowTab.id);
          if (windowTabWorkonaId.success && windowTabWorkonaId.data) {
            totalMappedTabs++;
            const windowTabWorkspaceId = windowTabWorkonaId.data.split("-")[1];
            if (windowTabWorkspaceId === workspaceId) {
              workspaceTabsCount++;
            }
          }
        }
      }
      console.log(`📊 窗口标签页归属分析: 工作区${workspaceId}有${workspaceTabsCount}个标签页，总映射标签页${totalMappedTabs}个`);
      if (workspaceTabsCount > 0 && workspaceTabsCount >= totalMappedTabs / 2) {
        console.log(`✅ 窗口主要属于工作区 ${workspaceId}，归属未映射标签页`);
        return true;
      }
      if (totalMappedTabs === 0) {
        const workspacesResult = await StorageManager.getWorkspaces();
        if (workspacesResult.success && workspacesResult.data && workspacesResult.data.length > 0) {
          const firstWorkspace = workspacesResult.data[0];
          if (firstWorkspace.id === workspaceId) {
            console.log(`🏠 使用默认工作区 ${workspaceId} 归属未映射标签页`);
            return true;
          }
        }
      }
      console.log(`❌ 标签页不应归属到工作区 ${workspaceId}`);
      return false;
    } catch (error) {
      console.warn("判断标签页归属时出错:", error);
      return false;
    }
  }
  /**
   * 设置工作区用户标签页隐藏状态
   */
  static async setWorkspaceUserTabsState(workspaceId, isHidden, hiddenTabIds, pinnedTabIds) {
    try {
      const stateData = {
        [`workspaceUserTabsHidden_${workspaceId}`]: isHidden,
        [`workspaceHiddenTabIds_${workspaceId}`]: hiddenTabIds
      };
      if (pinnedTabIds !== void 0) {
        stateData[`workspacePinnedTabIds_${workspaceId}`] = pinnedTabIds;
      }
      await chrome.storage.local.set(stateData);
      return { success: true };
    } catch (error) {
      return {
        success: false,
        error: {
          code: ERROR_CODES.STORAGE_ERROR,
          message: "Failed to set workspace user tabs state",
          details: error
        }
      };
    }
  }
  /**
   * 切换工作区用户标签页的显示/隐藏状态
   */
  static async toggleWorkspaceUserTabsVisibility(workspace) {
    try {
      console.log(`🔄 切换工作区 "${workspace.name}" 用户标签页的显示状态`);
      const stateResult = await this.getWorkspaceUserTabsState(workspace.id);
      if (!stateResult.success) {
        return {
          success: false,
          error: stateResult.error
        };
      }
      const { actionType } = stateResult.data;
      switch (actionType) {
        case "show":
          const showResult = await this.showWorkspaceUserTabs(workspace.id);
          if (!showResult.success) {
            return {
              success: false,
              error: showResult.error
            };
          }
          console.log(`✅ 工作区用户标签页显示成功，影响 ${showResult.data.length} 个标签页`);
          return {
            success: true,
            data: {
              action: "shown",
              tabIds: showResult.data
            }
          };
        case "continue_hide":
          const continueHideResult = await this.continueHideWorkspaceUserTabs(workspace.id);
          if (!continueHideResult.success) {
            return {
              success: false,
              error: continueHideResult.error
            };
          }
          console.log(`✅ 工作区用户标签页继续隐藏成功，影响 ${continueHideResult.data.length} 个标签页`);
          return {
            success: true,
            data: {
              action: "hidden",
              tabIds: continueHideResult.data
            }
          };
        case "hide":
        default:
          const hideResult = await this.hideWorkspaceUserTabs(workspace.id);
          if (!hideResult.success) {
            return {
              success: false,
              error: hideResult.error
            };
          }
          console.log(`✅ 工作区用户标签页隐藏成功，影响 ${hideResult.data.length} 个标签页`);
          return {
            success: true,
            data: {
              action: "hidden",
              tabIds: hideResult.data
            }
          };
      }
    } catch (error) {
      return {
        success: false,
        error: {
          code: ERROR_CODES.TAB_ERROR,
          message: "Failed to toggle workspace user tabs visibility",
          details: error
        }
      };
    }
  }
  /**
   * 检查当前窗口是否存在工作区核心标签页或系统标签页
   *
   * 标签页分类：
   * - 工作区核心标签页：工作区中用户配置的网站标签页
   * - 系统标签页：扩展内置页面和浏览器系统页面
   * - 用户标签页：除上述两类之外的所有普通网页标签页
   */
  static async hasWorkspaceSpecificTabs(currentWindowId, excludeTabIds = []) {
    try {
      if (DebugController.isDebugMode) ;
      const windowTabs = await chrome.tabs.query({ windowId: currentWindowId });
      for (const tab of windowTabs) {
        if (!tab.id || excludeTabIds.includes(tab.id)) continue;
        const workspacesResult = await StorageManager.getWorkspaces();
        if (workspacesResult.success && workspacesResult.data) {
          for (const workspace of workspacesResult.data) {
            if (workspace.websites) {
              for (const website of workspace.websites) {
                if (tab.url && tab.url.startsWith(website.url)) {
                  if (DebugController.isDebugMode) ;
                  return true;
                }
              }
            }
          }
        }
        if (tab.url && this.isSystemTab(tab.url)) {
          if (DebugController.isDebugMode) ;
          return true;
        }
      }
      if (DebugController.isDebugMode) ;
      return false;
    } catch (error) {
      console.error("检查工作区核心标签页或系统标签页失败:", error);
      return false;
    }
  }
  /**
   * 判断是否为系统标签页
   * 系统标签页包括：扩展内置页面和浏览器系统页面
   */
  static isSystemTab(url) {
    return (
      // 扩展内置页面
      url.startsWith("chrome-extension://") || url.startsWith("moz-extension://") || // 浏览器系统页面
      url.startsWith("chrome://") || url.startsWith("chrome-search://") || url.startsWith("edge://") || url.startsWith("about:") || url.startsWith("moz://") || url.startsWith("resource://") || url.startsWith("view-source:")
    );
  }
  /**
   * 创建系统标签页，确保窗口有足够的标签页内容
   */
  static async createSystemTab(windowId) {
    try {
      console.log(`🆕 为窗口 ${windowId} 创建系统标签页`);
      const systemTab = await chrome.tabs.create({
        windowId,
        url: "chrome://newtab/",
        active: false,
        // 不激活，避免干扰用户
        index: 0
        // 放在第一个位置
      });
      if (systemTab.id) {
        console.log(`✅ 成功创建系统标签页: ${systemTab.id}`);
        return { success: true, data: systemTab.id };
      } else {
        throw new Error("Failed to create system tab");
      }
    } catch (error) {
      console.error("创建系统标签页失败:", error);
      return {
        success: false,
        error: {
          code: ERROR_CODES.TAB_ERROR,
          message: "Failed to create system tab",
          details: error
        }
      };
    }
  }
  /**
   * 隐藏工作区的用户标签页（优化版：支持系统保护标签页）
   */
  static async hideWorkspaceUserTabs(workspaceId) {
    try {
      console.log(`🔒 开始隐藏工作区 ${workspaceId} 的用户标签页`);
      const stateResult = await this.getWorkspaceUserTabsState(workspaceId);
      if (!stateResult.success) {
        return {
          success: false,
          error: stateResult.error
        };
      }
      const currentState = stateResult.data;
      const allTabsResult = await TabManager.getAllTabs();
      if (!allTabsResult.success) {
        return {
          success: false,
          error: allTabsResult.error
        };
      }
      const allTabs = allTabsResult.data;
      const currentWindow = await chrome.windows.getCurrent();
      const workspaceUserTabs = [];
      for (const tab of allTabs) {
        if (tab.windowId !== currentWindow.id) {
          continue;
        }
        const isUserTab = await UserTabsUtils.isRealUserTab(tab);
        if (!isUserTab) {
          continue;
        }
        const workonaIdResult = await WorkonaTabManager.getWorkonaIdByChromeId(tab.id);
        if (workonaIdResult.success && workonaIdResult.data) {
          const workspaceIdFromMapping = workonaIdResult.data.split("-")[1];
          if (workspaceIdFromMapping === workspaceId && !currentState.hiddenTabIds.includes(tab.id)) {
            workspaceUserTabs.push(tab);
          }
        } else {
          if (!currentState.hiddenTabIds.includes(tab.id)) {
            workspaceUserTabs.push(tab);
            console.log(`✅ 将未分类用户标签页包含在隐藏操作中: ${tab.title} (${tab.url})`);
          }
        }
      }
      if (workspaceUserTabs.length === 0) {
        console.log(`⚠️ 工作区 ${workspaceId} 没有用户标签页需要隐藏`);
        return { success: true, data: [] };
      }
      console.log(`📤 准备隐藏工作区 ${workspaceId} 的 ${workspaceUserTabs.length} 个用户标签页`);
      const pinnedTabIds = [];
      for (const tab of workspaceUserTabs) {
        const workonaIdResult = await WorkonaTabManager.getWorkonaIdByChromeId(tab.id);
        let shouldRecordAsPinned = tab.isPinned;
        if (workonaIdResult.success && workonaIdResult.data) {
          const metadataResult = await WorkonaTabManager.getTabMetadata(workonaIdResult.data);
          if (metadataResult.success && metadataResult.data?.metadata?.isPinned !== void 0) {
            shouldRecordAsPinned = metadataResult.data.metadata.isPinned;
          }
        }
        if (shouldRecordAsPinned) {
          pinnedTabIds.push(tab.id);
          console.log(`📌 记录固定标签页: ${tab.title} (${tab.id}) - 基于${workonaIdResult.success ? "Workona元数据" : "实际状态"}`);
        }
      }
      const tabIds = workspaceUserTabs.map((tab) => tab.id).filter((id) => id && typeof id === "number" && id > 0);
      const hasWorkspaceSpecific = await this.hasWorkspaceSpecificTabs(currentWindow.id, tabIds);
      if (!hasWorkspaceSpecific) {
        console.log(`🆕 隐藏用户标签页后窗口将没有工作区核心标签页或系统标签页，创建系统标签页`);
        const systemTabResult = await this.createSystemTab(currentWindow.id);
        if (!systemTabResult.success) {
          console.warn("创建系统标签页失败，继续执行隐藏操作");
        }
      }
      if (tabIds.length === 0) {
        console.warn(`⚠️ 工作区 ${workspaceId} 没有有效的标签页ID可以隐藏`);
        return { success: true, data: [] };
      }
      const moveResult = await WindowManager.moveTabsToWorkspaceWindow(
        tabIds,
        `workspace-${workspaceId}-hidden-tabs`,
        `工作区 ${workspaceId} - 隐藏的用户标签页`
      );
      if (!moveResult.success) {
        console.error(`❌ 移动工作区 ${workspaceId} 用户标签页到隐藏窗口失败:`, moveResult.error);
        return {
          success: false,
          error: moveResult.error
        };
      }
      const newHiddenTabIds = [...currentState.hiddenTabIds, ...tabIds];
      const existingPinnedTabIds = currentState.pinnedTabIds || [];
      const allPinnedTabIds = [...existingPinnedTabIds, ...pinnedTabIds];
      await this.setWorkspaceUserTabsState(workspaceId, true, newHiddenTabIds, allPinnedTabIds);
      UserTabsRealTimeMonitor.triggerImmediateStateCheck();
      console.log(`✅ 成功隐藏工作区 ${workspaceId} 的 ${tabIds.length} 个用户标签页`);
      return { success: true, data: tabIds };
    } catch (error) {
      console.error(`❌ 隐藏工作区 ${workspaceId} 用户标签页失败:`, error);
      return {
        success: false,
        error: {
          code: ERROR_CODES.TAB_ERROR,
          message: "Failed to hide workspace user tabs",
          details: error
        }
      };
    }
  }
  /**
   * 继续隐藏工作区的可见用户标签页
   */
  static async continueHideWorkspaceUserTabs(workspaceId) {
    try {
      console.log(`🔒 继续隐藏工作区 ${workspaceId} 的可见用户标签页`);
      const stateResult = await this.getWorkspaceUserTabsState(workspaceId);
      if (!stateResult.success) {
        return {
          success: false,
          error: stateResult.error
        };
      }
      const currentState = stateResult.data;
      const allTabsResult = await TabManager.getAllTabs();
      if (!allTabsResult.success) {
        return {
          success: false,
          error: allTabsResult.error
        };
      }
      const allTabs = allTabsResult.data;
      const currentWindow = await chrome.windows.getCurrent();
      const visibleWorkspaceUserTabs = [];
      for (const tab of allTabs) {
        if (tab.windowId !== currentWindow.id || currentState.hiddenTabIds.includes(tab.id)) {
          continue;
        }
        const isUserTab = await UserTabsUtils.isRealUserTab(tab);
        if (!isUserTab) {
          continue;
        }
        const workonaIdResult = await WorkonaTabManager.getWorkonaIdByChromeId(tab.id);
        if (workonaIdResult.success && workonaIdResult.data) {
          const workspaceIdFromMapping = workonaIdResult.data.split("-")[1];
          if (workspaceIdFromMapping === workspaceId) {
            visibleWorkspaceUserTabs.push(tab);
          }
        } else {
          visibleWorkspaceUserTabs.push(tab);
          console.log(`✅ 将未分类用户标签页包含在继续隐藏操作中: ${tab.title} (${tab.url})`);
        }
      }
      if (visibleWorkspaceUserTabs.length === 0) {
        console.log(`⚠️ 工作区 ${workspaceId} 没有新的可见用户标签页需要隐藏`);
        return { success: true, data: [] };
      }
      console.log(`📤 准备继续隐藏工作区 ${workspaceId} 的 ${visibleWorkspaceUserTabs.length} 个可见用户标签页`);
      const newTabIds = visibleWorkspaceUserTabs.map((tab) => tab.id).filter((id) => id && typeof id === "number" && id > 0);
      const hasWorkspaceSpecificAfterHide = await this.hasWorkspaceSpecificTabs(currentWindow.id, newTabIds);
      if (!hasWorkspaceSpecificAfterHide) {
        console.log(`🆕 继续隐藏后窗口将没有工作区核心标签页或系统标签页，创建系统标签页`);
        const systemTabResult = await this.createSystemTab(currentWindow.id);
        if (!systemTabResult.success) {
          console.warn("创建系统标签页失败，继续执行隐藏操作");
        }
      }
      if (newTabIds.length === 0) {
        console.warn(`⚠️ 工作区 ${workspaceId} 没有有效的标签页ID可以继续隐藏`);
        return { success: true, data: [] };
      }
      const moveResult = await WindowManager.moveTabsToWorkspaceWindow(
        newTabIds,
        `workspace-${workspaceId}-hidden-tabs`,
        `工作区 ${workspaceId} - 隐藏的用户标签页`
      );
      if (!moveResult.success) {
        console.error(`❌ 移动工作区 ${workspaceId} 用户标签页到隐藏窗口失败:`, moveResult.error);
        return {
          success: false,
          error: moveResult.error
        };
      }
      const allHiddenTabIds = [...currentState.hiddenTabIds, ...newTabIds];
      await this.setWorkspaceUserTabsState(workspaceId, true, allHiddenTabIds);
      UserTabsRealTimeMonitor.triggerImmediateStateCheck();
      console.log(`✅ 成功继续隐藏工作区 ${workspaceId} 的 ${newTabIds.length} 个用户标签页`);
      return { success: true, data: newTabIds };
    } catch (error) {
      console.error(`❌ 继续隐藏工作区 ${workspaceId} 用户标签页失败:`, error);
      return {
        success: false,
        error: {
          code: ERROR_CODES.TAB_ERROR,
          message: "Failed to continue hiding workspace user tabs",
          details: error
        }
      };
    }
  }
  /**
   * 继续隐藏新的用户标签页（公开方法）
   */
  static async continueHideNewUserTabs(workspaceId) {
    try {
      console.log(`🔄 继续隐藏工作区 ${workspaceId} 的新用户标签页`);
      const result = await this.continueHideWorkspaceUserTabs(workspaceId);
      if (result.success) {
        return {
          success: true,
          data: {
            action: "continued_hiding",
            newlyHiddenTabIds: result.data
          }
        };
      } else {
        return {
          success: false,
          error: result.error
        };
      }
    } catch (error) {
      console.error(`❌ 继续隐藏工作区 ${workspaceId} 用户标签页失败:`, error);
      return {
        success: false,
        error: {
          code: ERROR_CODES.TAB_ERROR,
          message: "Failed to continue hiding new user tabs",
          details: error
        }
      };
    }
  }
  /**
   * 显示工作区的隐藏用户标签页
   */
  static async showWorkspaceUserTabs(workspaceId) {
    try {
      console.log(`🔓 开始显示工作区 ${workspaceId} 的隐藏用户标签页`);
      const stateResult = await this.getWorkspaceUserTabsState(workspaceId);
      if (!stateResult.success) {
        return {
          success: false,
          error: stateResult.error
        };
      }
      const { isHidden, hiddenTabIds, pinnedTabIds } = stateResult.data;
      if (!isHidden || hiddenTabIds.length === 0) {
        console.log(`⚠️ 工作区 ${workspaceId} 没有隐藏的用户标签页需要显示`);
        return { success: true, data: [] };
      }
      console.log(`📥 准备显示工作区 ${workspaceId} 的 ${hiddenTabIds.length} 个隐藏用户标签页`);
      const existingTabIds = [];
      for (const tabId of hiddenTabIds) {
        try {
          await chrome.tabs.get(tabId);
          existingTabIds.push(tabId);
        } catch {
          console.log(`⚠️ 工作区 ${workspaceId} 的标签页 ${tabId} 已不存在，将从隐藏列表中移除`);
        }
      }
      if (existingTabIds.length === 0) {
        console.log(`⚠️ 工作区 ${workspaceId} 所有隐藏的标签页都已不存在`);
        await this.setWorkspaceUserTabsState(workspaceId, false, []);
        return { success: true, data: [] };
      }
      const currentWindow = await chrome.windows.getCurrent();
      try {
        console.log(`📥 移动 ${existingTabIds.length} 个隐藏标签页回到主窗口 ${currentWindow.id}`);
        await chrome.tabs.move(existingTabIds, {
          windowId: currentWindow.id,
          index: -1
          // 移动到窗口末尾
        });
        for (const tabId of existingTabIds) {
          try {
            const workonaIdResult = await WorkonaTabManager.getWorkonaIdByChromeId(tabId);
            let shouldRestorePinned = pinnedTabIds.includes(tabId);
            if (workonaIdResult.success && workonaIdResult.data) {
              const metadataResult = await WorkonaTabManager.getTabMetadata(workonaIdResult.data);
              if (metadataResult.success && metadataResult.data?.metadata?.isPinned !== void 0) {
                shouldRestorePinned = metadataResult.data.metadata.isPinned;
              }
            }
            if (shouldRestorePinned) {
              await chrome.tabs.update(tabId, { pinned: true });
              console.log(`📌 恢复固定状态: 标签页 ${tabId} - 基于${workonaIdResult.success ? "Workona元数据" : "存储记录"}`);
            } else {
              const currentTab = await chrome.tabs.get(tabId);
              if (currentTab.pinned) {
                await chrome.tabs.update(tabId, { pinned: false });
                console.log(`📌 取消错误的固定状态: 标签页 ${tabId}`);
              }
            }
          } catch (error) {
            console.warn(`⚠️ 处理标签页 ${tabId} 固定状态失败:`, error);
          }
        }
        await this.setWorkspaceUserTabsState(workspaceId, false, []);
        UserTabsRealTimeMonitor.triggerImmediateStateCheck();
        console.log(`✅ 成功显示工作区 ${workspaceId} 的 ${existingTabIds.length} 个隐藏用户标签页`);
        return { success: true, data: existingTabIds };
      } catch (moveError) {
        console.error(`❌ 移动工作区 ${workspaceId} 隐藏标签页回主窗口失败:`, moveError);
        return {
          success: false,
          error: {
            code: ERROR_CODES.TAB_ERROR,
            message: "Failed to move tabs back to main window",
            details: moveError
          }
        };
      }
    } catch (error) {
      console.error(`❌ 显示工作区 ${workspaceId} 用户标签页失败:`, error);
      return {
        success: false,
        error: {
          code: ERROR_CODES.TAB_ERROR,
          message: "Failed to show workspace user tabs",
          details: error
        }
      };
    }
  }
}

const tabs = /*#__PURE__*/Object.freeze(/*#__PURE__*/Object.defineProperty({
  __proto__: null,
  TabManager,
  UserTabsRealTimeMonitor,
  UserTabsUtils,
  WorkspaceTabContentMatcher,
  WorkspaceUserTabsVisibilityManager
}, Symbol.toStringTag, { value: 'Module' }));

class WorkonaTabManager {
  /**
   * 生成 Workona 风格标签页ID
   * 格式：t-{workspaceId}-{uuid}
   */
  static generateWorkonaTabId(workspaceId) {
    const uuid = "xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx".replace(/[xy]/g, function(c) {
      const r = Math.random() * 16 | 0;
      const v = c === "x" ? r : r & 3 | 8;
      return v.toString(16);
    });
    return `t-${workspaceId}-${uuid}`;
  }
  /**
   * 创建标签页ID映射关系（增强版：支持元数据分类）
   */
  static async createTabIdMapping(workonaId, chromeId, workspaceId, websiteId, options) {
    try {
      const isWorkspaceCore = options?.isWorkspaceCore ?? !!websiteId;
      const tabType = isWorkspaceCore ? "core" : "session";
      const source = options?.source ?? (websiteId ? "workspace_website" : "user_opened");
      const mapping = {
        workonaId,
        chromeId,
        workspaceId,
        websiteId,
        createdAt: Date.now(),
        lastSyncAt: Date.now(),
        // 概念性重构：标签页分类元数据
        isWorkspaceCore,
        tabType,
        metadata: {
          source,
          sessionId: options?.sessionId,
          originalUrl: options?.originalUrl,
          addedToWorkspaceAt: isWorkspaceCore ? Date.now() : void 0,
          isPinned: false,
          // 初始化为未固定，后续会根据实际状态更新
          pinnedAt: void 0,
          unpinnedAt: void 0
        }
      };
      const mappingsResult = await StorageManager.getTabIdMappings();
      if (!mappingsResult.success) {
        return { success: false, error: mappingsResult.error };
      }
      const mappings = mappingsResult.data;
      const existingIndex = mappings.findIndex(
        (m) => m.workonaId === workonaId || m.chromeId === chromeId
      );
      if (existingIndex >= 0) {
        const existing = mappings[existingIndex];
        mappings[existingIndex] = {
          ...mapping,
          createdAt: existing.createdAt,
          // 保留原始创建时间
          metadata: {
            ...existing.metadata,
            ...mapping.metadata,
            source: mapping.metadata?.source || existing.metadata?.source || "user_opened",
            addedToWorkspaceAt: existing.metadata?.addedToWorkspaceAt || mapping.metadata?.addedToWorkspaceAt
          }
        };
      } else {
        mappings.push(mapping);
      }
      const saveResult = await StorageManager.saveTabIdMappings(mappings);
      if (!saveResult.success) {
        return { success: false, error: saveResult.error };
      }
      try {
        const tab = await chrome.tabs.get(chromeId);
        const isPinned = tab.pinned;
        await chrome.scripting.executeScript({
          target: { tabId: chromeId },
          func: (workonaId2, workspaceId2, websiteId2, isWorkspaceCore2, isPinned2) => {
            const workonaData = {
              workonaId: workonaId2,
              workspaceId: workspaceId2,
              websiteId: websiteId2,
              isWorkspaceCore: isWorkspaceCore2,
              isPinned: isPinned2,
              timestamp: Date.now()
            };
            sessionStorage.setItem("workonaData", JSON.stringify(workonaData));
            console.log(`📝 标签页会话存储 Workona 数据:`, workonaData);
          },
          args: [workonaId, workspaceId, websiteId || "", isWorkspaceCore, isPinned]
        });
      } catch (error) {
        console.warn(`⚠️ 无法为标签页 ${chromeId} 设置会话存储:`, error);
      }
      console.log(`✅ 创建标签页ID映射: ${workonaId} <-> ${chromeId} (类型: ${tabType}, 核心: ${isWorkspaceCore})`);
      return { success: true, data: mapping };
    } catch (error) {
      return {
        success: false,
        error: {
          code: ERROR_CODES.TAB_ERROR,
          message: "Failed to create tab ID mapping",
          details: error
        }
      };
    }
  }
  /**
   * 根据 Chrome ID 获取 Workona ID
   */
  static async getWorkonaIdByChromeId(chromeId) {
    try {
      const mappingsResult = await StorageManager.getTabIdMappings();
      if (!mappingsResult.success) {
        return { success: false, error: mappingsResult.error };
      }
      const mappings = mappingsResult.data;
      const mapping = mappings.find((m) => m.chromeId === chromeId);
      return { success: true, data: mapping?.workonaId || null };
    } catch (error) {
      return {
        success: false,
        error: {
          code: ERROR_CODES.TAB_ERROR,
          message: "Failed to get Workona ID by Chrome ID",
          details: error
        }
      };
    }
  }
  /**
   * 根据 Workona ID 获取 Chrome ID
   */
  static async getChromeIdByWorkonaId(workonaId) {
    try {
      const mappingsResult = await StorageManager.getTabIdMappings();
      if (!mappingsResult.success) {
        return { success: false, error: mappingsResult.error };
      }
      const mappings = mappingsResult.data;
      const mapping = mappings.find((m) => m.workonaId === workonaId);
      return { success: true, data: mapping?.chromeId || null };
    } catch (error) {
      return {
        success: false,
        error: {
          code: ERROR_CODES.TAB_ERROR,
          message: "Failed to get Chrome ID by Workona ID",
          details: error
        }
      };
    }
  }
  /**
   * 同步标签页映射关系
   * 清理无效的映射，更新现有映射的同步时间
   */
  static async syncTabMappings() {
    try {
      console.log("🔄 开始同步标签页映射关系...");
      const mappingsResult = await StorageManager.getTabIdMappings();
      if (!mappingsResult.success) {
        return { success: false, error: mappingsResult.error };
      }
      const mappings = mappingsResult.data;
      const validMappings = [];
      let cleanedCount = 0;
      const allTabs = await chrome.tabs.query({});
      const existingChromeIds = new Set(allTabs.map((tab) => tab.id));
      for (const mapping of mappings) {
        if (existingChromeIds.has(mapping.chromeId)) {
          mapping.lastSyncAt = Date.now();
          validMappings.push(mapping);
        } else {
          console.log(`🗑️ 清理无效映射: ${mapping.workonaId} <-> ${mapping.chromeId}`);
          cleanedCount++;
        }
      }
      const saveResult = await StorageManager.saveTabIdMappings(validMappings);
      if (!saveResult.success) {
        return { success: false, error: saveResult.error };
      }
      console.log(`✅ 标签页映射同步完成，清理了 ${cleanedCount} 个无效映射`);
      return { success: true, data: cleanedCount };
    } catch (error) {
      return {
        success: false,
        error: {
          code: ERROR_CODES.TAB_ERROR,
          message: "Failed to sync tab mappings",
          details: error
        }
      };
    }
  }
  /**
   * 删除标签页映射
   */
  static async removeTabMapping(workonaId) {
    try {
      const mappingsResult = await StorageManager.getTabIdMappings();
      if (!mappingsResult.success) {
        return { success: false, error: mappingsResult.error };
      }
      const mappings = mappingsResult.data;
      const filteredMappings = mappings.filter((m) => m.workonaId !== workonaId);
      const saveResult = await StorageManager.saveTabIdMappings(filteredMappings);
      if (!saveResult.success) {
        return { success: false, error: saveResult.error };
      }
      console.log(`🗑️ 删除标签页映射: ${workonaId}`);
      return { success: true };
    } catch (error) {
      return {
        success: false,
        error: {
          code: ERROR_CODES.TAB_ERROR,
          message: "Failed to remove tab mapping",
          details: error
        }
      };
    }
  }
  /**
   * 获取工作区的所有 Workona 标签页ID
   */
  static async getWorkspaceWorkonaTabIds(workspaceId) {
    try {
      const mappingsResult = await StorageManager.getTabIdMappings();
      if (!mappingsResult.success) {
        return { success: false, error: mappingsResult.error };
      }
      const mappings = mappingsResult.data;
      const workonaIds = mappings.filter((m) => m.workspaceId === workspaceId).map((m) => m.workonaId);
      return { success: true, data: workonaIds };
    } catch (error) {
      return {
        success: false,
        error: {
          code: ERROR_CODES.TAB_ERROR,
          message: "Failed to get workspace Workona tab IDs",
          details: error
        }
      };
    }
  }
  /**
   * 批量清理工作区的标签页映射
   */
  static async clearWorkspaceTabMappings(workspaceId) {
    try {
      const mappingsResult = await StorageManager.getTabIdMappings();
      if (!mappingsResult.success) {
        return { success: false, error: mappingsResult.error };
      }
      const mappings = mappingsResult.data;
      const remainingMappings = mappings.filter((m) => m.workspaceId !== workspaceId);
      const clearedCount = mappings.length - remainingMappings.length;
      const saveResult = await StorageManager.saveTabIdMappings(remainingMappings);
      if (!saveResult.success) {
        return { success: false, error: saveResult.error };
      }
      console.log(`🗑️ 清理工作区 ${workspaceId} 的 ${clearedCount} 个标签页映射`);
      return { success: true, data: clearedCount };
    } catch (error) {
      return {
        success: false,
        error: {
          code: ERROR_CODES.TAB_ERROR,
          message: "Failed to clear workspace tab mappings",
          details: error
        }
      };
    }
  }
  // === 概念性重构：标签页元数据管理方法 ===
  /**
   * 获取标签页元数据
   */
  static async getTabMetadata(workonaId) {
    try {
      const mappingsResult = await StorageManager.getTabIdMappings();
      if (!mappingsResult.success) {
        return { success: false, error: mappingsResult.error };
      }
      const mappings = mappingsResult.data;
      const mapping = mappings.find((m) => m.workonaId === workonaId);
      return { success: true, data: mapping || null };
    } catch (error) {
      return {
        success: false,
        error: {
          code: ERROR_CODES.TAB_ERROR,
          message: "Failed to get tab metadata",
          details: error
        }
      };
    }
  }
  /**
   * 更新标签页元数据
   */
  static async updateTabMetadata(workonaId, updates) {
    try {
      const mappingsResult = await StorageManager.getTabIdMappings();
      if (!mappingsResult.success) {
        return { success: false, error: mappingsResult.error };
      }
      const mappings = mappingsResult.data;
      const mappingIndex = mappings.findIndex((m) => m.workonaId === workonaId);
      if (mappingIndex === -1) {
        return {
          success: false,
          error: {
            code: ERROR_CODES.TAB_ERROR,
            message: "Tab mapping not found"
          }
        };
      }
      const existingMapping = mappings[mappingIndex];
      const updatedMapping = {
        ...existingMapping,
        ...updates,
        lastSyncAt: Date.now(),
        metadata: {
          ...existingMapping.metadata,
          ...updates.metadata,
          // 确保 source 字段始终有值
          source: updates.metadata?.source || existingMapping.metadata?.source || "user_opened"
        }
      };
      mappings[mappingIndex] = updatedMapping;
      const saveResult = await StorageManager.saveTabIdMappings(mappings);
      if (!saveResult.success) {
        return { success: false, error: saveResult.error };
      }
      console.log(`📝 更新标签页元数据: ${workonaId} (核心: ${updatedMapping.isWorkspaceCore})`);
      return { success: true, data: updatedMapping };
    } catch (error) {
      return {
        success: false,
        error: {
          code: ERROR_CODES.TAB_ERROR,
          message: "Failed to update tab metadata",
          details: error
        }
      };
    }
  }
  /**
   * 将会话临时标签页转换为工作区核心标签页
   */
  static async promoteToWorkspaceCore(workonaId, websiteId) {
    try {
      const updates = {
        isWorkspaceCore: true,
        tabType: "core",
        websiteId,
        metadata: {
          source: "workspace_website",
          addedToWorkspaceAt: Date.now()
        }
      };
      const result = await this.updateTabMetadata(workonaId, updates);
      if (result.success) {
        console.log(`⬆️ 标签页提升为工作区核心: ${workonaId}`);
        try {
          const workspaceId = workonaId.split("-")[1];
          await UserTabsRealTimeMonitor.triggerImmediateStateCheck();
          await UserTabsRealTimeMonitor.forceRefreshWorkspaceState(workspaceId);
          console.log(`🔄 已触发工作区 ${workspaceId} 用户标签页状态更新 (标签页提升)`);
        } catch (updateError) {
          console.warn("触发用户标签页状态更新失败:", updateError);
        }
      }
      return result;
    } catch (error) {
      return {
        success: false,
        error: {
          code: ERROR_CODES.TAB_ERROR,
          message: "Failed to promote tab to workspace core",
          details: error
        }
      };
    }
  }
  /**
   * 将工作区核心标签页降级为会话临时标签页
   * 当用户从工作区中移除某个工作区专属标签页时调用
   */
  static async demoteToSessionTab(workonaId) {
    try {
      const metadataResult = await this.getTabMetadata(workonaId);
      if (!metadataResult.success || !metadataResult.data) {
        return {
          success: false,
          error: {
            code: ERROR_CODES.TAB_ERROR,
            message: "Tab metadata not found for demotion"
          }
        };
      }
      const currentMetadata = metadataResult.data;
      if (!currentMetadata.isWorkspaceCore) {
        console.log(`⚠️ 标签页 ${workonaId} 已经是会话临时标签页，无需降级`);
        return { success: true, data: currentMetadata };
      }
      const updates = {
        isWorkspaceCore: false,
        tabType: "session",
        websiteId: void 0,
        // 移除与工作区网站的关联
        metadata: {
          ...currentMetadata.metadata,
          source: "user_opened",
          addedToWorkspaceAt: void 0,
          // 移除工作区添加时间
          demotedAt: Date.now(),
          // 记录降级时间
          originalWebsiteId: currentMetadata.websiteId
          // 保留原始网站ID用于追踪
        }
      };
      const result = await this.updateTabMetadata(workonaId, updates);
      if (result.success) {
        console.log(`⬇️ 标签页降级为会话临时标签页: ${workonaId} (原网站ID: ${currentMetadata.websiteId})`);
        try {
          const workspaceId = workonaId.split("-")[1];
          await UserTabsRealTimeMonitor.triggerImmediateStateCheck();
          await UserTabsRealTimeMonitor.forceRefreshWorkspaceState(workspaceId);
          console.log(`🔄 已触发工作区 ${workspaceId} 用户标签页状态更新 (标签页降级)`);
        } catch (updateError) {
          console.warn("触发用户标签页状态更新失败:", updateError);
        }
      }
      return result;
    } catch (error) {
      return {
        success: false,
        error: {
          code: ERROR_CODES.TAB_ERROR,
          message: "Failed to demote tab to session tab",
          details: error
        }
      };
    }
  }
  /**
   * 同步标签页编辑后的状态
   * 确保编辑标签页后不会破坏 Workona ID 映射关系
   */
  static async syncTabAfterEdit(chromeId, newUrl, newTitle) {
    try {
      const workonaIdResult = await this.getWorkonaIdByChromeId(chromeId);
      if (!workonaIdResult.success || !workonaIdResult.data) {
        return { success: true };
      }
      const workonaId = workonaIdResult.data;
      const metadataResult = await this.getTabMetadata(workonaId);
      if (!metadataResult.success || !metadataResult.data) {
        console.warn(`无法获取标签页元数据: ${workonaId}`);
        return { success: true };
      }
      const currentMetadata = metadataResult.data;
      if (newUrl && newUrl !== currentMetadata.metadata?.originalUrl) {
        const updates = {
          metadata: {
            ...currentMetadata.metadata,
            source: currentMetadata.metadata?.source || "user_opened",
            originalUrl: newUrl,
            // 记录编辑时间
            lastEditedAt: Date.now()
          }
        };
        const updateResult = await this.updateTabMetadata(workonaId, updates);
        if (updateResult.success) {
          console.log(`🔄 同步标签页编辑: ${workonaId} (新URL: ${newUrl})`);
        }
      }
      const currentSession = WorkspaceSessionManager.getCurrentSession();
      if (currentSession && currentSession.tabs[workonaId]) {
        const updatedTab = {
          ...currentSession.tabs[workonaId],
          url: newUrl || currentSession.tabs[workonaId].url,
          title: newTitle || currentSession.tabs[workonaId].title,
          lastUpdated: Date.now()
        };
        await WorkspaceSessionManager.updateSessionTab(workonaId, updatedTab);
        console.log(`📝 同步标签页到会话: ${workonaId}`);
      }
      return { success: true };
    } catch (error) {
      return {
        success: false,
        error: {
          code: ERROR_CODES.TAB_ERROR,
          message: "Failed to sync tab after edit",
          details: error
        }
      };
    }
  }
  /**
   * 检查标签页是否为工作区核心标签页
   */
  static async isWorkspaceCore(chromeId) {
    try {
      const workonaIdResult = await this.getWorkonaIdByChromeId(chromeId);
      if (!workonaIdResult.success || !workonaIdResult.data) {
        return { success: true, data: false };
      }
      const metadataResult = await this.getTabMetadata(workonaIdResult.data);
      if (!metadataResult.success || !metadataResult.data) {
        return { success: true, data: false };
      }
      return { success: true, data: metadataResult.data.isWorkspaceCore };
    } catch (error) {
      return {
        success: false,
        error: {
          code: ERROR_CODES.TAB_ERROR,
          message: "Failed to check if tab is workspace core",
          details: error
        }
      };
    }
  }
  /**
   * 获取工作区的所有核心标签页 Workona ID
   */
  static async getWorkspaceCoreTabIds(workspaceId) {
    try {
      const mappingsResult = await StorageManager.getTabIdMappings();
      if (!mappingsResult.success) {
        return { success: false, error: mappingsResult.error };
      }
      const mappings = mappingsResult.data;
      const coreTabIds = mappings.filter((m) => m.workspaceId === workspaceId && m.isWorkspaceCore).map((m) => m.workonaId);
      return { success: true, data: coreTabIds };
    } catch (error) {
      return {
        success: false,
        error: {
          code: ERROR_CODES.TAB_ERROR,
          message: "Failed to get workspace core tab IDs",
          details: error
        }
      };
    }
  }
  /**
   * 获取工作区的所有会话临时标签页 Workona ID
   */
  static async getWorkspaceSessionTabIds(workspaceId) {
    try {
      const mappingsResult = await StorageManager.getTabIdMappings();
      if (!mappingsResult.success) {
        return { success: false, error: mappingsResult.error };
      }
      const mappings = mappingsResult.data;
      const sessionTabIds = mappings.filter((m) => m.workspaceId === workspaceId && !m.isWorkspaceCore).map((m) => m.workonaId);
      return { success: true, data: sessionTabIds };
    } catch (error) {
      return {
        success: false,
        error: {
          code: ERROR_CODES.TAB_ERROR,
          message: "Failed to get workspace session tab IDs",
          details: error
        }
      };
    }
  }
  /**
   * 检查工作区网站是否有对应的打开标签页
   */
  static async isWebsiteTabOpen(workspaceId, websiteId) {
    try {
      const mappingsResult = await StorageManager.getTabIdMappings();
      if (!mappingsResult.success) {
        return { success: false, error: mappingsResult.error };
      }
      const mappings = mappingsResult.data;
      const mapping = mappings.find(
        (m) => m.workspaceId === workspaceId && m.websiteId === websiteId && m.isWorkspaceCore
        // 只检查工作区核心标签页
      );
      if (!mapping) {
        return {
          success: true,
          data: { isOpen: false }
        };
      }
      try {
        const tab = await chrome.tabs.get(mapping.chromeId);
        if (tab) {
          return {
            success: true,
            data: {
              isOpen: true,
              chromeId: mapping.chromeId,
              workonaId: mapping.workonaId
            }
          };
        }
      } catch {
        await this.removeTabMapping(mapping.workonaId);
        console.log(`🗑️ 清理无效的标签页映射: ${mapping.workonaId}`);
      }
      return {
        success: true,
        data: { isOpen: false }
      };
    } catch (error) {
      return {
        success: false,
        error: {
          code: ERROR_CODES.TAB_ERROR,
          message: "Failed to check if website tab is open",
          details: error
        }
      };
    }
  }
  /**
   * 获取工作区所有网站的标签页状态
   */
  static async getWorkspaceWebsiteTabStates(workspaceId, websiteIds) {
    try {
      const result = {};
      for (const websiteId of websiteIds) {
        const statusResult = await this.isWebsiteTabOpen(workspaceId, websiteId);
        if (statusResult.success) {
          result[websiteId] = statusResult.data;
        } else {
          result[websiteId] = { isOpen: false };
        }
      }
      return { success: true, data: result };
    } catch (error) {
      return {
        success: false,
        error: {
          code: ERROR_CODES.TAB_ERROR,
          message: "Failed to get workspace website tab states",
          details: error
        }
      };
    }
  }
}

class WindowManager {
  static globalWorkspaceWindowId = null;
  // 全局专用窗口ID
  static GLOBAL_WORKSPACE_WINDOW_KEY = "global_workspace_window";
  static isCreatingWindow = false;
  // 防止并发创建窗口
  /**
   * 从存储中恢复全局窗口ID
   */
  static async loadGlobalWindowId() {
    try {
      const result = await chrome.storage.local.get([this.GLOBAL_WORKSPACE_WINDOW_KEY]);
      if (result[this.GLOBAL_WORKSPACE_WINDOW_KEY]) {
        this.globalWorkspaceWindowId = result[this.GLOBAL_WORKSPACE_WINDOW_KEY];
      }
    } catch (error) {
      console.warn("加载全局窗口ID失败:", error);
    }
  }
  /**
   * 保存全局窗口ID到存储
   */
  static async saveGlobalWindowId(windowId) {
    try {
      await chrome.storage.local.set({
        [this.GLOBAL_WORKSPACE_WINDOW_KEY]: windowId
      });
      this.globalWorkspaceWindowId = windowId;
    } catch (error) {
      console.warn("保存全局窗口ID失败:", error);
    }
  }
  /**
   * 清理全局窗口ID
   */
  static async clearGlobalWindowId() {
    try {
      await chrome.storage.local.remove([this.GLOBAL_WORKSPACE_WINDOW_KEY]);
      this.globalWorkspaceWindowId = null;
    } catch (error) {
      console.warn("清理全局窗口ID失败:", error);
    }
  }
  /**
   * 获取或创建全局专用窗口（单例模式）
   */
  static async getOrCreateGlobalWorkspaceWindow() {
    try {
      console.log("🪟 获取或创建全局工作区专用窗口");
      if (this.isCreatingWindow) {
        console.log("⏳ 正在创建窗口中，等待完成...");
        await new Promise((resolve) => setTimeout(resolve, 1e3));
        return await this.getOrCreateGlobalWorkspaceWindow();
      }
      if (!this.globalWorkspaceWindowId) {
        await this.loadGlobalWindowId();
      }
      if (this.globalWorkspaceWindowId) {
        try {
          const window = await chrome.windows.get(this.globalWorkspaceWindowId);
          if (window) {
            console.log(`✅ 全局工作区专用窗口已存在: ${this.globalWorkspaceWindowId}`);
            return {
              success: true,
              data: {
                id: this.globalWorkspaceWindowId,
                workspaceId: "global",
                workspaceName: "全局工作区专用窗口",
                tabCount: window.tabs?.length || 0,
                isVisible: window.state !== "minimized"
              }
            };
          }
        } catch {
          console.log("🗑️ 全局专用窗口已不存在，需要重新创建");
          await this.clearGlobalWindowId();
        }
      }
      this.isCreatingWindow = true;
      try {
        console.log("🔨 创建新的全局工作区专用窗口");
        const window = await chrome.windows.create({
          type: "normal",
          state: "normal",
          focused: false,
          // 不获取焦点
          width: 1200,
          height: 800,
          left: 100,
          top: 100,
          url: chrome.runtime.getURL("workspace-placeholder.html") + "?workspaceId=global&workspaceName=" + encodeURIComponent("全局工作区专用窗口")
        });
        if (!window.id) {
          throw new Error("Failed to create global workspace window");
        }
        await this.saveGlobalWindowId(window.id);
        try {
          await chrome.windows.update(window.id, { state: "minimized" });
        } catch (error) {
          console.warn("最小化窗口失败，但窗口创建成功:", error);
        }
      } finally {
        this.isCreatingWindow = false;
      }
      console.log(`✅ 成功创建全局工作区专用窗口 -> 窗口ID ${this.globalWorkspaceWindowId}`);
      return {
        success: true,
        data: {
          id: this.globalWorkspaceWindowId,
          workspaceId: "global",
          workspaceName: "全局工作区专用窗口",
          tabCount: 1,
          // 新创建的窗口包含一个占位符标签页
          isVisible: false
          // 窗口默认最小化，所以不可见
        }
      };
    } catch (error) {
      console.error(`创建全局工作区专用窗口失败`, error);
      return {
        success: false,
        error: {
          code: ERROR_CODES.WINDOW_ERROR,
          message: "Failed to create global workspace window",
          details: error
        }
      };
    }
  }
  /**
   * 为工作区创建专用窗口（保持向后兼容）
   * 现在所有工作区都使用同一个全局专用窗口
   */
  static async createWorkspaceWindow(_workspaceId, _workspaceName) {
    return await this.getOrCreateGlobalWorkspaceWindow();
  }
  /**
   * 获取工作区的专用窗口ID（现在所有工作区共享同一个窗口）
   */
  static getWorkspaceWindowId(_workspaceId) {
    return this.globalWorkspaceWindowId || void 0;
  }
  /**
   * 获取窗口对应的工作区ID（现在返回全局标识）
   */
  static getWindowWorkspaceId(windowId) {
    return windowId === this.globalWorkspaceWindowId ? "global" : void 0;
  }
  /**
   * 获取全局专用窗口ID
   */
  static getGlobalWorkspaceWindowId() {
    return this.globalWorkspaceWindowId || void 0;
  }
  /**
   * 将标签页移动到全局专用窗口
   */
  static async moveTabsToWorkspaceWindow(tabIds, workspaceId, workspaceName) {
    try {
      if (tabIds.length === 0) {
        return { success: true };
      }
      console.log(`🔄 开始移动 ${tabIds.length} 个标签页到全局专用窗口（来自工作区: ${workspaceName}）`, {
        tabIds,
        workspaceId,
        timestamp: (/* @__PURE__ */ new Date()).toISOString()
      });
      const windowResult = await this.getOrCreateGlobalWorkspaceWindow();
      if (!windowResult.success) {
        return { success: false, error: windowResult.error };
      }
      const windowId = windowResult.data.id;
      console.log(`🔍 开始验证 ${tabIds.length} 个标签页的有效性...`);
      const validTabIds = [];
      for (const tabId of tabIds) {
        try {
          const tab = await chrome.tabs.get(tabId);
          validTabIds.push(tabId);
          console.log(`📋 验证标签页 ${tabId}: "${tab.title}" (${tab.url}) - 有效`);
        } catch (error) {
          console.warn(`⚠️ 标签页 ${tabId} 不存在或无法访问，跳过移动:`, error);
        }
      }
      if (validTabIds.length === 0) {
        console.log(`⚠️ 没有有效的标签页需要移动`);
        return { success: true };
      }
      try {
        if (validTabIds.length === 0) {
          console.log(`⚠️ 验证后发现没有有效的标签页需要移动`);
          return { success: true };
        }
        const safeTabIds = validTabIds.filter((id) => id && typeof id === "number" && id > 0);
        if (safeTabIds.length === 0) {
          console.log(`⚠️ 过滤后没有安全的标签页ID可以移动`);
          return { success: true };
        }
        if (safeTabIds.length !== validTabIds.length) {
          console.warn(`⚠️ 发现 ${validTabIds.length - safeTabIds.length} 个无效的标签页ID，已过滤`);
        }
        await chrome.tabs.move(safeTabIds, {
          windowId,
          index: -1
          // 移动到窗口末尾
        });
        console.log(`✅ 成功移动 ${safeTabIds.length} 个标签页到专用窗口 ${windowId}`);
      } catch (moveError) {
        console.error(`❌ 移动标签页到专用窗口失败:`, moveError);
        throw moveError;
      }
      return { success: true };
    } catch (error) {
      console.error(`移动标签页到工作区专用窗口失败:`, error);
      return {
        success: false,
        error: {
          code: ERROR_CODES.TAB_ERROR,
          message: "Failed to move tabs to workspace window",
          details: error
        }
      };
    }
  }
  /**
   * 将特定工作区的标签页从全局专用窗口移动到主窗口
   */
  static async moveTabsFromWorkspaceWindow(workspaceId, targetWindowId) {
    try {
      console.log(`从全局专用窗口移动工作区 ${workspaceId} 的标签页到主窗口`);
      const windowId = this.globalWorkspaceWindowId;
      if (!windowId) {
        console.log(`全局专用窗口不存在`);
        return { success: true, data: [] };
      }
      const workspaceResult = await StorageManager.getWorkspace(workspaceId);
      if (!workspaceResult.success) {
        console.log(`获取工作区 ${workspaceId} 信息失败:`, workspaceResult.error);
        return { success: true, data: [] };
      }
      const workspace = workspaceResult.data;
      const workspaceUrls = workspace.websites.map((w) => w.url);
      const tabs = await chrome.tabs.query({ windowId });
      const workspaceTabs = [];
      console.log(`🔍 检查专用窗口中的 ${tabs.length} 个标签页，识别属于工作区 "${workspace.name}" 的标签页...`);
      for (const tab of tabs) {
        if (tab.url?.includes("workspace-placeholder.html")) {
          console.log(`🚫 跳过占位符页面: ${tab.url}`);
          continue;
        }
        if (!tab.id) {
          console.log(`⚠️ 标签页无ID，跳过: ${tab.url}`);
          continue;
        }
        const workonaIdResult = await WorkonaTabManager.getWorkonaIdByChromeId(tab.id);
        if (workonaIdResult.success && workonaIdResult.data) {
          const workonaId = workonaIdResult.data;
          const workspaceIdFromMapping = workonaId.split("-")[1];
          if (workspaceIdFromMapping === workspaceId) {
            workspaceTabs.push(tab);
            console.log(`✅ 找到工作区标签页: ${tab.title} (${tab.url}) - Workona ID: ${workonaId}`);
          } else {
            console.log(`❌ 标签页属于其他工作区: ${tab.title} - 工作区ID: ${workspaceIdFromMapping}`);
          }
        } else {
          const isWorkspaceTab = workspaceUrls.some((url) => tab.url?.startsWith(url));
          if (isWorkspaceTab) {
            workspaceTabs.push(tab);
            console.log(`✅ 通过URL匹配找到工作区标签页: ${tab.title} (${tab.url})`);
          } else {
            console.log(`❌ 非工作区标签页: ${tab.title} (${tab.url})`);
          }
        }
      }
      console.log(`📊 在全局专用窗口中找到工作区 "${workspace.name}" 的 ${workspaceTabs.length} 个标签页`);
      if (workspaceTabs.length === 0) {
        console.log(`全局专用窗口中没有工作区 "${workspace.name}" 的标签页需要移动`);
        return { success: true, data: [] };
      }
      let targetWindow = targetWindowId;
      if (!targetWindow) {
        const currentWindow = await chrome.windows.getCurrent();
        targetWindow = currentWindow.id;
      }
      console.log(`从全局专用窗口 ${windowId} 移动 ${workspaceTabs.length} 个标签页到窗口 ${targetWindow}`);
      const tabIds = workspaceTabs.map((tab) => tab.id);
      console.log(`🚀 移动 ${tabIds.length} 个标签页到主窗口 ${targetWindow}`);
      await chrome.tabs.move(tabIds, {
        windowId: targetWindow,
        index: -1
      });
      console.log(`✅ 成功移动 ${tabIds.length} 个标签页到主窗口`);
      const tabInfos = workspaceTabs.map((tab) => ({
        id: tab.id,
        url: tab.url || "",
        title: tab.title || "",
        favicon: tab.favIconUrl || "",
        isPinned: tab.pinned,
        isActive: tab.active,
        windowId: targetWindow,
        index: tab.index
      }));
      console.log(`✅ 成功移动 ${workspaceTabs.length} 个标签页到主窗口（包括会话临时标签页）`);
      return { success: true, data: tabInfos };
    } catch (error) {
      console.error(`从全局专用窗口移动标签页失败:`, error);
      return {
        success: false,
        error: {
          code: ERROR_CODES.TAB_ERROR,
          message: "Failed to move tabs from global workspace window",
          details: error
        }
      };
    }
  }
  /**
   * 关闭全局专用窗口
   */
  static async closeWorkspaceWindow(_workspaceId) {
    return await this.closeGlobalWorkspaceWindow();
  }
  /**
   * 关闭全局专用窗口
   */
  static async closeGlobalWorkspaceWindow() {
    try {
      const windowId = this.globalWorkspaceWindowId;
      if (!windowId) {
        console.log(`全局专用窗口不存在，无需关闭`);
        return { success: true };
      }
      console.log(`关闭全局专用窗口: ${windowId}`);
      await this.moveTabsFromWorkspaceWindow("global");
      await chrome.windows.remove(windowId);
      this.globalWorkspaceWindowId = null;
      console.log(`成功关闭全局专用窗口: ${windowId}`);
      return { success: true };
    } catch (error) {
      console.error(`关闭全局专用窗口失败:`, error);
      return {
        success: false,
        error: {
          code: ERROR_CODES.WINDOW_ERROR,
          message: "Failed to close global workspace window",
          details: error
        }
      };
    }
  }
  /**
   * 获取所有工作区专用窗口信息（现在只有一个全局窗口）
   */
  static async getAllWorkspaceWindows() {
    try {
      const windowInfos = [];
      if (this.globalWorkspaceWindowId) {
        try {
          const window = await chrome.windows.get(this.globalWorkspaceWindowId, { populate: true });
          windowInfos.push({
            id: this.globalWorkspaceWindowId,
            workspaceId: "global",
            workspaceName: "全局工作区专用窗口",
            tabCount: window.tabs?.length || 0,
            isVisible: window.state !== "minimized"
          });
        } catch {
          this.globalWorkspaceWindowId = null;
        }
      }
      return { success: true, data: windowInfos };
    } catch (error) {
      return {
        success: false,
        error: {
          code: ERROR_CODES.WINDOW_ERROR,
          message: "Failed to get workspace windows",
          details: error
        }
      };
    }
  }
  /**
   * 更新窗口标题（现在更新全局窗口标题）
   */
  static async updateWindowTitle(_workspaceId, _workspaceName, tabCount) {
    try {
      const windowId = this.globalWorkspaceWindowId;
      if (!windowId) {
        return { success: true };
      }
      const tabs = await chrome.tabs.query({ windowId });
      const placeholderTab = tabs.find(
        (tab) => tab.url?.includes("workspace-placeholder.html")
      );
      if (placeholderTab) {
        const newUrl = chrome.runtime.getURL("workspace-placeholder.html") + `?workspaceId=global&workspaceName=${encodeURIComponent("全局工作区专用窗口")}&tabCount=${tabCount}`;
        await chrome.tabs.update(placeholderTab.id, { url: newUrl });
      }
      return { success: true };
    } catch (error) {
      return {
        success: false,
        error: {
          code: ERROR_CODES.WINDOW_ERROR,
          message: "Failed to update window title",
          details: error
        }
      };
    }
  }
}

class WorkspaceManager {
  /**
   * 生成唯一ID（支持 Workona 风格）
   */
  static generateId(type = "saved") {
    const timestamp = Date.now();
    const randomStr = Math.random().toString(36).substr(2, 9);
    if (type === "temp") {
      return `temp_${timestamp}_${randomStr}`;
    }
    return `ws_${timestamp}_${randomStr}`;
  }
  /**
   * 生成网站ID
   */
  static generateWebsiteId() {
    return `site_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }
  /**
   * 验证并格式化URL（与AddWebsiteModal保持一致）
   */
  static validateAndFormatUrl(url) {
    if (!url || !url.trim()) {
      return { isValid: false, formattedUrl: "" };
    }
    let formattedUrl = url.trim();
    if (!formattedUrl.startsWith("http://") && !formattedUrl.startsWith("https://")) {
      formattedUrl = "https://" + formattedUrl;
    }
    try {
      const urlObj = new URL(formattedUrl);
      if (!urlObj.hostname || urlObj.hostname.length === 0) {
        return { isValid: false, formattedUrl: "" };
      }
      const hostnameRegex = /^[a-zA-Z0-9]([a-zA-Z0-9-]{0,61}[a-zA-Z0-9])?(\.[a-zA-Z0-9]([a-zA-Z0-9-]{0,61}[a-zA-Z0-9])?)*$/;
      if (!hostnameRegex.test(urlObj.hostname)) {
        return { isValid: false, formattedUrl: "" };
      }
      return { isValid: true, formattedUrl };
    } catch (error) {
      return { isValid: false, formattedUrl: "" };
    }
  }
  /**
   * 验证URL格式（保持向后兼容）
   */
  static isValidUrl(url) {
    const { isValid } = this.validateAndFormatUrl(url);
    return isValid;
  }
  /**
   * 获取网站favicon
   */
  static async getFavicon(url) {
    try {
      const domain = new URL(url).origin;
      return `${domain}/favicon.ico`;
    } catch {
      return DEFAULT_FAVICON;
    }
  }
  /**
   * 获取网站标题
   */
  static async getWebsiteTitle(url) {
    try {
      const tabs = await chrome.tabs.query({ url });
      if (tabs.length > 0 && tabs[0].title) {
        return tabs[0].title;
      }
      const domain = new URL(url).hostname;
      return domain.replace("www.", "");
    } catch {
      return url;
    }
  }
  /**
   * 创建新工作区
   */
  static async createWorkspace(options) {
    try {
      if (!options.name.trim()) {
        return {
          success: false,
          error: {
            code: ERROR_CODES.INVALID_URL,
            message: "Workspace name cannot be empty"
          }
        };
      }
      const workspacesResult = await StorageManager.getWorkspaces();
      if (!workspacesResult.success) {
        return {
          success: false,
          error: workspacesResult.error
        };
      }
      const existingWorkspaces = workspacesResult.data;
      if (existingWorkspaces.some((w) => w.name === options.name)) {
        return {
          success: false,
          error: {
            code: ERROR_CODES.DUPLICATE_WORKSPACE,
            message: "Workspace with this name already exists"
          }
        };
      }
      const workspaceType = options.type || "saved";
      const workspace = {
        id: this.generateId(workspaceType),
        name: options.name,
        icon: options.icon || WORKSPACE_ICONS[Math.floor(Math.random() * WORKSPACE_ICONS.length)],
        color: options.color || WORKSPACE_COLORS[Math.floor(Math.random() * WORKSPACE_COLORS.length)],
        websites: [],
        createdAt: Date.now(),
        updatedAt: Date.now(),
        isActive: false,
        order: existingWorkspaces.length,
        // Workona 风格扩展字段
        type: workspaceType,
        pos: Date.now(),
        // 使用时间戳作为位置标识符
        state: "inactive",
        workonaTabIds: [],
        sessionId: `session_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
        tabOrder: []
      };
      if (options.websites) {
        for (let i = 0; i < options.websites.length; i++) {
          const siteData = options.websites[i];
          const website = {
            id: this.generateWebsiteId(),
            url: siteData.url,
            title: siteData.title || await this.getWebsiteTitle(siteData.url),
            favicon: siteData.favicon || await this.getFavicon(siteData.url),
            // Workona 风格：移除 isPinned 字段，完全基于 Workona ID 映射管理
            addedAt: Date.now(),
            order: i
          };
          workspace.websites.push(website);
        }
      }
      existingWorkspaces.push(workspace);
      const saveResult = await StorageManager.saveWorkspaces(existingWorkspaces);
      if (!saveResult.success) {
        return {
          success: false,
          error: saveResult.error
        };
      }
      if (options.addCurrentTabs) {
        console.log(`📋 添加当前标签页到新工作区: ${workspace.name}`);
        await this.addCurrentTabsToWorkspace(workspace.id);
      }
      if (options.activate) {
        await StorageManager.setActiveWorkspaceId(workspace.id);
        workspace.isActive = true;
      }
      return { success: true, data: workspace };
    } catch (error) {
      return {
        success: false,
        error: {
          code: ERROR_CODES.STORAGE_ERROR,
          message: "Failed to create workspace",
          details: error
        }
      };
    }
  }
  /**
   * 更新工作区
   */
  static async updateWorkspace(id, options) {
    try {
      const workspacesResult = await StorageManager.getWorkspaces();
      if (!workspacesResult.success) {
        return { success: false, error: workspacesResult.error };
      }
      const workspaces = workspacesResult.data;
      const workspaceIndex = workspaces.findIndex((w) => w.id === id);
      if (workspaceIndex === -1) {
        return {
          success: false,
          error: {
            code: ERROR_CODES.WORKSPACE_NOT_FOUND,
            message: `Workspace with id ${id} not found`
          }
        };
      }
      const workspace = workspaces[workspaceIndex];
      if (options.name !== void 0) workspace.name = options.name;
      if (options.icon !== void 0) workspace.icon = options.icon;
      if (options.color !== void 0) workspace.color = options.color;
      if (options.websites !== void 0) workspace.websites = options.websites;
      if (options.isActive !== void 0) workspace.isActive = options.isActive;
      workspace.updatedAt = Date.now();
      const saveResult = await StorageManager.saveWorkspaces(workspaces);
      if (!saveResult.success) {
        return { success: false, error: saveResult.error };
      }
      return { success: true, data: workspace };
    } catch (error) {
      return {
        success: false,
        error: {
          code: ERROR_CODES.STORAGE_ERROR,
          message: "Failed to update workspace",
          details: error
        }
      };
    }
  }
  /**
   * 删除工作区
   */
  static async deleteWorkspace(id) {
    try {
      const workspacesResult = await StorageManager.getWorkspaces();
      if (!workspacesResult.success) {
        return { success: false, error: workspacesResult.error };
      }
      const workspaces = workspacesResult.data;
      const workspaceIndex = workspaces.findIndex((w) => w.id === id);
      if (workspaceIndex === -1) {
        return {
          success: false,
          error: {
            code: ERROR_CODES.WORKSPACE_NOT_FOUND,
            message: `Workspace with id ${id} not found`
          }
        };
      }
      const closeWindowResult = await WindowManager.closeWorkspaceWindow(id);
      if (!closeWindowResult.success) {
      }
      workspaces.splice(workspaceIndex, 1);
      workspaces.forEach((workspace, index) => {
        workspace.order = index;
      });
      const saveResult = await StorageManager.saveWorkspaces(workspaces);
      if (!saveResult.success) return saveResult;
      const activeIdResult = await StorageManager.getActiveWorkspaceId();
      if (activeIdResult.success && activeIdResult.data === id) {
        await StorageManager.setActiveWorkspaceId(null);
      }
      return { success: true };
    } catch (error) {
      return {
        success: false,
        error: {
          code: ERROR_CODES.STORAGE_ERROR,
          message: "Failed to delete workspace",
          details: error
        }
      };
    }
  }
  /**
   * 添加网站到工作区
   */
  static async addWebsite(workspaceId, url, options = {}) {
    try {
      const { isValid, formattedUrl } = this.validateAndFormatUrl(url);
      if (!isValid) {
        return {
          success: false,
          error: {
            code: ERROR_CODES.INVALID_URL,
            message: "Invalid URL format"
          }
        };
      }
      const finalUrl = formattedUrl;
      const workspacesResult = await StorageManager.getWorkspaces();
      if (!workspacesResult.success) {
        return { success: false, error: workspacesResult.error };
      }
      const workspaces = workspacesResult.data;
      const workspace = workspaces.find((w) => w.id === workspaceId);
      if (!workspace) {
        return {
          success: false,
          error: {
            code: ERROR_CODES.WORKSPACE_NOT_FOUND,
            message: `Workspace with id ${workspaceId} not found`
          }
        };
      }
      if (workspace.websites.some((w) => w.url === finalUrl)) {
        return {
          success: false,
          error: {
            code: ERROR_CODES.DUPLICATE_WEBSITE,
            message: "工作区中已存在使用该网址的网站"
          }
        };
      }
      const websiteTitle = options.title || await this.getWebsiteTitle(finalUrl);
      const website = {
        id: this.generateWebsiteId(),
        url: finalUrl,
        title: websiteTitle,
        favicon: options.favicon || await this.getFavicon(finalUrl),
        // Workona 风格：移除 isPinned 字段，完全基于 Workona ID 映射管理
        addedAt: Date.now(),
        order: workspace.websites.length
      };
      workspace.websites.push(website);
      workspace.updatedAt = Date.now();
      const saveResult = await StorageManager.saveWorkspaces(workspaces);
      if (!saveResult.success) {
        return { success: false, error: saveResult.error };
      }
      await this.promoteExistingTabToWorkspaceCore(workspaceId, website.id, finalUrl);
      if (options.openInNewTab) {
        console.log(`🚀 创建工作区核心标签页: ${finalUrl}`);
        try {
          const tab = await chrome.tabs.create({
            url: finalUrl,
            pinned: false,
            // Workona 风格：不使用固定状态
            active: true
          });
          if (tab.id) {
            const workonaId = WorkonaTabManager.generateWorkonaTabId(workspaceId);
            const mappingResult = await WorkonaTabManager.createTabIdMapping(
              workonaId,
              tab.id,
              workspaceId,
              website.id,
              {
                isWorkspaceCore: true,
                // 概念性重构：标记为核心标签页
                source: "workspace_website"
              }
            );
            if (mappingResult.success) {
              await this.addWorkonaTabId(workspaceId, workonaId);
              console.log(`✅ 成功创建工作区核心标签页: ${workonaId} (网站: ${website.id})`);
            } else {
              console.warn("Workona ID 映射创建失败:", mappingResult.error);
            }
          }
        } catch (error) {
          console.error("创建工作区核心标签页失败:", error);
        }
      }
      return { success: true, data: website };
    } catch (error) {
      return {
        success: false,
        error: {
          code: ERROR_CODES.STORAGE_ERROR,
          message: "Failed to add website",
          details: error
        }
      };
    }
  }
  /**
   * 基于Workona ID血缘关系添加当前标签页到工作区
   * 直接处理指定的标签页ID，避免URL匹配的不准确性
   */
  static async addCurrentTabByWorkonaId(workspaceId, tabId, tabInfo) {
    try {
      console.log(`🎯 基于Workona ID血缘关系添加标签页 ${tabId} 到工作区 ${workspaceId}`);
      const workonaIdResult = await WorkonaTabManager.getWorkonaIdByChromeId(tabId);
      const workspacesResult = await StorageManager.getWorkspaces();
      if (!workspacesResult.success) {
        return { success: false, error: workspacesResult.error };
      }
      const workspaces = workspacesResult.data;
      const workspace = workspaces.find((w) => w.id === workspaceId);
      if (!workspace) {
        return {
          success: false,
          error: {
            code: ERROR_CODES.WORKSPACE_NOT_FOUND,
            message: `Workspace with id ${workspaceId} not found`
          }
        };
      }
      if (workspace.websites.some((w) => w.url === tabInfo.url)) {
        return {
          success: false,
          error: {
            code: ERROR_CODES.DUPLICATE_WEBSITE,
            message: "工作区中已存在使用该网址的网站"
          }
        };
      }
      const website = {
        id: this.generateWebsiteId(),
        url: tabInfo.url,
        title: tabInfo.title || await this.getWebsiteTitle(tabInfo.url),
        favicon: tabInfo.favicon || await this.getFavicon(tabInfo.url),
        addedAt: Date.now(),
        order: workspace.websites.length
      };
      workspace.websites.push(website);
      workspace.updatedAt = Date.now();
      const saveResult = await StorageManager.saveWorkspaces(workspaces);
      if (!saveResult.success) {
        return { success: false, error: saveResult.error };
      }
      if (workonaIdResult.success && workonaIdResult.data) {
        const metadataResult = await WorkonaTabManager.getTabMetadata(workonaIdResult.data);
        if (metadataResult.success && metadataResult.data) {
          const { isWorkspaceCore, tabType } = metadataResult.data;
          if (!isWorkspaceCore && tabType === "session") {
            console.log(`⬆️ 提升会话临时标签页为工作区核心: ${workonaIdResult.data}`);
            const promoteResult = await WorkonaTabManager.promoteToWorkspaceCore(
              workonaIdResult.data,
              website.id
            );
            if (promoteResult.success) {
              console.log(`✅ 成功提升标签页为工作区核心 (网站ID: ${website.id})`);
            } else {
              console.error(`❌ 提升标签页失败:`, promoteResult.error);
            }
          } else if (isWorkspaceCore) {
            console.log(`ℹ️ 标签页已经是工作区核心: ${workonaIdResult.data}`);
          }
        }
      } else {
        console.log(`🆔 为标签页 ${tabId} 创建工作区核心 Workona ID`);
        const newWorkonaId = WorkonaTabManager.generateWorkonaTabId(workspaceId);
        const mappingResult = await WorkonaTabManager.createTabIdMapping(
          newWorkonaId,
          tabId,
          workspaceId,
          website.id,
          {
            isWorkspaceCore: true,
            source: "workspace_website"
          }
        );
        if (mappingResult.success) {
          console.log(`✅ 为标签页创建工作区核心 Workona ID: ${newWorkonaId}`);
        } else {
          console.error(`❌ 创建 Workona ID 失败:`, mappingResult.error);
        }
      }
      try {
        console.log(`🔄 触发用户标签页状态更新 (工作区: ${workspaceId})`);
        await UserTabsRealTimeMonitor.triggerImmediateStateCheck();
        await UserTabsRealTimeMonitor.forceRefreshWorkspaceState(workspaceId);
        WorkspaceStateSync.sendWorkspaceStateUpdate(workspaceId, "userTabsVisibility");
        console.log(`✅ 用户标签页状态更新完成 (工作区: ${workspaceId})`);
      } catch (updateError) {
        console.warn("触发用户标签页状态更新失败:", updateError);
      }
      console.log(`✅ 成功基于Workona ID血缘关系添加标签页到工作区: ${website.title}`);
      return { success: true, data: website };
    } catch (error) {
      console.error("❌ 基于Workona ID血缘关系添加标签页时出错:", error);
      return {
        success: false,
        error: {
          code: ERROR_CODES.STORAGE_ERROR,
          message: "Failed to add current tab by Workona ID",
          details: error
        }
      };
    }
  }
  /**
   * 将现有标签页提升为工作区核心标签页
   * 用于"添加当前标签页"功能，将会话临时标签页转换为工作区核心标签页
   */
  static async promoteExistingTabToWorkspaceCore(workspaceId, websiteId, url) {
    try {
      console.log(`🔄 检查是否有现有标签页需要提升为工作区核心: ${url}`);
      const allTabsResult = await TabManager.getAllTabs();
      if (!allTabsResult.success) {
        console.warn("获取标签页列表失败:", allTabsResult.error);
        return;
      }
      const allTabs = allTabsResult.data;
      const matchingTabs = [];
      for (const tab of allTabs) {
        if (!tab.id) continue;
        const workonaIdResult = await WorkonaTabManager.getWorkonaIdByChromeId(tab.id);
        if (workonaIdResult.success && workonaIdResult.data) {
          const metadataResult = await WorkonaTabManager.getTabMetadata(workonaIdResult.data);
          if (metadataResult.success && metadataResult.data?.workspaceId === workspaceId && metadataResult.data?.websiteId === websiteId) {
            matchingTabs.push(tab);
          }
        }
      }
      if (matchingTabs.length === 0) {
        console.log(`📝 没有找到匹配URL的现有标签页: ${url}`);
        return;
      }
      console.log(`🎯 找到 ${matchingTabs.length} 个匹配的标签页，开始检查和转换...`);
      for (const tab of matchingTabs) {
        try {
          const workonaIdResult = await WorkonaTabManager.getWorkonaIdByChromeId(tab.id);
          if (workonaIdResult.success && workonaIdResult.data) {
            const metadataResult = await WorkonaTabManager.getTabMetadata(workonaIdResult.data);
            if (metadataResult.success && metadataResult.data) {
              const { isWorkspaceCore, tabType } = metadataResult.data;
              if (!isWorkspaceCore && tabType === "session") {
                console.log(`⬆️ 提升会话临时标签页为工作区核心: ${tab.title} (${workonaIdResult.data})`);
                const promoteResult = await WorkonaTabManager.promoteToWorkspaceCore(
                  workonaIdResult.data,
                  websiteId
                );
                if (promoteResult.success) {
                  console.log(`✅ 成功提升标签页: ${tab.title} -> 工作区核心 (网站ID: ${websiteId})`);
                } else {
                  console.error(`❌ 提升标签页失败:`, promoteResult.error);
                }
              } else if (isWorkspaceCore) {
                console.log(`ℹ️ 标签页已经是工作区核心: ${tab.title} (${workonaIdResult.data})`);
              }
            }
          } else {
            console.log(`🆔 为现有标签页创建工作区核心 Workona ID: ${tab.title}`);
            const newWorkonaId = WorkonaTabManager.generateWorkonaTabId(workspaceId);
            const mappingResult = await WorkonaTabManager.createTabIdMapping(
              newWorkonaId,
              tab.id,
              workspaceId,
              websiteId,
              {
                isWorkspaceCore: true,
                source: "workspace_website"
              }
            );
            if (mappingResult.success) {
              console.log(`✅ 为现有标签页创建工作区核心 Workona ID: ${newWorkonaId} (${tab.title})`);
            } else {
              console.error(`❌ 创建 Workona ID 失败:`, mappingResult.error);
            }
          }
        } catch (error) {
          console.error(`❌ 处理标签页 ${tab.id} 时出错:`, error);
        }
      }
      if (matchingTabs.length > 0) {
        try {
          console.log(`🔄 触发用户标签页状态更新 (工作区: ${workspaceId}, 处理了 ${matchingTabs.length} 个标签页)`);
          await UserTabsRealTimeMonitor.triggerImmediateStateCheck();
          await UserTabsRealTimeMonitor.forceRefreshWorkspaceState(workspaceId);
          WorkspaceStateSync.sendWorkspaceStateUpdate(workspaceId, "userTabsVisibility");
          console.log(`✅ 用户标签页状态更新完成 (工作区: ${workspaceId})`);
        } catch (updateError) {
          console.warn("触发用户标签页状态更新失败:", updateError);
        }
      }
    } catch (error) {
      console.error("❌ 提升现有标签页为工作区核心时出错:", error);
    }
  }
  /**
   * 将当前窗口的所有标签页添加到工作区
   */
  static async addCurrentTabsToWorkspace(workspaceId) {
    try {
      console.log(`📋 开始添加当前标签页到工作区: ${workspaceId}`);
      const currentTabsResult = await TabManager.getCurrentWindowTabs();
      if (!currentTabsResult.success) {
        console.error("获取当前窗口标签页失败:", currentTabsResult.error);
        return;
      }
      const currentTabs = currentTabsResult.data;
      console.log(`🔍 找到 ${currentTabs.length} 个当前窗口标签页`);
      const validTabs = currentTabs.filter((tab) => {
        return !tab.url.startsWith("chrome://") && !tab.url.startsWith("chrome-extension://") && !tab.url.startsWith("edge://") && !tab.url.startsWith("about:") && !tab.url.includes("workspace-placeholder.html") && tab.url !== "chrome://newtab/" && tab.url.trim() !== "";
      });
      console.log(`✅ 过滤后有效标签页: ${validTabs.length} 个`);
      if (validTabs.length === 0) {
        console.log("⚠️ 没有有效的标签页可以添加到工作区");
        return;
      }
      let addedCount = 0;
      for (const tab of validTabs) {
        try {
          console.log(`📝 添加标签页到工作区: ${tab.title} (${tab.url})`);
          const addResult = await this.addWebsite(workspaceId, tab.url, {
            title: tab.title,
            favicon: tab.favicon,
            openInNewTab: false
            // 不创建新标签页，使用现有的
          });
          if (addResult.success) {
            addedCount++;
            console.log(`✅ 成功添加: ${tab.title}`);
          } else {
            console.warn(`⚠️ 添加失败: ${tab.title} - ${addResult.error?.message}`);
          }
        } catch (error) {
          console.error(`❌ 添加标签页时出错: ${tab.title}`, error);
        }
      }
      console.log(`🎉 成功添加 ${addedCount}/${validTabs.length} 个标签页到工作区 ${workspaceId}`);
    } catch (error) {
      console.error("❌ 添加当前标签页到工作区时出错:", error);
    }
  }
  /**
   * 从工作区移除网站
   */
  static async removeWebsite(workspaceId, websiteId) {
    try {
      const workspacesResult = await StorageManager.getWorkspaces();
      if (!workspacesResult.success) {
        return { success: false, error: workspacesResult.error };
      }
      const workspaces = workspacesResult.data;
      const workspace = workspaces.find((w) => w.id === workspaceId);
      if (!workspace) {
        return {
          success: false,
          error: {
            code: ERROR_CODES.WORKSPACE_NOT_FOUND,
            message: `Workspace with id ${workspaceId} not found`
          }
        };
      }
      const websiteIndex = workspace.websites.findIndex((w) => w.id === websiteId);
      if (websiteIndex === -1) {
        return {
          success: false,
          error: {
            code: ERROR_CODES.WEBSITE_NOT_FOUND,
            message: `Website with id ${websiteId} not found`
          }
        };
      }
      const removedWebsite = workspace.websites[websiteIndex];
      console.log(`🔄 处理网站 "${removedWebsite.title}" 相关的工作区专属标签页降级`);
      try {
        const mappingsResult = await StorageManager.getTabIdMappings();
        if (mappingsResult.success) {
          const mappings = mappingsResult.data;
          const relatedMappings = mappings.filter(
            (mapping) => mapping.workspaceId === workspaceId && mapping.websiteId === websiteId && mapping.isWorkspaceCore
          );
          console.log(`🔍 找到 ${relatedMappings.length} 个与网站 "${removedWebsite.title}" 关联的工作区专属标签页`);
          for (const mapping of relatedMappings) {
            const demoteResult = await WorkonaTabManager.demoteToSessionTab(mapping.workonaId);
            if (demoteResult.success) {
              console.log(`⬇️ 成功降级标签页: ${mapping.workonaId} (网站: ${removedWebsite.title})`);
            } else {
              console.warn(`⚠️ 降级标签页失败: ${mapping.workonaId}`, demoteResult.error);
            }
          }
        }
      } catch (error) {
        console.warn("处理工作区专属标签页降级时出错:", error);
      }
      workspace.websites.splice(websiteIndex, 1);
      workspace.websites.forEach((website, index) => {
        website.order = index;
      });
      workspace.updatedAt = Date.now();
      const saveResult = await StorageManager.saveWorkspaces(workspaces);
      if (!saveResult.success) return saveResult;
      console.log(`✅ 成功移除网站 "${removedWebsite.title}" 并处理相关标签页降级`);
      return { success: true };
    } catch (error) {
      return {
        success: false,
        error: {
          code: ERROR_CODES.STORAGE_ERROR,
          message: "Failed to remove website",
          details: error
        }
      };
    }
  }
  /**
   * 更新工作区中的网站
   */
  static async updateWebsite(workspaceId, websiteId, updates) {
    try {
      const workspacesResult = await StorageManager.getWorkspaces();
      if (!workspacesResult.success) {
        return {
          success: false,
          error: workspacesResult.error
        };
      }
      const workspaces = workspacesResult.data;
      const workspace = workspaces.find((w) => w.id === workspaceId);
      if (!workspace) {
        return {
          success: false,
          error: {
            code: ERROR_CODES.WORKSPACE_NOT_FOUND,
            message: `Workspace with id ${workspaceId} not found`
          }
        };
      }
      const website = workspace.websites.find((w) => w.id === websiteId);
      if (!website) {
        return {
          success: false,
          error: {
            code: ERROR_CODES.WEBSITE_NOT_FOUND,
            message: `Website with id ${websiteId} not found`
          }
        };
      }
      if (updates.url !== void 0) {
        if (!this.isValidUrl(updates.url)) {
          return {
            success: false,
            error: {
              code: ERROR_CODES.INVALID_URL,
              message: "Invalid URL format"
            }
          };
        }
        website.url = updates.url;
        website.favicon = await this.getFavicon(updates.url);
      }
      if (updates.title !== void 0) {
        website.title = updates.title;
      }
      if (updates.isPinned !== void 0) {
        website.isPinned = updates.isPinned;
      }
      workspace.updatedAt = Date.now();
      const saveResult = await StorageManager.saveWorkspaces(workspaces);
      if (!saveResult.success) {
        return {
          success: false,
          error: saveResult.error
        };
      }
      return { success: true, data: website };
    } catch (error) {
      return {
        success: false,
        error: {
          code: ERROR_CODES.STORAGE_ERROR,
          message: "Failed to update website",
          details: error
        }
      };
    }
  }
  /**
   * 重新排序工作区
   */
  static async reorderWorkspaces(workspaceIds) {
    try {
      const workspacesResult = await StorageManager.getWorkspaces();
      if (!workspacesResult.success) {
        return { success: false, error: workspacesResult.error };
      }
      const workspaces = workspacesResult.data;
      const reorderedWorkspaces = [];
      workspaceIds.forEach((id, index) => {
        const workspace = workspaces.find((w) => w.id === id);
        if (workspace) {
          workspace.order = index;
          reorderedWorkspaces.push(workspace);
        }
      });
      const saveResult = await StorageManager.saveWorkspaces(reorderedWorkspaces);
      if (!saveResult.success) return saveResult;
      return { success: true };
    } catch (error) {
      return {
        success: false,
        error: {
          code: ERROR_CODES.STORAGE_ERROR,
          message: "Failed to reorder workspaces",
          details: error
        }
      };
    }
  }
  /**
   * 重新排序工作区内的网站
   */
  static async reorderWebsites(workspaceId, websiteIds) {
    try {
      const workspacesResult = await StorageManager.getWorkspaces();
      if (!workspacesResult.success) {
        return { success: false, error: workspacesResult.error };
      }
      const workspaces = workspacesResult.data;
      const workspace = workspaces.find((w) => w.id === workspaceId);
      if (!workspace) {
        return {
          success: false,
          error: {
            code: ERROR_CODES.WORKSPACE_NOT_FOUND,
            message: `Workspace with id ${workspaceId} not found`
          }
        };
      }
      const reorderedWebsites = [];
      websiteIds.forEach((id, index) => {
        const website = workspace.websites.find((w) => w.id === id);
        if (website) {
          website.order = index;
          reorderedWebsites.push(website);
        }
      });
      workspace.websites = reorderedWebsites;
      workspace.updatedAt = Date.now();
      const saveResult = await StorageManager.saveWorkspaces(workspaces);
      if (!saveResult.success) return saveResult;
      return { success: true };
    } catch (error) {
      return {
        success: false,
        error: {
          code: ERROR_CODES.STORAGE_ERROR,
          message: "Failed to reorder websites",
          details: error
        }
      };
    }
  }
  /**
   * 设置工作区用户标签页隐藏状态
   */
  static async setUserTabsHiddenState(workspaceId, isHidden, hiddenTabIds) {
    try {
      const workspacesResult = await StorageManager.getWorkspaces();
      if (!workspacesResult.success) {
        return {
          success: false,
          error: workspacesResult.error
        };
      }
      const workspaces = workspacesResult.data;
      const workspace = workspaces.find((w) => w.id === workspaceId);
      if (!workspace) {
        return {
          success: false,
          error: {
            code: ERROR_CODES.WORKSPACE_NOT_FOUND,
            message: "Workspace not found"
          }
        };
      }
      workspace.userTabsHidden = isHidden;
      workspace.hiddenUserTabIds = hiddenTabIds || [];
      workspace.updatedAt = Date.now();
      const saveResult = await StorageManager.saveWorkspaces(workspaces);
      if (!saveResult.success) return saveResult;
      return { success: true };
    } catch (error) {
      return {
        success: false,
        error: {
          code: ERROR_CODES.STORAGE_ERROR,
          message: "Failed to set user tabs hidden state",
          details: error
        }
      };
    }
  }
  /**
   * 获取工作区用户标签页隐藏状态
   */
  static async getUserTabsHiddenState(workspaceId) {
    try {
      const workspaceResult = await StorageManager.getWorkspace(workspaceId);
      if (!workspaceResult.success) {
        return {
          success: false,
          error: workspaceResult.error
        };
      }
      const workspace = workspaceResult.data;
      return {
        success: true,
        data: {
          isHidden: workspace.userTabsHidden || false,
          hiddenTabIds: workspace.hiddenUserTabIds || []
        }
      };
    } catch (error) {
      return {
        success: false,
        error: {
          code: ERROR_CODES.STORAGE_ERROR,
          message: "Failed to get user tabs hidden state",
          details: error
        }
      };
    }
  }
  /**
   * 清除工作区的隐藏标签页记录（当标签页被永久删除时调用）
   */
  static async clearHiddenTabIds(workspaceId, tabIdsToRemove) {
    try {
      const workspacesResult = await StorageManager.getWorkspaces();
      if (!workspacesResult.success) {
        return {
          success: false,
          error: workspacesResult.error
        };
      }
      const workspaces = workspacesResult.data;
      const workspace = workspaces.find((w) => w.id === workspaceId);
      if (!workspace) {
        return {
          success: false,
          error: {
            code: ERROR_CODES.WORKSPACE_NOT_FOUND,
            message: "Workspace not found"
          }
        };
      }
      if (workspace.hiddenUserTabIds) {
        workspace.hiddenUserTabIds = workspace.hiddenUserTabIds.filter(
          (id) => !tabIdsToRemove.includes(id)
        );
        workspace.updatedAt = Date.now();
        console.log(`从工作区 "${workspace.name}" 的隐藏列表中移除标签页ID: ${tabIdsToRemove.join(", ")}`);
        const saveResult = await StorageManager.saveWorkspaces(workspaces);
        if (!saveResult.success) return saveResult;
      }
      return { success: true };
    } catch (error) {
      return {
        success: false,
        error: {
          code: ERROR_CODES.STORAGE_ERROR,
          message: "Failed to clear hidden tab IDs",
          details: error
        }
      };
    }
  }
  // ===== Workona 风格工作区管理方法 =====
  /**
   * 更新工作区类型（Workona 风格）
   */
  static async updateWorkspaceType(workspaceId, newType) {
    try {
      const workspacesResult = await StorageManager.getWorkspaces();
      if (!workspacesResult.success) {
        return { success: false, error: workspacesResult.error };
      }
      const workspaces = workspacesResult.data;
      const workspace = workspaces.find((w) => w.id === workspaceId);
      if (!workspace) {
        return {
          success: false,
          error: {
            code: ERROR_CODES.WORKSPACE_NOT_FOUND,
            message: "Workspace not found"
          }
        };
      }
      workspace.type = newType;
      workspace.updatedAt = Date.now();
      if (workspace.type === "temp" && newType === "saved") {
        const newId = this.generateId("saved");
        const clearResult = await WorkonaTabManager.clearWorkspaceTabMappings(workspaceId);
        if (clearResult.success) {
          console.log(`清理了 ${clearResult.data} 个旧的标签页映射`);
        }
        workspace.id = newId;
        console.log(`工作区类型转换: ${workspaceId} -> ${newId} (${newType})`);
      }
      const saveResult = await StorageManager.saveWorkspaces(workspaces);
      if (!saveResult.success) {
        return { success: false, error: saveResult.error };
      }
      console.log(`✅ 工作区类型已更新: ${workspace.name} -> ${newType}`);
      return { success: true };
    } catch (error) {
      return {
        success: false,
        error: {
          code: ERROR_CODES.STORAGE_ERROR,
          message: "Failed to update workspace type",
          details: error
        }
      };
    }
  }
  /**
   * 更新工作区位置标识符（Workona 风格排序）
   */
  static async updateWorkspacePosition(workspaceId, newPosition) {
    try {
      const workspacesResult = await StorageManager.getWorkspaces();
      if (!workspacesResult.success) {
        return { success: false, error: workspacesResult.error };
      }
      const workspaces = workspacesResult.data;
      const workspace = workspaces.find((w) => w.id === workspaceId);
      if (!workspace) {
        return {
          success: false,
          error: {
            code: ERROR_CODES.WORKSPACE_NOT_FOUND,
            message: "Workspace not found"
          }
        };
      }
      workspace.pos = newPosition;
      workspace.updatedAt = Date.now();
      const saveResult = await StorageManager.saveWorkspaces(workspaces);
      if (!saveResult.success) {
        return { success: false, error: saveResult.error };
      }
      console.log(`📍 工作区位置已更新: ${workspace.name} -> ${newPosition}`);
      return { success: true };
    } catch (error) {
      return {
        success: false,
        error: {
          code: ERROR_CODES.STORAGE_ERROR,
          message: "Failed to update workspace position",
          details: error
        }
      };
    }
  }
  /**
   * 更新工作区状态（Workona 风格）
   */
  static async updateWorkspaceState(workspaceId, newState) {
    try {
      const workspacesResult = await StorageManager.getWorkspaces();
      if (!workspacesResult.success) {
        return { success: false, error: workspacesResult.error };
      }
      const workspaces = workspacesResult.data;
      const workspace = workspaces.find((w) => w.id === workspaceId);
      if (!workspace) {
        return {
          success: false,
          error: {
            code: ERROR_CODES.WORKSPACE_NOT_FOUND,
            message: "Workspace not found"
          }
        };
      }
      workspace.state = newState;
      workspace.updatedAt = Date.now();
      const saveResult = await StorageManager.saveWorkspaces(workspaces);
      if (!saveResult.success) {
        return { success: false, error: saveResult.error };
      }
      console.log(`🔄 工作区状态已更新: ${workspace.name} -> ${newState}`);
      return { success: true };
    } catch (error) {
      return {
        success: false,
        error: {
          code: ERROR_CODES.STORAGE_ERROR,
          message: "Failed to update workspace state",
          details: error
        }
      };
    }
  }
  /**
   * 添加 Workona 标签页ID到工作区
   */
  static async addWorkonaTabId(workspaceId, workonaTabId) {
    try {
      const workspacesResult = await StorageManager.getWorkspaces();
      if (!workspacesResult.success) {
        return { success: false, error: workspacesResult.error };
      }
      const workspaces = workspacesResult.data;
      const workspace = workspaces.find((w) => w.id === workspaceId);
      if (!workspace) {
        return {
          success: false,
          error: {
            code: ERROR_CODES.WORKSPACE_NOT_FOUND,
            message: "Workspace not found"
          }
        };
      }
      if (!workspace.workonaTabIds) {
        workspace.workonaTabIds = [];
      }
      if (!workspace.workonaTabIds.includes(workonaTabId)) {
        workspace.workonaTabIds.push(workonaTabId);
        workspace.updatedAt = Date.now();
        const saveResult = await StorageManager.saveWorkspaces(workspaces);
        if (!saveResult.success) {
          return { success: false, error: saveResult.error };
        }
        console.log(`➕ 添加 Workona 标签页ID: ${workonaTabId} -> ${workspace.name}`);
      }
      return { success: true };
    } catch (error) {
      return {
        success: false,
        error: {
          code: ERROR_CODES.STORAGE_ERROR,
          message: "Failed to add Workona tab ID",
          details: error
        }
      };
    }
  }
  /**
   * 从工作区移除 Workona 标签页ID
   */
  static async removeWorkonaTabId(workspaceId, workonaTabId) {
    try {
      const workspacesResult = await StorageManager.getWorkspaces();
      if (!workspacesResult.success) {
        return { success: false, error: workspacesResult.error };
      }
      const workspaces = workspacesResult.data;
      const workspace = workspaces.find((w) => w.id === workspaceId);
      if (!workspace || !workspace.workonaTabIds) {
        return { success: true };
      }
      const initialLength = workspace.workonaTabIds.length;
      workspace.workonaTabIds = workspace.workonaTabIds.filter((id) => id !== workonaTabId);
      if (workspace.workonaTabIds.length !== initialLength) {
        workspace.updatedAt = Date.now();
        const saveResult = await StorageManager.saveWorkspaces(workspaces);
        if (!saveResult.success) {
          return { success: false, error: saveResult.error };
        }
        console.log(`➖ 移除 Workona 标签页ID: ${workonaTabId} <- ${workspace.name}`);
      }
      return { success: true };
    } catch (error) {
      return {
        success: false,
        error: {
          code: ERROR_CODES.STORAGE_ERROR,
          message: "Failed to remove Workona tab ID",
          details: error
        }
      };
    }
  }
  /**
   * 清理临时工作区（生命周期管理）
   */
  static async cleanupTemporaryWorkspaces(maxAge = 24 * 60 * 60 * 1e3) {
    try {
      console.log("🧹 开始清理临时工作区...");
      const workspacesResult = await StorageManager.getWorkspaces();
      if (!workspacesResult.success) {
        return { success: false, error: workspacesResult.error };
      }
      const workspaces = workspacesResult.data;
      const now = Date.now();
      let cleanedCount = 0;
      const workspacesToKeep = workspaces.filter((workspace) => {
        if (workspace.type === "temp") {
          const age = now - workspace.createdAt;
          if (age > maxAge) {
            console.log(`🗑️ 清理过期临时工作区: ${workspace.name} (${Math.round(age / (60 * 60 * 1e3))}小时前创建)`);
            cleanedCount++;
            return false;
          }
        }
        return true;
      });
      if (cleanedCount > 0) {
        const saveResult = await StorageManager.saveWorkspaces(workspacesToKeep);
        if (!saveResult.success) {
          return { success: false, error: saveResult.error };
        }
      }
      console.log(`✅ 临时工作区清理完成，清理了 ${cleanedCount} 个过期工作区`);
      return { success: true, data: cleanedCount };
    } catch (error) {
      return {
        success: false,
        error: {
          code: ERROR_CODES.STORAGE_ERROR,
          message: "Failed to cleanup temporary workspaces",
          details: error
        }
      };
    }
  }
}

class MigrationManager {
  static CURRENT_VERSION = "1.0.0";
  static BACKUP_KEY = "migrationBackup";
  /**
   * 检测当前数据版本
   */
  static async detectDataVersion() {
    try {
      const versionResult = await StorageManager.getDataVersion();
      if (!versionResult.success) {
        return { success: true, data: "0.0.0" };
      }
      return { success: true, data: versionResult.data };
    } catch (error) {
      return {
        success: false,
        error: {
          code: ERROR_CODES.STORAGE_ERROR,
          message: "Failed to detect data version",
          details: error
        }
      };
    }
  }
  /**
   * 执行 Workona 格式迁移
   */
  static async migrateToWorkonaFormat(options = {}) {
    try {
      console.log("🚀 开始 Workona 格式数据迁移...");
      const versionResult = await this.detectDataVersion();
      if (!versionResult.success) {
        return { success: false, error: versionResult.error };
      }
      const currentVersion = versionResult.data;
      if (currentVersion === this.CURRENT_VERSION) {
        console.log("✅ 数据已是最新版本，无需迁移");
        return { success: true, data: false };
      }
      if (options.backupOriginalData !== false) {
        const backupResult = await this.backupOriginalData();
        if (!backupResult.success) {
          console.error("❌ 数据备份失败，中止迁移");
          return { success: false, error: backupResult.error };
        }
        console.log("💾 原始数据备份完成");
      }
      const allDataResult = await StorageManager.getAllData();
      if (!allDataResult.success) {
        return { success: false, error: allDataResult.error };
      }
      const data = allDataResult.data;
      console.log(`📊 检测到 ${data.workspaces.length} 个工作区需要迁移`);
      const migratedWorkspaces = await this.migrateWorkspaces(data.workspaces);
      if (!migratedWorkspaces.success) {
        if (options.rollbackOnError !== false) {
          await this.rollbackMigration();
        }
        return { success: false, error: migratedWorkspaces.error };
      }
      const initResult = await this.initializeWorkonaData();
      if (!initResult.success) {
        if (options.rollbackOnError !== false) {
          await this.rollbackMigration();
        }
        return { success: false, error: initResult.error };
      }
      const versionUpdateResult = await StorageManager.saveDataVersion(this.CURRENT_VERSION);
      if (!versionUpdateResult.success) {
        if (options.rollbackOnError !== false) {
          await this.rollbackMigration();
        }
        return { success: false, error: versionUpdateResult.error };
      }
      if (options.validateAfterMigration !== false) {
        const validationResult = await this.validateMigration();
        if (!validationResult.success) {
          console.error("❌ 迁移验证失败");
          if (options.rollbackOnError !== false) {
            await this.rollbackMigration();
          }
          return { success: false, error: validationResult.error };
        }
        console.log("✅ 迁移验证通过");
      }
      console.log("🎉 Workona 格式迁移完成！");
      return { success: true, data: true };
    } catch (error) {
      console.error("❌ 迁移过程中发生错误:", error);
      if (options.rollbackOnError !== false) {
        await this.rollbackMigration();
      }
      return {
        success: false,
        error: {
          code: ERROR_CODES.STORAGE_ERROR,
          message: "Migration failed",
          details: error
        }
      };
    }
  }
  /**
   * 迁移工作区数据
   */
  static async migrateWorkspaces(workspaces) {
    try {
      const migratedWorkspaces = [];
      for (const workspace of workspaces) {
        const migratedWorkspace = {
          ...workspace,
          // 添加 Workona 风格字段（如果不存在）
          type: workspace.type || "saved",
          pos: workspace.pos || workspace.createdAt,
          state: workspace.state || (workspace.isActive ? "active" : "inactive"),
          workonaTabIds: workspace.workonaTabIds || [],
          sessionId: workspace.sessionId || `session_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
          tabOrder: workspace.tabOrder || []
        };
        migratedWorkspaces.push(migratedWorkspace);
        console.log(`✨ 迁移工作区: ${workspace.name} -> Workona 格式`);
      }
      const saveResult = await StorageManager.saveWorkspaces(migratedWorkspaces);
      if (!saveResult.success) {
        return { success: false, error: saveResult.error };
      }
      return { success: true, data: migratedWorkspaces };
    } catch (error) {
      return {
        success: false,
        error: {
          code: ERROR_CODES.STORAGE_ERROR,
          message: "Failed to migrate workspaces",
          details: error
        }
      };
    }
  }
  /**
   * 初始化 Workona 数据结构
   */
  static async initializeWorkonaData() {
    try {
      await StorageManager.saveTabIdMappings([]);
      await StorageManager.saveLocalOpenWorkspaces({});
      await StorageManager.saveTabGroups({});
      await StorageManager.saveWorkspaceSessions({});
      console.log("🏗️ Workona 数据结构初始化完成");
      return { success: true };
    } catch (error) {
      return {
        success: false,
        error: {
          code: ERROR_CODES.STORAGE_ERROR,
          message: "Failed to initialize Workona data",
          details: error
        }
      };
    }
  }
  /**
   * 备份原始数据
   */
  static async backupOriginalData() {
    try {
      const allDataResult = await StorageManager.getAllData();
      if (!allDataResult.success) {
        return { success: false, error: allDataResult.error };
      }
      const backupData = {
        ...allDataResult.data,
        backupTimestamp: Date.now(),
        backupVersion: await this.detectDataVersion()
      };
      await chrome.storage.local.set({
        [this.BACKUP_KEY]: backupData
      });
      return { success: true };
    } catch (error) {
      return {
        success: false,
        error: {
          code: ERROR_CODES.STORAGE_ERROR,
          message: "Failed to backup original data",
          details: error
        }
      };
    }
  }
  /**
   * 回滚迁移
   */
  static async rollbackMigration() {
    try {
      console.log("🔄 开始回滚迁移...");
      const result = await chrome.storage.local.get([this.BACKUP_KEY]);
      const backupData = result[this.BACKUP_KEY];
      if (!backupData) {
        return {
          success: false,
          error: {
            code: ERROR_CODES.STORAGE_ERROR,
            message: "No backup data found for rollback"
          }
        };
      }
      await StorageManager.saveWorkspaces(backupData.workspaces);
      await StorageManager.saveSettings(backupData.settings);
      await StorageManager.setActiveWorkspaceId(backupData.activeWorkspaceId);
      await chrome.storage.local.remove([
        WORKONA_STORAGE_KEYS.TAB_ID_MAPPINGS,
        WORKONA_STORAGE_KEYS.LOCAL_OPEN_WORKSPACES,
        WORKONA_STORAGE_KEYS.TAB_GROUPS,
        WORKONA_STORAGE_KEYS.WORKSPACE_SESSIONS,
        WORKONA_STORAGE_KEYS.DATA_VERSION
      ]);
      console.log("✅ 迁移回滚完成");
      return { success: true };
    } catch (error) {
      return {
        success: false,
        error: {
          code: ERROR_CODES.STORAGE_ERROR,
          message: "Failed to rollback migration",
          details: error
        }
      };
    }
  }
  /**
   * 验证迁移结果
   */
  static async validateMigration() {
    try {
      const versionResult = await StorageManager.getDataVersion();
      if (!versionResult.success || versionResult.data !== this.CURRENT_VERSION) {
        throw new Error("Data version validation failed");
      }
      const workspacesResult = await StorageManager.getWorkspaces();
      if (!workspacesResult.success) {
        throw new Error("Workspaces validation failed");
      }
      const workspaces = workspacesResult.data;
      for (const workspace of workspaces) {
        if (!workspace.type || !workspace.pos || !workspace.state) {
          throw new Error(`Workspace ${workspace.name} missing Workona fields`);
        }
      }
      const mappingsResult = await StorageManager.getTabIdMappings();
      const sessionsResult = await StorageManager.getWorkspaceSessions();
      if (!mappingsResult.success || !sessionsResult.success) {
        throw new Error("Workona data structures validation failed");
      }
      return { success: true };
    } catch (error) {
      return {
        success: false,
        error: {
          code: ERROR_CODES.STORAGE_ERROR,
          message: "Migration validation failed",
          details: error
        }
      };
    }
  }
  /**
   * 清理备份数据
   */
  static async cleanupBackup() {
    try {
      await chrome.storage.local.remove([this.BACKUP_KEY]);
      console.log("🧹 迁移备份数据已清理");
      return { success: true };
    } catch (error) {
      return {
        success: false,
        error: {
          code: ERROR_CODES.STORAGE_ERROR,
          message: "Failed to cleanup backup",
          details: error
        }
      };
    }
  }
  // === 概念性重构：标签页元数据迁移方法 ===
  /**
   * 迁移现有 Workona ID 映射的元数据（概念性重构）
   */
  static async migrateTabMappingsMetadata() {
    try {
      console.log("🔄 迁移现有 Workona ID 映射的元数据...");
      const mappingsResult = await StorageManager.getTabIdMappings();
      if (!mappingsResult.success) {
        console.log("ℹ️ 没有现有的 Workona ID 映射需要迁移");
        return { success: true, data: 0 };
      }
      const mappings = mappingsResult.data;
      let migratedCount = 0;
      for (const mapping of mappings) {
        if (mapping.hasOwnProperty("isWorkspaceCore") && mapping.hasOwnProperty("tabType")) {
          continue;
        }
        const isWorkspaceCore = !!mapping.websiteId;
        const tabType = isWorkspaceCore ? "core" : "session";
        const source = isWorkspaceCore ? "workspace_website" : "user_opened";
        const updatedMapping = {
          ...mapping,
          isWorkspaceCore,
          tabType,
          metadata: {
            source,
            addedToWorkspaceAt: isWorkspaceCore ? mapping.createdAt : void 0
          }
        };
        const index = mappings.findIndex((m) => m.workonaId === mapping.workonaId);
        if (index >= 0) {
          mappings[index] = updatedMapping;
          migratedCount++;
        }
      }
      if (migratedCount > 0) {
        const saveResult = await StorageManager.saveTabIdMappings(mappings);
        if (saveResult.success) {
          console.log(`✅ 成功迁移 ${migratedCount} 个 Workona ID 映射的元数据`);
          return { success: true, data: migratedCount };
        } else {
          throw new Error("保存迁移后的映射失败");
        }
      } else {
        console.log("ℹ️ 所有 Workona ID 映射已包含最新元数据");
        return { success: true, data: 0 };
      }
    } catch (error) {
      console.error("❌ 迁移 Workona ID 映射元数据失败:", error);
      return {
        success: false,
        error: {
          code: ERROR_CODES.STORAGE_ERROR,
          message: "Failed to migrate tab mappings metadata",
          details: error
        }
      };
    }
  }
}

export { COMMANDS as C, ERROR_CODES as E, MigrationManager as M, StorageManager as S, TabManager as T, UserTabsRealTimeMonitor as U, WorkspaceManager as W, WorkspaceSwitcher as a, WorkonaTabManager as b, WORKSPACE_ICONS as c, WORKSPACE_COLORS as d, WorkspaceUserTabsVisibilityManager as e, WorkspaceStateSync as f, WorkspaceSessionManager as g, STORAGE_KEYS as h, tabs as t, workspaceSessionManager as w };
