#!/usr/bin/env node

/**
 * 图标生成脚本
 * 将SVG图标转换为不同尺寸的PNG文件
 */

import fs from 'fs';
import path from 'path';
import { fileURLToPath } from 'url';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

// 检查是否安装了sharp
let sharp;
try {
  const sharpModule = await import('sharp');
  sharp = sharpModule.default;
} catch (error) {
  console.error('❌ 需要安装sharp库来生成PNG图标');
  console.log('请运行: npm install sharp --save-dev');
  process.exit(1);
}

const ICON_SIZES = [16, 32, 48, 128];
const SVG_PATH = path.join(__dirname, '../public/icons/icon.svg');
const ICONS_DIR = path.join(__dirname, '../public/icons');

async function generateIcons() {
  try {
    console.log('🎨 开始生成图标...');

    // 检查SVG文件是否存在
    if (!fs.existsSync(SVG_PATH)) {
      throw new Error(`SVG文件不存在: ${SVG_PATH}`);
    }

    // 读取SVG内容
    const svgBuffer = fs.readFileSync(SVG_PATH);
    console.log('✅ 读取SVG文件成功');

    // 为每个尺寸生成PNG
    for (const size of ICON_SIZES) {
      const outputPath = path.join(ICONS_DIR, `icon${size}.png`);
      
      await sharp(svgBuffer)
        .resize(size, size)
        .png()
        .toFile(outputPath);
      
      console.log(`✅ 生成 ${size}x${size} 图标: ${outputPath}`);
    }

    console.log('🎉 所有图标生成完成！');
    
    // 显示生成的文件信息
    console.log('\n📁 生成的图标文件:');
    for (const size of ICON_SIZES) {
      const filePath = path.join(ICONS_DIR, `icon${size}.png`);
      const stats = fs.statSync(filePath);
      console.log(`  icon${size}.png - ${(stats.size / 1024).toFixed(1)}KB`);
    }

  } catch (error) {
    console.error('❌ 生成图标失败:', error.message);
    process.exit(1);
  }
}

// 如果直接运行此脚本
if (import.meta.url === `file://${process.argv[1]}`) {
  generateIcons();
}

export { generateIcons };
